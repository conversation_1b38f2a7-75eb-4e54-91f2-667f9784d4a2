{"name": "wansh/apps", "type": "project", "keywords": ["php", "swoole"], "description": "wansh swoole apps", "license": "Apache-2.0", "require": {"php": ">=8.4", "ext-bcmath": "*", "ext-curl": "*", "ext-openssl": "*", "ext-redis": "*", "ext-pdo": "*", "ext-mysqli": "*", "league/csv": "^9.16", "ext-mbstring": "*", "twig/twig": "^3.14", "ext-zlib": "*", "ext-xlswriter": "*", "yansongda/pay": "^3.7", "guzzlehttp/guzzle": "^7.0", "hyperf/pimple": "~2.2.0", "alibabacloud/dysmsapi-20170525": "^3.1", "ext-dom": "*", "ext-libxml": "*", "overtrue/pinyin": "^5.3", "ext-posix": "*", "alibabacloud/dyvmsapi-20170525": "3.2.2", "alibabacloud/dyplsapi-20170525": "2.0.1", "ext-simplexml": "*"}, "require-dev": {"google/protobuf": "^4.27", "swoole/ide-helper": "6.0.0-rc1"}, "suggest": {"ext-json": "Required to use JSON.", "ext-pdo_mysql": "Required to use MySQL Client."}, "autoload": {"psr-4": {"App\\": "App/", "Generate\\": "runtime/Generate", "Swlib\\": "Swlib/", "GPBMetadata\\": "runtime/Protobuf/GPBMetadata/", "Protobuf\\": "runtime/Protobuf/Protobuf/"}}}