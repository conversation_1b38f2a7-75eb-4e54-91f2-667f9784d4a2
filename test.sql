CREATE TABLE `order` (
                         `id` int NOT NULL AUTO_INCREMENT COMMENT 'protobuf:ext:json:[\r\n  "item:shops:repeated Protobuf.Datas.ShopOrder.ShopOrderProto",\r\n  "item:address:Protobuf.Datas.Address.AddressProto"\r\n]',
                         `sn` varchar(255) NOT NULL COMMENT '订单号',
                         `original_price` decimal(10,2) DEFAULT NULL COMMENT '订单价格，优惠前的价格',
                         `price` decimal(10,2) NOT NULL COMMENT '订单价格,实际付款金额',
                         `user_id` int NOT NULL,
                         `time` int DEFAULT NULL COMMENT '订单时间',
                         `refund_time` int unsigned DEFAULT '0' COMMENT '退款申请时间',
                         `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
                         `refund_handle_time` int unsigned DEFAULT '0' COMMENT '退款处理时间，需要处理完成后，才发起退款',
                         `refund_complete_time` int unsigned DEFAULT NULL COMMENT '退款完成时间',
                         `refund_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '退款（支付宝/微信...）流水号',
                         `refund_status` tinyint DEFAULT '0' COMMENT '0:未发起退款 \r\n1：用户申请退款\r\n2：审核通过，等待发起退款\r\n3：审核不通过，\r\n4: 审核通过 已经发起退款，尚未返回成功,需要调用查询，是否退款成功 \r\n5: 审核通过，完成退款\r\n这里是用户申请退款以后，管理员审核通过后的状态\r\n',
                         `refund_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '退款审核失败的提示信息',
                         `complete_time` int unsigned DEFAULT '0' COMMENT '订单完成时间，确认收货时间',
                         `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单备注',
                         `pay_type` int DEFAULT NULL COMMENT '支付方式 1：支付宝 ； 2：微信',
                         `pay_time` int unsigned DEFAULT NULL COMMENT '支付时间',
                         `pay_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '支付流水号',
                         `addr_id` int DEFAULT NULL COMMENT '收货地址',
                         `notes` varchar(255) DEFAULT '' COMMENT '备注',
                         `status` enum('wait_pay','wait_delivery','wait_receive','refund','cancel','timeout','refund_finish','finish','partial_shipment','partial_receipt','partial_refund') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'wait_pay' COMMENT '订单状态\r\nwait_pay:待付款\r\nwait_delivery:待发货\r\nwait_receive:待收货\r\nrefund:退换货\r\ncancel:已取消\r\ntimeout:已超时\r\nrefund_finish:退货完成\r\nfinish:订单完成\r\npartial_shipment:部分发货\r\npartial_receipt:部分收货\r\npartial_refund:部分退款',
                         PRIMARY KEY (`id`),
                         KEY `IDX_F5299398A76ED395` (`user_id`),
                         KEY `sn` (`sn`),
                         KEY `addr_id` (`addr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=117 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `order_logistics` (
                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'protobuf:ext:json:[\r\n"item:companiesService:repeated Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto"\r\n]',
                                   `business_id` int unsigned DEFAULT NULL COMMENT '商家',
                                   `sn` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
                                   `order_id` int unsigned DEFAULT NULL COMMENT '订单ID',
                                   `product_id` int unsigned DEFAULT NULL COMMENT '产品ID',
                                   `sku_id` int unsigned DEFAULT NULL COMMENT '规格ID',
                                   `user_id` int unsigned DEFAULT NULL COMMENT '用户ID',
                                   `news` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最新一条动态',
                                   `logistics_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物流单号',
                                   `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                   `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `number` int unsigned DEFAULT NULL COMMENT '本次发货数量',
                                   `shor_order_id` int DEFAULT '0' COMMENT '子订单表ID',
                                   `num` int DEFAULT '0' COMMENT '本次包裹发货数量',
                                   `confirm_receipt` tinyint DEFAULT '0' COMMENT '确认收货',
                                   `confirm_receipt_time` datetime DEFAULT NULL COMMENT '确认收货时间',
                                   `last_time` int DEFAULT NULL COMMENT '最后一次获取物流动态的时间',
                                   `last_detail` text COLLATE utf8mb4_general_ci COMMENT '物流动态明细',
                                   `status` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物流包裹状态\r\nWAIT_ACCEPT  待揽收\r\nACCEPT 已揽收\r\nTRANSPORT 运输中\r\nDELIVERING 派件中\r\nAGENT_SIGN 已代签收\r\nSIGN 已签收\r\nFAILED  包裹异常',
                                   PRIMARY KEY (`id`),
                                   KEY `sn` (`sn`),
                                   KEY `order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=129 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单物流信息表';


CREATE TABLE `order_refund` (
                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'protobuf:ext:json:[\r\n"item:companiesService:Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto",\r\n"item:companies:Protobuf.Datas.HyCompanies.HyCompaniesProto",\r\n"item:sku:Protobuf.Datas.HyProductsSku.HyProductsSkuProto"\r\n]',
                                `business_id` int unsigned DEFAULT NULL COMMENT '商家',
                                `sn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
                                `order_id` int unsigned DEFAULT NULL COMMENT '订单ID',
                                `product_id` int unsigned DEFAULT NULL COMMENT '产品ID',
                                `sku_id` int unsigned DEFAULT NULL COMMENT '规格ID',
                                `user_id` int unsigned DEFAULT NULL COMMENT '用户ID',
                                `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `shor_order_id` int DEFAULT '0' COMMENT '子订单表ID',
                                `refund_num` int DEFAULT '0' COMMENT '申请退款数量',
                                `refund_only` tinyint DEFAULT '0' COMMENT '是否仅退款',
                                `refund_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '申请退款说明',
                                `refund_price` decimal(10,2) DEFAULT '0.00' COMMENT '申请退款商品单价',
                                `handle_refund_status` tinyint DEFAULT '0' COMMENT '退款状态 0 用户申请， 1 同意 2拒绝 3退款打款中',
                                `handle_refund_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '处理退款说明',
                                `handle_refund_price` decimal(10,2) DEFAULT '0.00' COMMENT '最终退款给用户的金额',
                                `handle_refund_time` datetime DEFAULT NULL COMMENT '出来退款的时间',
                                PRIMARY KEY (`id`),
                                KEY `sn` (`sn`),
                                KEY `order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=130 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单退款信息';


CREATE TABLE `shop_order` (
                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'protobuf:ext:json:[\r\n    "item:companiesService:Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto",\r\n  "item:skus:repeated Protobuf.Datas.HyProductsSku.HyProductsSkuProto",\r\n"item:companies:Protobuf.Datas.HyCompanies.HyCompaniesProto"\r\n]',
                              `user_id` int NOT NULL COMMENT '用户ID',
                              `sn` varchar(255) NOT NULL COMMENT '订单号',
                              `price` decimal(10,2) NOT NULL COMMENT '商品价格，这里是单价',
                              `num` int DEFAULT NULL COMMENT '数量',
                              `business_id` int DEFAULT NULL COMMENT '商家ID',
                              `product_id` int DEFAULT NULL COMMENT '商品ID',
                              `sku_id` int DEFAULT NULL COMMENT 'SKU',
                              `time` int DEFAULT NULL COMMENT '订单时间',
                              `order_id` int unsigned DEFAULT NULL,
                              `invoice_id` int unsigned DEFAULT '0' COMMENT 'invoice_id 发票ID，如果有 表示已经开过票了',
                              `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '产品名称，冗余字段，用于订单查询',
                              `sku_name` varchar(255) DEFAULT NULL COMMENT '规格名称，冗余字段，用于订单查询',
                              `quantity_shipped` int DEFAULT '0' COMMENT '已发货数量',
                              `returned_quantity` int DEFAULT '0' COMMENT '申请售后数量',
                              `completed_quantity` int DEFAULT '0' COMMENT '已经完成数量',
                              PRIMARY KEY (`id`),
                              KEY `IDX_F5299398A76ED395` (`user_id`),
                              KEY `IDX_F52993984584665A` (`product_id`),
                              KEY `IDX_F5299398A89DB457` (`business_id`),
                              KEY `IDX_F52993981777D41C` (`sku_id`),
                              KEY `sn` (`sn`)
) ENGINE=InnoDB AUTO_INCREMENT=195 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;