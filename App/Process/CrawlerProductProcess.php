<?php
declare(strict_types=1);

namespace App\Process;

use App\Model\CrawlerProduct;
use Error;
use Generate\Tables\Datas\ParitySourceTable;
use Swlib\Process\Process;
use Swlib\Utils\Log;
use Swoole\WebSocket\Server;
use Throwable;

//#[Process(interval: 10)]
class CrawlerProductProcess extends Process
{
    public function handle(Server|\Swoole\Http\Server $server): void
    {
        try {
            foreach (new ParitySourceTable()->where([
                [ParitySourceTable::LAST_RUN_TIME, '<', time() - 86400 * 3],
            ])->generator() as $source) {
                /**@var ParitySourceTable $source */
                if (empty($source->url)) {
                    continue;
                }
                CrawlerProduct::run($source);
                sleep(mt_rand(3, 10));
            }

        } catch (Throwable|Error $e) {
            echo $e->getMessage() . PHP_EOL;
            Log::saveException($e, 'crawler-product');
        }
    }
}