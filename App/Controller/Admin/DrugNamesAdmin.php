<?php
namespace App\Controller\Admin;


use Generate\Tables\Datas\DrugNamesTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


/*
* 药品名称
*/
class DrugNamesAdmin extends AbstractAdmin{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '药品名称';
        $config->tableName = DrugNamesTable::class;
    }
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: DrugNamesTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: DrugNamesTable::NAME, label: '药品名称'),
            new ImageField(field: DrugNamesTable::IMAGES, label: '默认图'),
        );
    }
}