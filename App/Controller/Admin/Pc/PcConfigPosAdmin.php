<?php

namespace App\Controller\Admin\Pc;

use Generate\Tables\Datas\PcConfigPosTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class PcConfigPosAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "配置位置";
        $config->tableName = PcConfigPosTable::class;
        $config->order = [
            PcConfigPosTable::DISPLAY_SORT => 'ASC',
            PcConfigPosTable::ID => 'ASC',
        ];
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: PcConfigPosTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: PcConfigPosTable::NAME, label: '位置名称'),
            new NumberField(field: PcConfigPosTable::DISPLAY_SORT, label: '排序'),
        );
    }

}