<?php
namespace App\Controller\Admin\Pc;


use Generate\Tables\Datas\PcFeedbackTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;



class PcFeedbackAdmin extends AbstractAdmin{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "官网留言";
        $config->tableName = PcFeedbackTable::class;
    }
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: PcFeedbackTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: PcFeedbackTable::CONTACTS, label: '联系人'),
            new TextField(field: PcFeedbackTable::PHONE, label: '联系电话'),
            new TextField(field: PcFeedbackTable::EMAIL, label: 'email'),
            new TextField(field: PcFeedbackTable::CONTENT, label: '留言信息'),
            new Int2TimeField(field: PcFeedbackTable::TIME, label: '时间'),
        );
    }
}