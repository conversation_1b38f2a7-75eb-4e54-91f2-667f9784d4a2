<?php

namespace App\Controller\Admin\Pc;

use Generate\Models\Datas\PcConfigModel;
use Generate\Tables\Datas\PcConfigPosTable;
use Generate\Tables\Datas\PcConfigTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextareaField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;


class PcConfigAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "Web配置";
        $config->tableName = PcConfigTable::class;
        $config->addJsFile('/pages/js/pc-config.js');
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new SelectField(field: PcConfigTable::CONFIG_POS_ID, label: '配置位置')->setRelation(PcConfigPosTable::class, PcConfigPosTable::ID, PcConfigPosTable::NAME),
            new SelectField(field: PcConfigTable::CONFIG_TYPE, label: '展示类型')->setDefault(PcConfigModel::ConfigTypeText)->setOptions(
                new OptionManager(PcConfigModel::ConfigTypeImg, PcConfigModel::ConfigTypeTextMaps[PcConfigModel::ConfigTypeImg]),
                new OptionManager(PcConfigModel::ConfigTypeText, PcConfigModel::ConfigTypeTextMaps[PcConfigModel::ConfigTypeText]),
                new OptionManager(PcConfigModel::ConfigTypeIframe, PcConfigModel::ConfigTypeTextMaps[PcConfigModel::ConfigTypeIframe]),
            )->setId('ctx_type'),
            new TextField(field: PcConfigTable::CONFIG_CTX, label: 'URL')->setId('ctx_url')->hideOnFilter()->hideOnList(),
            new ImageField(field: PcConfigTable::CONFIG_CTX, label: '图片')->setId('ctx_img')->hideOnFilter()->hideOnList(),
            new TextareaField(field: PcConfigTable::CONFIG_CTX, label: '内容')->setId('ctx_text')->hideOnFilter()->setTemplateList('pcConfig/ctx_text.twig'),
            new SelectField(field: PcConfigTable::URL_TYPE, label: '跳转类型')->setId('url_type')->setDefault(PcConfigModel::UrlTypeNone)->setOptions(
                new OptionManager(PcConfigModel::UrlTypeInner, PcConfigModel::UrlTypeTextMaps[PcConfigModel::UrlTypeInner]),
                new OptionManager(PcConfigModel::UrlTypeOuter, PcConfigModel::UrlTypeTextMaps[PcConfigModel::UrlTypeOuter]),
                new OptionManager(PcConfigModel::UrlTypeNone, PcConfigModel::UrlTypeTextMaps[PcConfigModel::UrlTypeNone]),
            ),
            new TextField(field: PcConfigTable::URL, label: '跳转链接')->setRequired(false),
            new TextareaField(field: PcConfigTable::TEXT_EXT1, label: '文本扩展1')->hideOnFilter()->hideOnList()->setRequired(false),
            new TextareaField(field: PcConfigTable::TEXT_EXT2, label: '文本扩展2')->hideOnFilter()->hideOnList()->setRequired(false),
            new TextareaField(field: PcConfigTable::TEXT_EXT3, label: '文本扩展3')->hideOnFilter()->hideOnList()->setRequired(false),
            new ImageField(field: PcConfigTable::IMG_EXT1, label: '图片扩展1')->hideOnFilter()->hideOnList()->setRequired(false),
            new ImageField(field: PcConfigTable::IMG_EXT2, label: '图片扩展2')->hideOnFilter()->hideOnList()->setRequired(false),
            new ImageField(field: PcConfigTable::IMG_EXT3, label: '图片扩展3')->hideOnFilter()->hideOnList()->setRequired(false),
            new SwitchField(field: PcConfigTable::IS_ENABLE, label: '是否启用')->hideOnForm()->hideOnList()->setRequired(false)
        );
    }

}