<?php

namespace App\Controller\Admin\System\Banner;

use Generate\Tables\Datas\BannerPosTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class BannerPosAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "广告位置";
        $config->tableName = BannerPosTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: BannerPosTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: BannerPosTable::NAME, label: '位置名称'),
        );
    }

}