<?php

namespace App\Controller\Admin\System\Banner;

use Generate\Tables\Datas\BannerAdTable;
use Generate\Tables\Datas\BannerPosTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;


class BannerAdAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "广告管理";
        $config->tableName = BannerAdTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: BannerAdTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: BannerAdTable::BANNER_POS_ID, label: '广告位置')
                ->setRelation(BannerPosTable::class, BannerPosTable::ID, BannerPosTable::NAME),
            new ImageField(field: BannerAdTable::PATH, label: '广告图片'),
            new SelectField(field: BannerAdTable::URL_TYPE, label: '点击跳转类型')->setOptions(
                new OptionManager(1, '应用内页'),
                new OptionManager(2, '外部地址'),
                new OptionManager(3, '不跳转'),
                new OptionManager(4, '查看视频'),
            ),
            new TextField(field: BannerAdTable::URL, label: '跳转地址')->setRequired(false),
            new SwitchField(field: BannerAdTable::IS_ENABLE, label: '是否启用')->hideOnForm(),
        );
    }

}