<?php

namespace App\Controller\Admin\System;

use Generate\Tables\Datas\ArticleTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\EditorField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class ArticleAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "文章内容";
        $config->tableName = ArticleTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ArticleTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: ArticleTable::TITLE, label: '标题'),
            new EditorField(field: ArticleTable::CONTENT, label: '内容')->hideOnList(),
            new SwitchField(field: ArticleTable::ENABLE, label: '是否启用'),
        );
    }

}