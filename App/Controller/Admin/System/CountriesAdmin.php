<?php

namespace App\Controller\Admin\System;

use Generate\Tables\Datas\CountriesTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class CountriesAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "国家管理";
        $config->tableName = CountriesTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: CountriesTable::ID, label: 'ID')->hideOnForm(),
            new ImageField(field: CountriesTable::ICON_PATH, label: 'logo'),
            new TextField(field: CountriesTable::NAME, label: '国家名称'),
            new TextField(field: CountriesTable::COUNTRY_CODE, label: '国家编码'),
            new TextField(field: CountriesTable::PHONE_PREFIX, label: '电话前缀'),

        );
    }

}