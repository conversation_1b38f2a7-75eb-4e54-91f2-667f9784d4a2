<?php

namespace App\Controller\Admin\System;

use Generate\Tables\Datas\ConfigTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class ConfigAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "系统配置";
        $config->tableName = ConfigTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ConfigTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: ConfigTable::MARK, label: '标识'),
            new TextField(field: ConfigTable::VALUE, label: '值')->hideOnList(),
            new SwitchField(field: ConfigTable::IS_ENABLE, label: '是否启用'),
        );
    }

}