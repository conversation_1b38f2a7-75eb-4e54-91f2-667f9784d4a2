<?php

namespace App\Controller\Admin\System;

use Generate\Tables\Datas\CitysTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class CityAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "城市管理";
        $config->tableName = CitysTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: CitysTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: CitysTable::CODE, label: '编码'),
            new TextField(field: CitysTable::NAME, label: '名称'),
            new TextField(field: CitysTable::LETTER, label: '字母'),
            new SwitchField(field: CitysTable::IS_HOT, label: '热门城市'),

        );
    }

}