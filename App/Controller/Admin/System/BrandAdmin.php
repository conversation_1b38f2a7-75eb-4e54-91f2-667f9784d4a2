<?php

namespace App\Controller\Admin\System;

use Generate\Tables\Datas\BrandTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class BrandAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "品牌管理";
        $config->tableName = BrandTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
           new NumberField(field: BrandTable::ID, label: 'ID')->hideOnForm(),
           new ImageField(field: BrandTable::PICTURE, label: 'logo'),
           new TextField(field: BrandTable::NAME, label: '名称'),
           new TextField(field: BrandTable::PREFIX, label: '前缀分类'),

        );
    }

}