<?php

namespace App\Controller\Admin\System;


use Generate\Models\Datas\FeedbackModel;
use Generate\Tables\Datas\FeedbackTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;


/*
* 
*/

class FeedbackAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '意见反馈';
        $config->tableName = FeedbackTable::class;
    }

    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: FeedbackTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: FeedbackTable::DESC, label: '问题描述'),
            new ImageField(field: FeedbackTable::PICS, label: '截图')->setMax(9),
            new Int2TimeField(field: FeedbackTable::TIME, label: '反馈时间'),
            new SelectField(field: FeedbackTable::STATUS, label: '状态')->setOptions(
                new OptionManager(FeedbackModel::StatusWait, '待处理'),
                new OptionManager(FeedbackModel::StatusIng, '处理中'),
                new OptionManager(FeedbackModel::StatusComplete, '已完成'),
            ),
            new Int2TimeField(field: FeedbackTable::HANDLE_TIME, label: '处理时间'),
            new TextField(field: FeedbackTable::USER_ID, label: '反馈用户'),
        );
    }
}