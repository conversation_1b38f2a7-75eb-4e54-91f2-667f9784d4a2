<?php

namespace App\Controller\Admin\Wx;

use Generate\Tables\Datas\WxProjectTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class WxProjectAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "项目管理";
        $config->tableName = WxProjectTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: WxProjectTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: WxProjectTable::NAME, label: '项目名称'),
            new NumberField(field: WxProjectTable::DISPLAY_SORTING, label: '显示排序')->setRequired(false)->hideOnFilter(),
        );
    }
}