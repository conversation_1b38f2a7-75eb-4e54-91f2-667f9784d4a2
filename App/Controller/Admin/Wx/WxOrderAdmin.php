<?php

namespace App\Controller\Admin\Wx;

use Generate\Tables\Datas\WxEngineerTable;
use Generate\Tables\Datas\WxOrderTable;
use Generate\Tables\Datas\WxServiceTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Utils\Language;
use Throwable;

class WxOrderAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "订单管理";
        $config->tableName = WxOrderTable::class;
    }


    /**
     * @throws Throwable
     */
    public function join(WxOrderTable $query): void
    {
        $query->join(WxEngineerTable::TABLE_NAME, WxEngineerTable::ID, WxOrderTable::ENGINEER_ID);
        $query->join(WxServiceTable::TABLE_NAME, WxServiceTable::ID, WxOrderTable::SERVICE_ID);
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: WxOrderTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: WxOrderTable::TYPE, label: '类型')->setOptions(
                new OptionManager('wx',Language::get('维修')),
                new OptionManager('install',Language::get('安装'))
            ),
            new TextField(field: WxEngineerTable::NAME, label: '工程师名称'),
            new TextField(field: WxEngineerTable::PHONE, label: '工程师电话'),
            new TextField(field: WxServiceTable::MEN_ZHEN, label: '门诊名称'),
            new TextField(field: WxServiceTable::ADDR, label: '地址'),
            new TextField(field: WxServiceTable::NAME, label: '联系人'),
            new TextField(field: WxServiceTable::PHONE, label: '联系电话'),
            new TextField(field: WxServiceTable::DESC, label: '维修简介'),
            new TextField(field: WxServiceTable::MANUFACTURER, label: '厂家'),
            new Int2TimeField(field: WxOrderTable::TIME, label: '接单时间'),
        );
    }
}