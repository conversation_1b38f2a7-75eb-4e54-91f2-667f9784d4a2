<?php

namespace App\Controller\Admin\Wx;

use Generate\Tables\Datas\CitysTable;
use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\WxEngineerTable;
use Generate\Tables\Datas\WxProjectTable;
use Generate\Tables\Datas\WxServiceTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\HiddenField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\PhoneField;
use Swlib\Admin\Fields\SelectArrayField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Admin\Utils\Func;
use Swlib\Utils\Language;
use Throwable;

class WxServiceAdmin extends AbstractAdmin
{

    /**
     * @throws Throwable
     */
    protected function configPage(PageConfig $config): void
    {
        $type = $this->get(WxServiceTable::TYPE);
        $pageName = $type == 'wx' ? Language::get("维修需求列表") : Language::get('安装需求列表');
        $config->setPageName($pageName, false);
        $config->tableName = WxServiceTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $type = $this->get(WxServiceTable::TYPE);
        $this->listRefreshUrl = Func::url('lists', [WxServiceTable::TYPE => $type], [], false);
        if ($type == 'wx') {
            $fields->setFields(
                new NumberField(field: WxServiceTable::ID, label: 'ID')->hideOnForm(),
                new HiddenField(field: WxServiceTable::TYPE, label: '类型')->setDefault($type),
                new TextField(field: WxServiceTable::MEN_ZHEN, label: '门诊名称'),
                new SelectField(field: WxServiceTable::USER_ID, label: '发布用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
                new SelectField(field: WxServiceTable::AREA, label: '所在区域')->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                new SelectArrayField(field: WxServiceTable::PROJECT_IDS, label: '维修项目')->setRelation(WxProjectTable::class, WxProjectTable::ID, WxProjectTable::NAME),
                new TextField(field: WxServiceTable::ADDR, label: '地址'),
                new TextField(field: WxServiceTable::NAME, label: '联系人'),
                new PhoneField(field: WxServiceTable::PHONE, label: '联系电话')->setRequired(false),
                new TextField(field: WxServiceTable::DESC, label: '维修简介')->hideOnFilter(),
                new SelectField(field: WxServiceTable::STATUS, label: '状态')->setOptions(
                    new OptionManager('wait', Language::get('待接单')),
                    new OptionManager('complete', Language::get('已完成')),
                    new OptionManager('in_service', Language::get('服务中')),
                    new OptionManager('cancel', Language::get('已取消')),
                ),
                new Int2TimeField(field: WxServiceTable::TIME, label: '添加时间'),
                new TextField(field: WxServiceTable::PICS, label: '图片列表')->hideOnList()->hideOnForm()->hideOnFilter()->setListFormat(function ($value) {
                    $pics = json_decode($value, true);
                    $str = '';
                    foreach ($pics as $item) {
                        if ($item['type'] == 'video') {
                            $str .= '<div><video src="' . $item['path'] . '" controls="controls" width="300px" height="300px"></video></div>';
                        } else {
                            $str .= '<div><img src="' . $item['path'] . '" width="100px" height="100px"  alt=""/></div>';
                        }
                    }
                    return $str;
                }),
                new SelectField(field: WxServiceTable::ENGINEER_ID, label: '服务工程师')->setRelation(WxEngineerTable::class, WxEngineerTable::ID, WxEngineerTable::NAME),

            );
        } else {
            $fields->setFields(
                new NumberField(field: WxServiceTable::ID, label: 'ID')->hideOnForm(),
                new SelectField(field: WxServiceTable::USER_ID, label: '发布用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
                new HiddenField(field: WxServiceTable::TYPE, label: '类型')->setDefault($type),
                new TextField(field: WxServiceTable::MANUFACTURER, label: '厂家'),
                new SelectArrayField(field: WxServiceTable::PROJECT_IDS, label: '安装项目')->setRelation(WxProjectTable::class, WxProjectTable::ID, WxProjectTable::NAME),
                new SelectField(field: WxServiceTable::AREA, label: '所在区域')->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                new TextField(field: WxServiceTable::ADDR, label: '地址'),
                new TextField(field: WxServiceTable::NAME, label: '联系人'),
                new PhoneField(field: WxServiceTable::PHONE, label: '联系电话')->setRequired(false),
                new SelectField(field: WxServiceTable::STATUS, label: '状态')->setOptions(
                    new OptionManager('wait', Language::get('待接单')),
                    new OptionManager('complete', Language::get('已完成')),
                    new OptionManager('in_service', Language::get('服务中')),
                    new OptionManager('cancel', Language::get('已取消')),
                ),
                new Int2TimeField(field: WxServiceTable::TIME, label: '添加时间'),
                new SelectField(field: WxServiceTable::ENGINEER_ID, label: '服务工程师')->setRelation(WxEngineerTable::class, WxEngineerTable::ID, WxEngineerTable::NAME),
            );
        }


    }
}