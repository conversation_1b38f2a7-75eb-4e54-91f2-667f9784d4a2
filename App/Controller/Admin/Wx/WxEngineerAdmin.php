<?php

namespace App\Controller\Admin\Wx;

use Generate\Models\Datas\WxEngineerModel;
use Generate\Tables\Datas\CitysTable;
use Generate\Tables\Datas\WxEngineerTable;
use Generate\Tables\Datas\WxFieldTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectArrayField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Admin\Utils\Func;
use Protobuf\Datas\WxEngineer\WxEngineerStatusEnum;
use Throwable;

class WxEngineerAdmin extends AbstractAdmin
{


    /**
     * @throws Throwable
     */
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '工程师入驻';
        $config->tableName = WxEngineerTable::class;
    }




    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: WxEngineerTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: WxEngineerTable::TYPE, label: '类型')->setOptions(
                new OptionManager(id: WxEngineerModel::TypeAll, text: '全部'),
                new OptionManager(id: WxEngineerModel::TypeWx, text: '维修'),
                new OptionManager(id: WxEngineerModel::TypeInstall, text: '安装'),
            ),
            new TextField(field: WxEngineerTable::NAME, label: '姓名'),
            new TextField(field: WxEngineerTable::PHONE, label: '电话')->setRequired(false),
            new SelectArrayField(field: WxEngineerTable::AREA, label: '服务区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
            new SelectArrayField(field: WxEngineerTable::FIELD, label: '擅长领域')->setRelation(WxFieldTable::class, WxFieldTable::ID, WxFieldTable::NAME),
            new ImageField(field: WxEngineerTable::CARD_FRONT, label: '身份证正面'),
            new ImageField(field: WxEngineerTable::CARD_BACK, label: '身份证反面'),
            new ImageField(field: WxEngineerTable::CERTIFICATE, label: '证书')->setRequired(false),
            new SelectField(field: WxEngineerTable::STATUS, label: '审核状态')->setOptions(
                new OptionManager(strtolower(WxEngineerStatusEnum::name(WxEngineerStatusEnum::PENDING)), '待审核'),
                new OptionManager(strtolower(WxEngineerStatusEnum::name(WxEngineerStatusEnum::APPROVED)), '审核通过'),
                new OptionManager(strtolower(WxEngineerStatusEnum::name(WxEngineerStatusEnum::DISABLED)), '禁用'),
                new OptionManager(strtolower(WxEngineerStatusEnum::name(WxEngineerStatusEnum::REJECT)), '拒绝入驻'),
            ),
            new Int2TimeField(field: WxEngineerTable::TIME, label: '入驻时间')->hideOnFilter(),
        );
    }
}