<?php

namespace App\Controller\Admin\Wx;

use Generate\Tables\Datas\WxFieldTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class WxFieldAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "擅长领域";
        $config->tableName = WxFieldTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: WxFieldTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: WxFieldTable::NAME, label: '领域名称'),
            new NumberField(field: WxFieldTable::DISPLAY_SORTING, label: '显示排序')->setRequired(false)->hideOnFilter(),
        );
    }
}