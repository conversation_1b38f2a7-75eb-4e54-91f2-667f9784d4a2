<?php

namespace App\Controller\Admin\Auth;


use Generate\Tables\Datas\AuthLevelsTable;
use Generate\Tables\Datas\AuthUserInfoTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Throwable;


/*
* 用户认证信息表
*/

class AuthUserInfoAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '用户认证信息表';
        $config->tableName = AuthUserInfoTable::class;
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: AuthUserInfoTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: AuthUserInfoTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: AuthUserInfoTable::CURRENT_LEVEL_ID, label: '当前认证等级')->setRelation(AuthLevelsTable::class, AuthLevelsTable::ID, AuthLevelsTable::LEVEL_NAME),
            new TextField(field: AuthUserInfoTable::DEPOSIT_PAID, label: '已缴纳保证金金额'),
            new TextField(field: AuthUserInfoTable::DEPOSIT_USED, label: '已使用保证金金额'),
            new TextField(field: AuthUserInfoTable::AVAILABLE_DEPOSIT, label: '可用保证金金额'),
            new SwitchField(field: AuthUserInfoTable::AUTH_STATUS, label: '有效认证'),
        );
    }
}