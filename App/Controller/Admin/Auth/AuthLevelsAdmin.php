<?php

namespace App\Controller\Admin\Auth;


use Generate\Tables\Datas\AuthLevelsTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\EditorField;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Throwable;


/*
* 认证等级权益表
*/

class AuthLevelsAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '认证等级权益表';
        $config->tableName = AuthLevelsTable::class;
    }
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: AuthLevelsTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: AuthLevelsTable::LEVEL_NAME, label: '认证等级名称'),
            new TextField(field: AuthLevelsTable::LEVEL_ORDER, label: '排序优先级(数字越小优先级越高)'),
            new TextField(field: AuthLevelsTable::DEPOSIT_AMOUNT, label: '保证金金额'),
            new ImageField(field: AuthLevelsTable::BADGE_ICON, label: '等级徽章图标'),
            new EditorField(field: AuthLevelsTable::DESC, label: '权益说明'),
            new EditorField(field: AuthLevelsTable::WARN_DESC, label: '注意事项'),
            new SwitchField(field: AuthLevelsTable::STATUS, label: '是否启用'),
        );
    }
}
