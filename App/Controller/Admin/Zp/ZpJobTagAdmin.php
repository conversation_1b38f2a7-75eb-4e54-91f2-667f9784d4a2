<?php

namespace App\Controller\Admin\Zp;

use Generate\RouterPath;
use Generate\Tables\Datas\ZpJobTagTable;
use Generate\Tables\Datas\ZpTagTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Enum\ActionDefaultButtonEnum;
use Swlib\Admin\Fields\HiddenField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Table\TableInterface;
use Throwable;


class ZpJobTagAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "职位标签";
        $config->tableName = ZpJobTagTable::class;
    }


    /**
     * @throws Throwable
     */
    public function listsQuery(TableInterface $query): void
    {
        /** @var ZpJobTagTable $query */
        $query->addWhere(ZpJobTagTable::JOB_ID, $this->get(ZpJobTagTable::JOB_ID));
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $jobId = $this->get(ZpJobTagTable::JOB_ID, 0);
        $fields->setFields(
           new NumberField(field: ZpJobTagTable::ID, label: 'ID')->hideOnForm(),
           new HiddenField(field: ZpJobTagTable::JOB_ID, label: '职位')->setDefault($jobId),
           new SelectField(field: ZpJobTagTable::TAG_ID, label: '标签')->setRelation(ZpTagTable::class, ZpTagTable::ID, ZpTagTable::NAME),
        );
    }

    /**
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {
        $actions->disabledActions = [ActionDefaultButtonEnum::NEW];
        $actions->addActions(
            new  Action('添加', RouterPath::AdminZpZpJobTagNew, [
                ZpJobTagTable::JOB_ID => $this->get(ZpJobTagTable::JOB_ID)
            ])->showIndex()
        );
    }

}