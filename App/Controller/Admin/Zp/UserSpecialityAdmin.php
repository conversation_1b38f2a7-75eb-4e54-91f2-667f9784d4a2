<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\SpecialityTable;
use Generate\Tables\Datas\UserSpecialityTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Throwable;


class UserSpecialityAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "用户选择特长";
        $config->tableName = UserSpecialityTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: UserSpecialityTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: UserSpecialityTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: UserSpecialityTable::SPECIALITY_ID, label: '特长')->setRelation(SpecialityTable::class, SpecialityTable::ID, SpecialityTable::NAME)
        );
    }

}