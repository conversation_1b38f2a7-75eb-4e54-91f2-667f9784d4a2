<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpJobIntentionTable;
use Generate\Tables\Datas\ZpJobsTypeTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectArrayField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;


class ZpJobIntentionAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "求职意向";
        $config->tableName = ZpJobIntentionTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpJobIntentionTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ZpJobIntentionTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: ZpJobIntentionTable::TYPE, label: '类型')->setOptions(
                new OptionManager(0,'全职'),
                new OptionManager(1,'兼职'),
            ),
           new TextField(field: ZpJobIntentionTable::CITY, label: '希望城市'),
           new SelectArrayField(field: ZpJobIntentionTable::TYPE_IDS, label: '希望职位')->setRelation(ZpJobsTypeTable::class, ZpJobsTypeTable::ID, ZpJobsTypeTable::NAME),
           new TextField(field: ZpJobIntentionTable::SALARY_RANGE, label: '薪资范围'),

        );
    }

}