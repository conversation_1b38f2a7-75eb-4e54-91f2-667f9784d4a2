<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpResumeTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Fields\UrlField;
use Throwable;


class ZpResumeAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "用户简历";
        $config->tableName = ZpResumeTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpResumeTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ZpResumeTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new TextField(field: ZpResumeTable::NAME, label: '简历名称'),
            new UrlField(field: ZpResumeTable::URL, label: '附件简历地址'),
            new Int2TimeField(field: ZpResumeTable::TIME, label: '上传时间'),
        );
    }

}