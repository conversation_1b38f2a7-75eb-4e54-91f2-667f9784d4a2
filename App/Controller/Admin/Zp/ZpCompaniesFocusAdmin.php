<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpCompaniesFocusTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Throwable;


class ZpCompaniesFocusAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "企业关注";
        $config->tableName = ZpCompaniesFocusTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpCompaniesFocusTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ZpCompaniesFocusTable::COMPANY_ID, label: '企业')->setRelation(ZpCompaniesTable::class, ZpCompaniesTable::ID, ZpCompaniesTable::NAME),
            new SelectField(field: ZpCompaniesFocusTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),

        );
    }

}