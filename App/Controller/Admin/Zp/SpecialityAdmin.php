<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\SpecialityTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class SpecialityAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "特长管理";
        $config->tableName = SpecialityTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: SpecialityTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: SpecialityTable::NAME, label: '名称')
        );
    }

}