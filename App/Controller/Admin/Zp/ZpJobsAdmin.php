<?php

namespace App\Controller\Admin\Zp;

use Generate\RouterPath;
use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Generate\Tables\Datas\ZpJobsLevelTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpJobsTypeTable;
use Generate\Tables\Datas\ZpJobTagTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\EditorField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextareaField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;


class ZpJobsAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "招聘职位";
        $config->tableName = ZpJobsTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpJobsTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ZpJobsTable::USER_ID, label: '发布用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: ZpJobsTable::COMPANY_ID, label: '公司')->setRelation(ZpCompaniesTable::class, ZpCompaniesTable::ID, ZpCompaniesTable::NAME),
            new SelectField(field: ZpJobsTable::TYPE, label: '招聘类型')->setOptions(
                new OptionManager(0, '全职'),
                new OptionManager(1, '兼职'),
            ),
            new TextField(field: ZpJobsTable::TITLE, label: '职位名称'),
            new TextareaField(field: ZpJobsTable::DESCRIPTION, label: '职位描述')->hideOnList(),
            new TextareaField(field: ZpJobsTable::ASK, label: '职位要求')->hideOnList(),
            new TextField(field: ZpJobsTable::CONTACTS_PHONE, label: '联系电话')->setRequired(false),
            new SelectField(field: ZpJobsTable::SALARY_RANGE, label: '薪资范围')->setOptions(
                new OptionManager('2500-3000', '2500-3000'),
                new OptionManager('3000-5000', '3000-5000'),
                new OptionManager('5000-8000', '5000-8000'),
                new OptionManager('8000-1万', '8000-1万'),
                new OptionManager('1万-1.2万', '1万-1.2万'),
                new OptionManager('1.2万-1.5万', '1.2万-1.5万'),
                new OptionManager('1.5万-1.8万', '1.5万-1.8万'),
                new OptionManager('1.8万-2万', '1.8万-2万'),
                new OptionManager('2万-5万', '2万-5万'),
                new OptionManager('5万-10万', '5万-10万'),
                new OptionManager('面议', '面议'),
            ),
           new SelectField(field: ZpJobsTable::TYPE_ID, label: '职位类型')->setRelation(ZpJobsTypeTable::class, ZpJobsTypeTable::ID, ZpJobsTypeTable::NAME),
           new SelectField(field: ZpJobsTable::LEVEL_ID, label: '职位级别')->setRelation(ZpJobsLevelTable::class, ZpJobsLevelTable::ID, ZpJobsLevelTable::NAME),
           new Int2TimeField(field: ZpJobsTable::CREATED_TIME, label: '发布时间')->hideOnForm(),
           new NumberField(field: ZpJobsTable::SHOW_NUM, label: '查看次数')->hideOnForm(),
        );
    }

    /**
     * @throws Throwable
     */
    public function insertBefore(ZpJobsTable $table): void
    {
        $table->createdTime = time();
    }

    /**
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {
        parent::configAction($actions);
        $actions->addActions(
            new Action('职位标签', RouterPath::AdminZpZpJobTagLists, [
                ZpJobTagTable::JOB_ID => "%" . ZpJobsTable::ID
            ])->setSort(10)->showList()->setTarget('_blank')
        );
    }

}