<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\ZpJobsLevelTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class ZpJobsLevelAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "职位级别";
        $config->tableName = ZpJobsLevelTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpJobsLevelTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: ZpJobsLevelTable::NAME, label: '级别')
        );
    }

}