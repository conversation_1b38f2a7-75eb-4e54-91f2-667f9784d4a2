<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpJobsUserSeeTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Throwable;


class ZpJobsUserSeeAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "职位查看记录";
        $config->tableName = ZpJobsUserSeeTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
           new NumberField(field: ZpJobsUserSeeTable::ID, label: 'ID')->hideOnForm(),
           new SelectField(field: ZpJobsUserSeeTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
           new SelectField(field: ZpJobsUserSeeTable::JOB_ID, label: '职位')->setRelation(ZpJobsTable::class, ZpJobsTable::ID, ZpJobsTable::TITLE),
           new NumberField(field: ZpJobsUserSeeTable::NUM, label: '查看次数'),
           new Int2TimeField(field: ZpJobsUserSeeTable::FIRST_TIME, label: '首次查看时间'),
           new Int2TimeField(field: ZpJobsUserSeeTable::LAST_TIME, label: '上次查看时间'),
        );
    }

}