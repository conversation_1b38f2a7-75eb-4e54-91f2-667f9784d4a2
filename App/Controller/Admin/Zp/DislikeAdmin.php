<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\DislikeTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;


class DislikeAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "不喜欢列表";
        $config->tableName = DislikeTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: DislikeTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: DislikeTable::USER_ID, label: '用户ID'),
            new TextField(field: DislikeTable::TARGET_ID, label: '目标ID'),
            new SelectField(field: DislikeTable::TYPE, label: '类型')->setOptions(
                new OptionManager(1, '门诊招聘'),
                new OptionManager(2, '医生简历')
            ),
        );
    }

}