<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpJobsApplyTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpResumeTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;


class ZpJobsApplyAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "职位申请";
        $config->tableName = ZpJobsApplyTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpJobsApplyTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ZpJobsApplyTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: ZpJobsApplyTable::JOB_ID, label: '职位')->setRelation(ZpJobsTable::class, ZpJobsTable::ID, ZpJobsTable::TITLE),
            new SelectField(field: ZpJobsApplyTable::RESUME_ID, label: '简历')->setRelation(ZpResumeTable::class, ZpResumeTable::ID, ZpResumeTable::NAME),
            new Int2TimeField(field: ZpJobsApplyTable::TIME, label: '申请时间'),
            new SelectField(field: ZpJobsApplyTable::STATUS, label: '状态')->setOptions(
                new OptionManager('pending', '等待中'),
                new OptionManager('interview', '面试中'),
                new OptionManager('rejected', '拒绝'),
                new OptionManager('accepted', '已通过'),
            ),
           new SelectField(field: ZpJobsApplyTable::INTERVIEW_USER_ID, label: '面试官')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
           new Int2TimeField(field: ZpJobsApplyTable::INTERVIEW_TIME, label: '面试时间'),
           new TextField(field: ZpJobsApplyTable::FEEDBACK, label: '面试反馈'),
        );
    }

}