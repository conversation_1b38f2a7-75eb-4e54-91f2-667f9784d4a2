<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpJobsCollectTable;
use Generate\Tables\Datas\ZpJobsTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Throwable;


class ZpJobsCollectAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "职位收藏";
        $config->tableName = ZpJobsCollectTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpJobsCollectTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ZpJobsCollectTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: ZpJobsCollectTable::JOBS_ID, label: '职位')->setRelation(ZpJobsTable::class, ZpJobsTable::ID, ZpJobsTable::TITLE),
            new Int2TimeField(field: ZpJobsCollectTable::TIME, label: '收藏时间'),
        );
    }

}