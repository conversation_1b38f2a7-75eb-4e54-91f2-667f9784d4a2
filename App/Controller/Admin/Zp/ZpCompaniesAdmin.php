<?php

namespace App\Controller\Admin\Zp;

use Generate\Models\Datas\ZpCompaniesModel;
use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextareaField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Table\TableInterface;
use Throwable;


class ZpCompaniesAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "认证管理";
        $config->tableName = ZpCompaniesTable::class;
    }


    protected function configAction(ActionsConfig $actions): void
    {

    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpCompaniesTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ZpCompaniesTable::USER_ID, label: '登录用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new TextField(field: ZpCompaniesTable::NAME, label: '认证名称'),
            new Int2TimeField(field: ZpCompaniesTable::TIME, label: '入驻时间')->hideOnForm(),
            new TextField(field: ZpCompaniesTable::CONTACTS_NAME, label: '联系人'),
            new TextField(field: ZpCompaniesTable::CONTACTS_PHONE, label: '联系电话'),
            new ImageField(field: ZpCompaniesTable::BUSINESS_LICENSE, label: '营业执照')->setRequired(false),
            new ImageField(field: ZpCompaniesTable::PERMIT, label: '许可证')->setRequired(false),
            new SelectField(field: ZpCompaniesTable::STATUS, label: '审核状态')->setOptions(
                new OptionManager(ZpCompaniesModel::StatusPending, ZpCompaniesModel::StatusTextMaps[ZpCompaniesModel::StatusPending]),
                new OptionManager(ZpCompaniesModel::StatusApproved, ZpCompaniesModel::StatusTextMaps[ZpCompaniesModel::StatusApproved]),
                new OptionManager(ZpCompaniesModel::StatusDisabled, ZpCompaniesModel::StatusTextMaps[ZpCompaniesModel::StatusDisabled]),
                new OptionManager(ZpCompaniesModel::StatusReject, ZpCompaniesModel::StatusTextMaps[ZpCompaniesModel::StatusReject]),
            ),
            new Int2TimeField(field: ZpCompaniesTable::EXPIRE_TIME, label: '认证到期时间')->hideOnFilter(),
            new TextareaField(field: ZpCompaniesTable::MSG, label: '审核备注')->setRequired(false)->hideOnFilter(),
        );
    }


    public function insertUpdateBefore(ZpCompaniesTable $table): void
    {
        if (!$table->time) {
            $table->time = time();
        }

    }

}