<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\ZpTagTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class ZpTagAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "职位标签";
        $config->tableName = ZpTagTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpTagTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: ZpTagTable::NAME, label: '名称')
        );
    }

}