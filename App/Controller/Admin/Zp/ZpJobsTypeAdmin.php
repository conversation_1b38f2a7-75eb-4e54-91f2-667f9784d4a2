<?php

namespace App\Controller\Admin\Zp;

use Generate\Tables\Datas\ZpJobsTypeTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;


class ZpJobsTypeAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "职位类型";
        $config->tableName = ZpJobsTypeTable::class;
    }


    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ZpJobsTypeTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: ZpJobsTypeTable::NAME, label: '名称')
        );
    }

}