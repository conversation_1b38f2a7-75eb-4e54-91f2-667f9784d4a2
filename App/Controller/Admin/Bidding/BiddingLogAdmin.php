<?php

namespace App\Controller\Admin\Bidding;


use Generate\Tables\Datas\BiddingLogTable;
use Generate\Tables\Datas\BiddingShopTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class BiddingLogAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "竞价记录";
        $config->tableName = BiddingLogTable::class;
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: BiddingLogTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: BiddingLogTable::BIDDING_SHOP_ID, label: '竞价商品')->setRelation(BiddingShopTable::class, BiddingShopTable::ID, BiddingShopTable::NAME),
            new TextField(field: BiddingLogTable::DESC, label: '参与竞价时对商品的介绍'),
            new TextField(field: BiddingLogTable::PRICE, label: '报价价格'),
            new Int2TimeField(field: BiddingLogTable::TIME, label: '发布时间'),
            new SelectField(field: BiddingLogTable::USER_ID, label: '用户ID ')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new TextField(field: BiddingLogTable::CONTACT_NAME, label: '联系人')->setRequired(false),
            new TextField(field: BiddingLogTable::CONTACT_PHONE, label: '联系电话')->setRequired(false),
        );
    }
}