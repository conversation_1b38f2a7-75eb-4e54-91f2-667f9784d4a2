<?php

namespace App\Controller\Admin\Bidding;


use Generate\Models\Datas\BiddingShopModel;
use Generate\RouterPath;
use Generate\Tables\Datas\BiddingLogTable;
use Generate\Tables\Datas\BiddingShopTable;
use Generate\Tables\Datas\CitysTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;


class BiddingShopAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "竞价商品";
        $config->tableName = BiddingShopTable::class;
    }

    /**
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {
        parent::configAction($actions);
        $actions->addActions(
            new Action('竞价商家', RouterPath::AdminBiddingBiddingLogLists, [
                BiddingLogTable::BIDDING_SHOP_ID => "%" . BiddingShopTable::ID
            ])->setSort(10)->showList()->setTarget('_blank')
        );
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: BiddingShopTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: BiddingShopTable::NAME, label: '商品名称'),
            new TextField(field: BiddingShopTable::BUDGET_MIN, label: '最低预算'),
            new TextField(field: BiddingShopTable::BUDGET_MAX, label: '最高预算'),
            new TextField(field: BiddingShopTable::ASK, label: '商品要求')->setRequired(false),
            new TextField(field: BiddingShopTable::CONTACT_NAME, label: '联系人')->setRequired(false),
            new TextField(field: BiddingShopTable::CONTACT_PHONE, label: '联系电话')->setRequired(false),
            new Int2TimeField(field: BiddingShopTable::TIME, label: '发布时间'),
            new TextField(field: BiddingShopTable::BIDDING_COUNT, label: '竞价人数')->hideOnForm(),
            new TextField(field: BiddingShopTable::BIDDING_MIN, label: '最低价')->hideOnForm(),
            new TextField(field: BiddingShopTable::BIDDING_MAX, label: '最高价')->hideOnForm(),
            new TextField(field: BiddingShopTable::BIDDING_AVG, label: '平均价')->hideOnForm(),
            new TextField(field: BiddingShopTable::BIDDING_NAME, label: '最低商家名称')->hideOnForm(),
            new SelectField(field: BiddingShopTable::STATUS, label: '状态')->setOptions(
                new OptionManager(BiddingShopModel::StatusWait, BiddingShopModel::StatusTextMaps[BiddingShopModel::StatusWait]),
                new OptionManager(BiddingShopModel::StatusIng, BiddingShopModel::StatusTextMaps[BiddingShopModel::StatusIng]),
                new OptionManager(BiddingShopModel::StatusEnd, BiddingShopModel::StatusTextMaps[BiddingShopModel::StatusEnd]),
            ),
            new SelectField(field: BiddingShopTable::USER_ID, label: '用户ID')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: BiddingShopTable::ADDR, label: '选择的区域')->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
        );
    }
}