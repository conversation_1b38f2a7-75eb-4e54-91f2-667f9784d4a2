<?php
namespace App\Controller\Admin\HuoYuan;


use Generate\Tables\Datas\HyProductsTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


/*
* 产品信息表
*/
class HyProductsAdmin extends AbstractAdmin{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '产品信息表';
        $config->tableName = HyProductsTable::class;
    }
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: HyProductsTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: HyProductsTable::COMPANY_ID, label: '所属公司ID'),
            new TextField(field: HyProductsTable::NAME, label: '产品名称'),
            new TextField(field: HyProductsTable::DESCRIPTION, label: '产品描述'),
            new TextField(field: HyProductsTable::CERTIFICATE_NO, label: '产品证件号'),
            new TextField(field: HyProductsTable::INSURANCE_CODE, label: '医保编码'),
            new Int2TimeField(field: HyProductsTable::TIME, label: 'time'),
            new TextField(field: HyProductsTable::IMAGE, label: '产品图片'),
            new TextField(field: HyProductsTable::PRICE, label: '产品价格'),
        );
    }
}