<?php

namespace App\Controller\Admin\HuoYuan;


use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\HiddenField;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Exception\AppException;
use Swlib\Table\TableInterface;
use Throwable;


/*
* 供应商/公司信息表
*/

class HyCompaniesAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '厂商入驻';
        $config->tableName = HyCompaniesTable::class;
    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    public function listsQuery(HyCompaniesTable $query): void
    {
        $type = $this->get(HyCompaniesTable::TYPE, '', 1);
        $query->setDebugSql()->addWhere(HyCompaniesTable::TYPE, $type);
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $type = $this->get(HyCompaniesTable::TYPE, '', 1);
        if ($type == 1) {
            $fields->setFields(
                new NumberField(field: HyCompaniesTable::ID, label: 'ID')->hideOnForm(),
                new HiddenField(field: HyCompaniesTable::TYPE, label: 'type')->setDefault($type),
                new TextField(field: HyCompaniesTable::NAME, label: '公司名称'),
                new ImageField(field: HyCompaniesTable::LOGO, label: 'logo'),
                new TextField(field: HyCompaniesTable::PHONE, label: '联系电话')->setRequired(false),
//            new TextField(field: HyCompaniesTable::MAIN_PRODUCTS, label: '主营产品简介'),
                new TextField(field: HyCompaniesTable::INTRODUCTION, label: '公司详细介绍')->setRequired(false),
                new TextField(field: HyCompaniesTable::ADDRESS, label: '公司地址')->setRequired(false),
//            new TextField(field: HyCompaniesTable::BANNER_IMAGE, label: '公司背景图片'),
                new Int2TimeField(field: HyCompaniesTable::TIME, label: 'time')->hideOnForm(),
                new SelectField(field: HyCompaniesTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
                new SelectField(field: HyCompaniesTable::STATUS, label: '入驻状态')->setOptions(
                    new OptionManager('pending', '待审核'),
                    new OptionManager('approved', '审核通过'),
                    new OptionManager('disabled', '禁用'),
                    new OptionManager('reject', '审核拒绝'),
                ),
                new ImageField(field: HyCompaniesTable::BUSINESS_LICENSE, label: '营业执照')->setRequired(false),
                new ImageField(field: HyCompaniesTable::PERMIT, label: '许可证')->setRequired(false),
                new ImageField(field: HyCompaniesTable::MEDICINE_PERMIT, label: '药品经营许可证')->setRequired(false),
                new ImageField(field: HyCompaniesTable::QUALIFICATIONS, label: '其他资质')->setMax(8)->setRequired(false),
                new SwitchField(field: HyCompaniesTable::IS_SPECIAL_INVITATION, label: '特邀厂家')->onlyOnList()->setRequired(false),
            );
        } else {
            $fields->setFields(
                new NumberField(field: HyCompaniesTable::ID, label: 'ID')->hideOnForm(),
                new HiddenField(field: HyCompaniesTable::TYPE, label: 'type')->setDefault($type),
                new TextField(field: HyCompaniesTable::NAME, label: '门诊名称'),
                new ImageField(field: HyCompaniesTable::LOGO, label: 'logo'),
                new TextField(field: HyCompaniesTable::PHONE, label: '联系电话')->setRequired(false),
//            new TextField(field: HyCompaniesTable::MAIN_PRODUCTS, label: '主营产品简介'),
                new TextField(field: HyCompaniesTable::INTRODUCTION, label: '门诊详细介绍')->setRequired(false),
                new TextField(field: HyCompaniesTable::ADDRESS, label: '门诊地址')->setRequired(false),
//            new TextField(field: HyCompaniesTable::BANNER_IMAGE, label: '公司背景图片'),
                new Int2TimeField(field: HyCompaniesTable::TIME, label: 'time')->hideOnForm(),
                new SelectField(field: HyCompaniesTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
                new SelectField(field: HyCompaniesTable::STATUS, label: '入驻状态')->setOptions(
                    new OptionManager('pending', '待审核'),
                    new OptionManager('approved', '审核通过'),
                    new OptionManager('disabled', '禁用'),
                    new OptionManager('reject', '审核拒绝'),
                ),
                new ImageField(field: HyCompaniesTable::BUSINESS_LICENSE, label: '营业执照')->setRequired(false),
                new ImageField(field: HyCompaniesTable::PERMIT, label: '许可证')->setRequired(false),
            );
        }


    }
}