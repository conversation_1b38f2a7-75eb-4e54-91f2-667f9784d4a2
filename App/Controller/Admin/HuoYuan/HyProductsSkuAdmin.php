<?php

namespace App\Controller\Admin\HuoYuan;


use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Swlib\Table\TableInterface;
use Throwable;


/*
* 产品信息表
*/

class HyProductsSkuAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '规格信息';
        $config->tableName = HyProductsSkuTable::class;
    }


    /**
     * @throws AppException|Throwable
     */
    public function listsQuery(HyProductsSkuTable $query): void
    {
        $productId = $this->get(HyProductsSkuTable::PRODUCT_ID, '', 0);
        if ($productId > 0) {
            $query->addWhere(HyProductsSkuTable::PRODUCT_ID, $productId);
        }
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $productId = $this->get(HyProductsSkuTable::PRODUCT_ID, '', 0);

        $fields->setFields(
            new NumberField(field: HyProductsSkuTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: HyProductsSkuTable::PRODUCT_ID, label: '产品')->setRelation(
                HyCompaniesServiceTable::class,
                HyCompaniesServiceTable::ID,
                HyCompaniesServiceTable::PRODUCT_NAME
            )->setDefault($productId ?: 0),
            new TextField(field: HyProductsSkuTable::NAME, label: '规格名称'),
            new TextField(field: HyProductsSkuTable::CODE, label: '规格编码')->setRequired(false),
//            new TextField(field: HyProductsSkuTable::DESCRIPTION, label: '规格描述'),
            new TextField(field: HyProductsSkuTable::PRICE, label: '价格')->setRequired(false),
            new SelectField(field: HyProductsSkuTable::IS_DICKER, label: '是否议价')->setOptions(
                new OptionManager(0, '不议价'),
                new OptionManager(1, '议价')
            )->setDefault(0),
            new TextField(field: HyProductsSkuTable::MIN_BUY, label: '起订量'),
            new TextField(field: HyProductsSkuTable::INVENTORY, label: '剩余库存'),
        );
    }

    public function insertBefore(HyProductsSkuTable $table): void
    {
        if (empty($table->price)) {
            $table->price = 0;
        }
    }

    /**
     * @throws Throwable
     */
    public function insertUpdateAfter(HyProductsSkuTable $table): void
    {
        if ($table->productId) {
            new HyCompaniesServiceTable()->where([
                HyCompaniesServiceTable::ID => $table->productId
            ])->update([
                HyCompaniesServiceTable::UPDATED_AT => date('Y-m-d H:i:s')
            ]);
        }

        Event::emit('update.product.minPrice', [
            'productId' => $table->productId
        ]);

    }

}