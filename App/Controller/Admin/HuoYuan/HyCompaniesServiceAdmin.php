<?php

namespace App\Controller\Admin\HuoYuan;


use App\Controller\Api\Dt\HyCompaniesServiceApi;
use App\Service\ExportProductService;
use App\Service\ImportProductService;
use DateMalformedStringException;
use DateTime;
use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\RouterPath;
use Generate\Tables\Datas\CitysTable;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Protobuf\Datas\HyCompaniesService\HyCompaniesServiceTypeEnum;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Action\DownloadAction;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\HiddenField;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectArrayField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextareaField;
use Swlib\Admin\Fields\TextArrayField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Admin\Utils\Func;
use Swlib\Exception\AppException;
use Swlib\Response\JsonResponse;
use Swlib\Router\Router;
use Swlib\Utils\Server;
use Throwable;


/*
* 厂家大厅表,厂家提供那些服务
*/

class HyCompaniesServiceAdmin extends AbstractAdmin
{
    /**
     * @throws AppException
     */
    protected function configPage(PageConfig $config): void
    {
        $typeInt = $this->get(HyCompaniesServiceTable::TYPE, '', '');
        $type = HyCompaniesServiceTypeEnum::name($typeInt);
        $config->setPageName(HyCompaniesServiceModel::TypeTextMaps[strtolower($type)]);
        $config->tableName = HyCompaniesServiceTable::class;

        if ($typeInt == HyCompaniesServiceTypeEnum::CHAN_PIN) {
            $config->order = [
                HyCompaniesServiceTable::UPDATED_AT => 'ASC',
            ];
        }

    }

    /**
     * @throws DateMalformedStringException
     */
    private function timeFilterQuery($query, $value): void
    {

        $datetime = new DateTime($value);
        $currDay = $datetime->format('Y-m-d 00:00:00');
        $query->addWhere(HyCompaniesServiceTable::CREATED_AT, $currDay, '>=');
        $datetime->modify('+1 day');
        $nextDay = $datetime->format('Y-m-d 00:00:00');
        $query->addWhere(HyCompaniesServiceTable::CREATED_AT, $nextDay, '<');
    }

    /**
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {

//        $actions->disabledActions = [ActionDefaultButtonEnum::EDIT];
        $type = $this->get(HyCompaniesServiceTable::TYPE, '', '');
//        $companiesType = $this->get(HyCompaniesTable::TYPE, '', 1);

        if ($type == HyCompaniesServiceTypeEnum::CHAN_PIN) {
            $saveDir = PUBLIC_DIR . 'export/';
            if (!is_dir($saveDir)) {
                mkdir($saveDir, 0777, true);
            }
            $fileName = '产品列表' . date('YmdHis') . '.xlsx';
            $filePath = $saveDir . $fileName;
            $actions->addActions(
                new Action('导入产品', RouterPath::AdminHuoYuanHyCompaniesServiceImport)
                    ->setSort(1)->setTemplate('import.twig')
                    ->onlyIndex(),
                new Action('下载导入模板', '/上传产品模板.xlsx')
                    ->setSort(1)
                    ->onlyIndex(),
                new DownloadAction(label: '导出到表格', url: RouterPath::AdminHuoYuanHyCompaniesServiceExport, params: ['file-path' => $filePath])
                    ->setProgressUrl(RouterPath::AdminHuoYuanHyCompaniesServiceGetExportProgress)
                    ->setCompleteUrl(Func::url("/export/$fileName"))
                    ->setSort(1),
                new Action('规格', RouterPath::AdminHuoYuanHyProductsSkuLists, [
                    HyProductsSkuTable::PRODUCT_ID => "%" . HyCompaniesServiceTable::ID,
                    '_source_url'=>RouterPath::AdminHuoYuanHyCompaniesServiceLists
                ])->showList()
            );
        }
    }

    /**
     * @throws AppException
     */
    #[Router(method: "GET")]
    public function export(): JsonResponse
    {
        $filePath = $this->get('file-path');
        $taskId = ExportProductService::export([
            'filePath' => $filePath,
        ]);
        return JsonResponse::success([
            'msgId' => $taskId
        ]);
    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(method: "GET")]
    public function getExportProgress(): JsonResponse
    {
        $taskId = $this->get('msgId');
        if (empty($taskId)) {
            throw new AppException('任务ID不能为空');
        }

        $progress = ExportProductService::getProgress($taskId);
        if ($progress === null) {
            throw new AppException('任务不存在或已过期');
        }

        return JsonResponse::success([
            // -1 表示执行成功了
            'progress' => $progress >= 100 ? -1 : $progress
        ]);
    }


    /**
     * @throws AppException
     */
    #[Router(method: "POST")]
    public function import(): JsonResponse
    {
        $taskId = ImportProductService::upload();
        return JsonResponse::success([
            'taskId' => $taskId
        ]);
    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(method: "GET")]
    public function getImportProgress(): JsonResponse
    {
        $taskId = $this->get('taskId');
        if (empty($taskId)) {
            throw new AppException('任务ID不能为空');
        }

        $progress = ImportProductService::getProgress($taskId);
        if ($progress === null) {
            throw new AppException('任务不存在或已过期');
        }

        return JsonResponse::success($progress);
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {

        $companiesType = $this->get(HyCompaniesTable::TYPE, '', 1);
        $type = $this->get(HyCompaniesServiceTable::TYPE, '', '');

        if (empty($type)) {
            throw new AppException('参数错误');
        }

        $companiesType = intval($companiesType);
        $type = intval($type);

        $this->listRefreshUrl = Func::url('lists', [
            HyCompaniesServiceTable::TYPE => $type,
            HyCompaniesTable::TYPE => $companiesType,
        ], [], false);

        // companiesType = 1 表示厂家发布(pageType=1), companiesType = 2 表示门诊发布需求(pageType=2)
        if ($companiesType === 1) {
            // 厂家发布产品/服务
            switch ($type) {
                case HyCompaniesServiceTypeEnum::DING_ZHI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new NumberField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '产品介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '产品图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::CHAN_PIN:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '产品价格')->setRequired(false),
//                        new TextField(field: HyCompaniesServiceTable::PRODUCT_SKU, label: '产品规格'),
                        new TextField(field: HyCompaniesServiceTable::SERVICE_PERSON, label: '厂家'),
                        new TextField(field: HyCompaniesServiceTable::UNIT, label: '包装规格'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NO, label: '产品证件号'),
                        new TextField(field: HyCompaniesServiceTable::YI_BAO, label: '医保编码'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '产品介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '产品主图')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '产品详情图片')->setMax(9),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SwitchField(field: HyCompaniesServiceTable::IS_SALE, label: '销售')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::DAI_LI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '设备名称'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '招代理区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '设备介绍'),
                        new TextField(field: HyCompaniesServiceTable::SERVICE_DESC, label: '代理要求'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '设备主图')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '设备详情图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::DA_SHE_BEI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '设备名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '设备介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '设备图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ER_SHOU:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '设备名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
                        new TextField(field: HyCompaniesServiceTable::EXPIRES_AT, label: '有效期'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '设备介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '设备图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::YAO_PIN:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '产品介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '产品图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ZHENG_QI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '产品介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '产品图片')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '产品详情图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ZHONG_ZHI_TI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '销售区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '产品介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '产品图片')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '产品详情图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JIA_GONG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '销售区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '产品介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '产品图片')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '产品详情图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ZHUANG_XIU:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '公司介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '公司图片')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '案例图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::YUN_YIN:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '公司介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '公司图片')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '案例图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::SHE_JI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '公司介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '公司图片')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '案例图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::QIAN_FANG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '公司介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '公司图片')->setMax(9),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '案例图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JIN_RONG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new NumberField(field: HyCompaniesServiceTable::FUNDING_NEED, label: '融资金额'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '公司介绍'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JIAO_YU:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '课程名称'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_CATEGORY, label: '课程分类'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_TYPE, label: '课程形式'),
                        new TextField(field: HyCompaniesServiceTable::START_AT, label: '开课时间'),
                        new TextField(field: HyCompaniesServiceTable::SERVICE_ADDRESS, label: '地址'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '课程介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '课程图片介绍')->setMax(9),
                        new TextField(field: HyCompaniesServiceTable::SERVICE_PERSON, label: '讲师'),
                        new TextField(field: HyCompaniesServiceTable::SERVICE_DESC, label: '讲师介绍'),
                        new TextField(field: HyCompaniesServiceTable::SERVICE_IMAGES, label: '讲师图片介绍'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ZHUAN_RANG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '门诊名称'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '转让价格'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '案例介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '案例图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JIA_MENG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '加盟要求'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '加盟要求图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JING_JIA:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '商品名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '最小预算'),
                        new TextField(field: HyCompaniesServiceTable::PRICE_MAX, label: '最大预算'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '配置要求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::WX_ADD:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '门诊名称'),
                        new TextField(field: HyCompaniesServiceTable::SERVICE_ADDRESS, label: '地址'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextArrayField(field: HyCompaniesServiceTable::SERVICE_DESC, label: '维修产品'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '维修介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '维修图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::INSTALL_ADD:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '安装地点')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextField(field: HyCompaniesServiceTable::COMPANIES_TYPE, label: '所属厂家'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '安装产品'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
            }
        } else {
            // 门诊发布需求 (companiesType = 2, pageType = 2)
            switch ($type) {
                case HyCompaniesServiceTypeEnum::DING_ZHI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new NumberField(field: HyCompaniesServiceTable::PRODUCT_NUM, label: '需求数量'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '产品介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '产品图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JIA_GONG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ZHONG_ZHI_TI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new NumberField(field: HyCompaniesServiceTable::PRODUCT_NUM, label: '需求数量'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ZHENG_QI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ER_SHOU:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '设备名称'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::YUN_YIN:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ZHUANG_XIU:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::SHE_JI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::QIAN_FANG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JIN_RONG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '需求区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊介绍'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::DA_SHE_BEI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '设备名称'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::DAI_LI:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '设备名称'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JIAO_YU:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '课程名称'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::ZHUAN_RANG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '门诊名称'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '所在区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '门诊图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JIA_MENG:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '加盟要求'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '加盟要求图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::YAO_PIN:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '门诊需求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::JING_JIA:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '商品名称'),
                        new TextField(field: HyCompaniesServiceTable::PRICE, label: '最小预算'),
                        new TextField(field: HyCompaniesServiceTable::PRICE_MAX, label: '最大预算'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '配置要求'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::WX_ADD:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '门诊名称'),
                        new TextField(field: HyCompaniesServiceTable::SERVICE_ADDRESS, label: '地址'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextArrayField(field: HyCompaniesServiceTable::SERVICE_DESC, label: '维修产品'),
                        new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '维修介绍'),
                        new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '维修图片')->setMax(9),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
                case HyCompaniesServiceTypeEnum::INSTALL_ADD:
                    $fields->setFields(
                        new HiddenField(HyCompaniesTable::TYPE, '厂家类型')->setDefault($companiesType),
                        new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '门诊')
                            ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                            ->setAddQuery(function ($query) use ($companiesType) {
                                $query->addWhere(HyCompaniesTable::TYPE, $companiesType);
                            }),
                        new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '安装地点')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
                        new TextField(field: HyCompaniesServiceTable::COMPANIES_TYPE, label: '所属厂家'),
                        new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '安装产品'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
                        new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
                        new HiddenField(field: HyCompaniesServiceTable::TYPE, label: '发布类型')->setDefault($type)->setFilterQuery(function ($query, $value) {
                            $query->addWhere(HyCompaniesServiceTable::TYPE, HyCompaniesServiceTypeEnum::name(intval($value)));
                        }),
                        new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnList(),
                        new SelectField(field: HyCompaniesServiceTable::IS_PUB, label: '上架')->onlyOnFilter()->setOptions(
                            new OptionManager(1, '上架'),
                            new OptionManager(0, '下架'),
                        ),
                        new Int2TimeField(field: HyCompaniesServiceTable::CREATED_AT, label: '时间')->hideOnForm()->setFilterQuery(function ($query, $value) {
                            $this->timeFilterQuery($query, $value);
                        }),
                    );
                    break;
            }
        }

//        $fields->setFields(
//            new NumberField(field: HyCompaniesServiceTable::ID, label: 'ID')->hideOnForm(),
//            new SelectField(field: HyCompaniesServiceTable::COMPANIES_ID, label: '厂家ID')->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME),
//            new SelectField(field: HyCompaniesServiceTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
//            new SelectField(field: HyCompaniesServiceTable::TYPE, label: '类型')->setOptions(
//                new OptionManager(HyCompaniesServiceModel::TypeDing_zhi, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeDing_zhi]),
//                new OptionManager(HyCompaniesServiceModel::TypeJia_gong, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeJia_gong]),
//                new OptionManager(HyCompaniesServiceModel::TypeZhong_zhi_ti, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeZhong_zhi_ti]),
//                new OptionManager(HyCompaniesServiceModel::TypeZheng_qi, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeZheng_qi]),
//                new OptionManager(HyCompaniesServiceModel::TypeEr_shou, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeEr_shou]),
//                new OptionManager(HyCompaniesServiceModel::TypeYun_yin, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeYun_yin]),
//                new OptionManager(HyCompaniesServiceModel::TypeZhuang_xiu, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeZhuang_xiu]),
//                new OptionManager(HyCompaniesServiceModel::TypeShe_ji, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeShe_ji]),
//                new OptionManager(HyCompaniesServiceModel::TypeQian_fang, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeQian_fang]),
//                new OptionManager(HyCompaniesServiceModel::TypeJin_rong, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeJin_rong]),
//                new OptionManager(HyCompaniesServiceModel::TypeDa_she_bei, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeDa_she_bei]),
//                new OptionManager(HyCompaniesServiceModel::TypeDai_li, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeDai_li]),
//                new OptionManager(HyCompaniesServiceModel::TypeJiao_yu, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeJiao_yu]),
//                new OptionManager(HyCompaniesServiceModel::TypeZhuan_rang, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeZhuan_rang]),
//                new OptionManager(HyCompaniesServiceModel::TypeJia_meng, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeJia_meng]),
//                new OptionManager(HyCompaniesServiceModel::TypeYao_pin, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeYao_pin]),
//                new OptionManager(HyCompaniesServiceModel::TypeJing_jia, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeJing_jia]),
//                new OptionManager(HyCompaniesServiceModel::TypeChan_pin, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeChan_pin]),
//                new OptionManager(HyCompaniesServiceModel::TypeWx_add, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeWx_add]),
//                new OptionManager(HyCompaniesServiceModel::TypeInstall_add, HyCompaniesServiceModel::TypeTextMaps[HyCompaniesServiceModel::TypeInstall_add]),
//            ),
//            new TextField(field: HyCompaniesServiceTable::CONTACT_PERSON, label: '联系人'),
//            new TextField(field: HyCompaniesServiceTable::CONTACT_PHONE, label: '联系电话'),
//            new SelectArrayField(field: HyCompaniesServiceTable::SERVICE_AREA, label: '区域')->setListMaxWidth(200)->setRelation(CitysTable::class, CitysTable::ID, CitysTable::NAME),
//            new TextField(field: HyCompaniesServiceTable::SERVICE_ADDRESS, label: '详细地址'),
//            new TextareaField(field: HyCompaniesServiceTable::DESCRIPTION, label: '介绍'),
//            new ImageField(field: HyCompaniesServiceTable::IMAGES, label: '主图')->setMax(9),
//            new ImageField(field: HyCompaniesServiceTable::IMAGES_DESC, label: '详情图')->setMax(9),
//            new SwitchField(field: HyCompaniesServiceTable::IS_PUB, label: '是否发布'),
//            new NumberField(field: HyCompaniesServiceTable::VIEWS, label: '浏览量'),
//            new NumberField(field: HyCompaniesServiceTable::FAVORITE_COUNT, label: '收藏数量'),
//            new TextField(field: HyCompaniesServiceTable::START_AT, label: '开始时间'),
//            new TextField(field: HyCompaniesServiceTable::EXPIRES_AT, label: '过期时间'),
//            new NumberField(field: HyCompaniesServiceTable::FUNDING_NEED, label: '资金需求，单位万'),
//            new TextField(field: HyCompaniesServiceTable::COMPANIES_TYPE, label: '企业类型'),
//            new TextField(field: HyCompaniesServiceTable::PRODUCT_NAME, label: '产品名称'),
//            new TextField(field: HyCompaniesServiceTable::PRODUCT_CATEGORY, label: '产品分类'),
//            new TextField(field: HyCompaniesServiceTable::PRODUCT_TYPE, label: '产品分类2'),
//            new NumberField(field: HyCompaniesServiceTable::PRODUCT_SIZE, label: '产品大小'),
//            new NumberField(field: HyCompaniesServiceTable::PRODUCT_NUM, label: '产品数量'),
//            new TextField(field: HyCompaniesServiceTable::PRODUCT_SKU, label: '产品规格'),
//            new TextField(field: HyCompaniesServiceTable::PRODUCT_NO, label: '产品证件号'),
//            new SwitchField(field: HyCompaniesServiceTable::IS_TOP, label: '是否置顶'),
//            new SwitchField(field: HyCompaniesServiceTable::IS_BANNER, label: '是否显示成 顶部 banner'),
//            new ImageField(field: HyCompaniesServiceTable::BANNER_IMG, label: 'banner图'),
//            new NumberField(field: HyCompaniesServiceTable::DISPLAY_SORT, label: '排序'),
//            new TextField(field: HyCompaniesServiceTable::PRICE, label: '价格'),
//            new TextField(field: HyCompaniesServiceTable::PRICE_MAX, label: '最高价格'),
//            new TextField(field: HyCompaniesServiceTable::SERVICE_PERSON, label: '服务人名称'),
//            new TextField(field: HyCompaniesServiceTable::SERVICE_DESC, label: '服务人介绍'),
//            new TextField(field: HyCompaniesServiceTable::SERVICE_IMAGES, label: '服务人图片列表 逗号分割'),
//            new TextField(field: HyCompaniesServiceTable::CREATED_AT, label: '创建时间'),
//            new TextField(field: HyCompaniesServiceTable::UPDATED_AT, label: '更新时间'),
//            new TextField(field: HyCompaniesServiceTable::YI_BAO, label: '医保编码'),
//        );
    }


    /**
     * @throws Throwable
     */
    public function insertUpdateBefore(HyCompaniesServiceTable $table): void
    {
        $userId = new HyCompaniesTable()->where([
            HyCompaniesTable::ID => $table->companiesId,
        ])->selectField(HyCompaniesTable::USER_ID);
        $table->userId = $userId;
    }


    public function updateAfter(HyCompaniesServiceTable $table): void
    {
        try {
            Server::task([HyCompaniesServiceApi::class, 'countSaleNum'], ['companiesId' => $table->companiesId]);
        } catch (AppException) {

        }
    }

    /**
     * @throws Throwable
     */
    public function join(HyCompaniesServiceTable $query): void
    {

        $companiesType = $this->get(HyCompaniesTable::TYPE, '', 1);
//        $type = $this->get(HyCompaniesServiceTable::TYPE, '', '');
//        $query->addWhere(HyCompaniesServiceTable::TYPE, strtolower(HyCompaniesServiceTypeEnum::name($type)));
//        $query->join(HyCompaniesTable::TABLE_NAME, HyCompaniesTable::ID, HyCompaniesServiceTable::COMPANIES_ID);
        $query->addWhere(HyCompaniesTable::TYPE, $companiesType);

        $query->join(HyCompaniesTable::TABLE_NAME, HyCompaniesTable::ID, HyCompaniesServiceTable::COMPANIES_ID);
    }

}