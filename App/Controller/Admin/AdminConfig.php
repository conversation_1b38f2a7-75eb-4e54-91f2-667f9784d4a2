<?php

namespace App\Controller\Admin;

use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\RouterPath;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\WxEngineerTable;
use Generate\Tables\Datas\WxServiceTable;
use Protobuf\Datas\HyCompaniesService\HyCompaniesServiceTypeEnum;
use Swlib\Admin\Config\AdminConfigAbstract;
use Swlib\Admin\Manager\AdminManager;
use Swlib\Admin\Menu\Menu;
use Swlib\Admin\Menu\MenuGroup;

use Throwable;


class AdminConfig extends AdminConfigAbstract
{

    public function Init(AdminManager $layout): void
    {
        // 上传文件地址
        $layout->uploadUrl = RouterPath::ToolAliUpload;

        // 首页地址
        $layout->adminIndexUrl = RouterPath::AdminDashboardIndex;

        // 退出登录路由，后台模板中有调用，再页面最右上角
        $layout->logoutUrl = RouterPath::AdminLoginLogout;
        $layout->loginUrl = RouterPath::AdminLoginLogin;

        // 退出登录路由，后台模板中有调用，再页面最右上角
        $layout->changePasswordUrl = RouterPath::AdminLoginChangePassword;

        // 没有权限页面
        $layout->noAccessUrl = RouterPath::AdminDashboardNoAccess;

        // 设置语言路由
        $layout->setLanguageUrl = RouterPath::AdminLanguageSetLanguage;
    }

    /**
     * @throws Throwable
     */
    public function configAdminTitle(): string
    {
        return '口小白';
    }

    /**
     * @return MenuGroup[]|Menu[]
     * @throws Throwable
     */
    public function configMenus(): array
    {
        return [
            new MenuGroup(label: '招聘管理', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '门诊认证', url: RouterPath::AdminZpZpCompaniesLists),
                new Menu(label: '招聘职位', url: RouterPath::AdminZpZpJobsLists),
                new Menu(label: '企业关注', url: RouterPath::AdminZpZpCompaniesFocusLists),
                new Menu(label: '职位申请', url: RouterPath::AdminZpZpJobsApplyLists),
                new Menu(label: '职位收藏', url: RouterPath::AdminZpZpJobsCollectLists),
                new Menu(label: '职位查看', url: RouterPath::AdminZpZpJobsUserSeeLists),
                new Menu(label: '求职意向', url: RouterPath::AdminZpZpJobIntentionLists),
                new Menu(label: '职位级别', url: RouterPath::AdminZpZpJobsLevelLists),
                new Menu(label: '职位标签', url: RouterPath::AdminZpZpTagLists),
                new Menu(label: '职位类型', url: RouterPath::AdminZpZpJobsTypeLists),
                new Menu(label: '用户简历', url: RouterPath::AdminZpZpResumeLists),
                new Menu(label: '特长管理', url: RouterPath::AdminZpSpecialityLists),
                new Menu(label: '用户选择特长', url: RouterPath::AdminZpUserSpecialityLists),
                new Menu(label: '用户不喜欢', url: RouterPath::AdminZpDislikeLists),
            ),

            new MenuGroup(label: '维修管理', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '擅长领域', url: RouterPath::AdminWxWxFieldLists),
                new Menu(label: '项目管理', url: RouterPath::AdminWxWxProjectLists),

                new Menu(label: '维修需求列表', url: RouterPath::AdminWxWxServiceLists, params: [WxServiceTable::TYPE => 'wx']),
                new Menu(label: '安装需求列表', url: RouterPath::AdminWxWxServiceLists, params: [WxServiceTable::TYPE => 'install']),
                new Menu(label: '订单管理', url: RouterPath::AdminWxWxOrderLists),
            ),
            new MenuGroup(label: '竞价', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '竞价商品', url: RouterPath::AdminBiddingBiddingShopLists),
                new Menu(label: '竞价日志', url: RouterPath::AdminBiddingBiddingLogLists),
            ),
            new MenuGroup(label: '基本信息', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '药品名称', url: RouterPath::AdminDrugNamesLists),
            ),

            new MenuGroup(label: '会员认证', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '认证等级', url: RouterPath::AdminAuthAuthLevelsLists ),
                new Menu(label: '会员认证', url: RouterPath::AdminAuthAuthUserInfoLists),
            ),

            new MenuGroup(label: '入驻管理', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '厂商入驻', url: RouterPath::AdminHuoYuanHyCompaniesLists, params: [HyCompaniesTable::TYPE => 1]),
                new Menu(label: '门诊入驻', url: RouterPath::AdminHuoYuanHyCompaniesLists, params: [HyCompaniesTable::TYPE => 2]),
                new Menu(label: '工程师入驻', url: RouterPath::AdminWxWxEngineerLists,),
            ),

            new MenuGroup(label: '厂家发布', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '定制大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::DING_ZHI]),
                new Menu(label: '产品大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::CHAN_PIN]),
                new Menu(label: '代理大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::DAI_LI]),
                new Menu(label: '大设备大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::DA_SHE_BEI]),
                new Menu(label: '二手大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ER_SHOU]),
                new Menu(label: '药品大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::YAO_PIN]),
                new Menu(label: '正畸大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ZHENG_QI]),
                new Menu(label: '种植体大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ZHONG_ZHI_TI]),
                new Menu(label: '加工大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JIA_GONG]),
                new Menu(label: '装修大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ZHUANG_XIU]),
                new Menu(label: '运营大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::YUN_YIN]),
                new Menu(label: '设计大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::SHE_JI]),
                new Menu(label: '前方大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::QIAN_FANG]),
                new Menu(label: '金融大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JIN_RONG]),
                new Menu(label: '教育大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JIAO_YU]),
                new Menu(label: '转让大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ZHUAN_RANG]),
                new Menu(label: '加盟大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JIA_MENG]),
                new Menu(label: '竞价大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JING_JIA]),
                new Menu(label: '维修大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::WX_ADD]),
                new Menu(label: '安装大厅', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 1, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::INSTALL_ADD]),
            ),
            new MenuGroup(label: '门诊发布', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '定制需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::DING_ZHI]),
//                new Menu(label: '产品需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::CHAN_PIN]),
                new Menu(label: '代理需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::DAI_LI]),
                new Menu(label: '大设备需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::DA_SHE_BEI]),
                new Menu(label: '二手需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ER_SHOU]),
                new Menu(label: '药品需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::YAO_PIN]),
                new Menu(label: '正畸需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ZHENG_QI]),
                new Menu(label: '种植体需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ZHONG_ZHI_TI]),
                new Menu(label: '加工需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JIA_GONG]),
                new Menu(label: '装修需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ZHUANG_XIU]),
                new Menu(label: '运营需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::YUN_YIN]),
                new Menu(label: '设计需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::SHE_JI]),
                new Menu(label: '前方需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::QIAN_FANG]),
                new Menu(label: '金融需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JIN_RONG]),
                new Menu(label: '教育需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JIAO_YU]),
                new Menu(label: '转让需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::ZHUAN_RANG]),
                new Menu(label: '加盟需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JIA_MENG]),
                new Menu(label: '竞价需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::JING_JIA]),
                new Menu(label: '维修需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::WX_ADD]),
                new Menu(label: '安装需求', url: RouterPath::AdminHuoYuanHyCompaniesServiceLists, params: [HyCompaniesTable::TYPE => 2, HyCompaniesServiceTable::TYPE => HyCompaniesServiceTypeEnum::INSTALL_ADD]),
            ),

            new MenuGroup(label: '商城管理', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '商品品牌', url: RouterPath::AdminSystemBrandLists),
                new Menu(label: '商品管理', url: RouterPath::AdminShopShopProductsLists),
                new Menu(label: '商品分类', url: RouterPath::AdminShopShopCategoriesLists),
                new Menu(label: '商品SKU', url: RouterPath::AdminShopShopProductsSkuLists),
                new Menu(label: '商品标签', url: RouterPath::AdminShopShopProductTagLists),
                new Menu(label: '商品收藏', url: RouterPath::AdminShopCollectCountLists),
                new Menu(label: '购物车', url: RouterPath::AdminShopShopCarLists),
                new Menu(label: '商品搜索历史', url: RouterPath::AdminUserHistorySearchLists),
                new Menu(label: '商品浏览记录', url: RouterPath::AdminShopFootprintLists),
                new Menu(label: '用户收藏商品', url: RouterPath::AdminShopCollectLists),
                new Menu(label: '用户商品评价', url: RouterPath::AdminShopCommentLists),
                new Menu(label: '用户购买统计', url: RouterPath::AdminShopShopProductByCountLists),
                new Menu(label: '用户优惠券', url: RouterPath::AdminShopCouponUserLists),
                new Menu(label: '优惠券管理', url: RouterPath::AdminShopCouponLists),
                new Menu(label: '商户分类', url: RouterPath::AdminShopBusinessCategoryLists),
                new Menu(label: '商户列表', url: RouterPath::AdminShopBusinessLists),
            ),


            new MenuGroup(label: '报价', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '来源管理', url: RouterPath::AdminShopSourceLists),
                new Menu(label: '报价政策', url: RouterPath::AdminShopParityPolicyLists),
                new Menu(label: '采集商品分类', url: RouterPath::AdminShopCategoriesLists),
                new Menu(label: '采集商品', url: RouterPath::AdminShopProductsLists),
                new Menu(label: '采集商品SKU', url: RouterPath::AdminShopProductSkuLists),
                new Menu(label: '关联商品', url: RouterPath::AdminShopParityProductsLists),
                new Menu(label: '关联商品SKU', url: RouterPath::AdminShopParityProductsSkuLists),
            ),

            new MenuGroup(label: '用户管理', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '用户列表', url: RouterPath::AdminUserLists),
                new Menu(label: '收货地址', url: RouterPath::AdminUserUserAddressLists),
                new Menu(label: '新用户红包配置', url: RouterPath::AdminUserHongBaoLists),
                new Menu(label: '红包领取记录', url: RouterPath::AdminUserUserHongBaoLists),
                new Menu(label: '联系客服', url: 'https://static.zhonguoyagu.com/uni-app/web/index.html#/pages/user/kf-login'),
            ),

            new MenuGroup(label: '发票管理', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '发票抬头', url: RouterPath::AdminOrderInvoiceTitleLists),
                new Menu(label: '开票申请', url: RouterPath::AdminOrderInvoiceLists),
            ),
            new MenuGroup(label: '官网配置', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '位置管理', url: RouterPath::AdminPcPcConfigPosLists),
                new Menu(label: '官网配置', url: RouterPath::AdminPcPcConfigLists),
                new Menu(label: '官网留言', url: RouterPath::AdminPcPcFeedbackLists),
            ),

            new MenuGroup(label: '订单管理', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '订单管理', url: RouterPath::AdminOrderLists),
                new Menu(label: '退款管理', url: RouterPath::AdminOrderOrderRefundLists),
            ),


            new MenuGroup(label: '广告管理', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '广告位置', url: RouterPath::AdminSystemBannerBannerPosLists),
                new Menu(label: '广告管理', url: RouterPath::AdminSystemBannerBannerAdLists),
            ),


            new MenuGroup(label: '系统', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '管理员', url: RouterPath::AdminSystemAdminManagerLists),
                new Menu(label: '意见反馈', url: RouterPath::AdminSystemFeedbackLists),
                new Menu(label: '系统配置', url: RouterPath::AdminSystemConfigLists),
                new Menu(label: '文章内容', url: RouterPath::AdminSystemArticleLists),
                new Menu(label: '国家管理', url: RouterPath::AdminSystemCountriesLists),
                new Menu(label: '翻译配置', url: RouterPath::AdminLanguageLists),
                new Menu(label: '城市管理', url: RouterPath::AdminSystemCityLists),
            ),
            new MenuGroup(label: 'VR', icon: 'bi bi-chevron-double-right')->setMenus(
                new Menu(label: '场景管理', url: RouterPath::AdminVrVrSceneLists),
                new Menu(label: '场景标注', url: RouterPath::AdminVrVrTagSceneLists),
            ),


        ];
    }

}