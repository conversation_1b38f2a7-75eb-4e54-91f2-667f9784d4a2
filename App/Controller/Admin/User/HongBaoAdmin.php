<?php
namespace App\Controller\Admin\User;


use Generate\Tables\Datas\HongBaoTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


/*
* 新用户红包
*/
class HongBaoAdmin extends AbstractAdmin{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '新用户红包';
        $config->tableName = HongBaoTable::class;
    }
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: HongBaoTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: HongBaoTable::PRICE, label: '红包金额 不能小于1元 否则微信转账失败'),
            new TextField(field: HongBaoTable::NAME, label: '红包描述'),
            new ImageField(field: HongBaoTable::ICON, label: '红包图标'),
            new TextField(field: HongBaoTable::RATIO, label: '抽中概率：万分之'),
        );
    }
}