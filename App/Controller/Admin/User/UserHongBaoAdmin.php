<?php
namespace App\Controller\Admin\User;


use Generate\Tables\Datas\UserHongBaoTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Throwable;


/*
* 新用户红包
*/
class UserHongBaoAdmin extends AbstractAdmin{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '新用户红包';
        $config->tableName = UserHongBaoTable::class;
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: UserHongBaoTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: UserHongBaoTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new TextField(field: UserHongBaoTable::PRICE, label: '抽中的红包金额'),
            new Int2TimeField(field: UserHongBaoTable::TIME, label: '抽奖时间'),
        );
    }
}