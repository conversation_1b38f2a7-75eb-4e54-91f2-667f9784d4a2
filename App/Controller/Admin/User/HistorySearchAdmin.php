<?php

namespace App\Controller\Admin\User;

use Generate\Tables\Datas\HistorySearchTable;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Enum\ActionDefaultButtonEnum;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class HistorySearchAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "搜索历史";
        $config->tableName = HistorySearchTable::class;
        $config->order = [
            HistorySearchTable::NUM => 'desc'
        ];
    }

    protected function configAction(ActionsConfig $actions): void
    {
        $actions->disabledActions = [ActionDefaultButtonEnum::NEW];
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: HistorySearchTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: HistorySearchTable::KEYWORD, label: '搜索关键字'),
            new TextField(field: HistorySearchTable::NUM, label: '搜索次数'),
            new NumberField(field: HistorySearchTable::USER_ID, label: '用户ID'),
            new Int2TimeField(field: HistorySearchTable::TIME, label: '搜索时间')->hideOnFilter(),
        );
    }

}