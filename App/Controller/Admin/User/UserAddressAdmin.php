<?php

namespace App\Controller\Admin\User;

use Generate\Tables\Datas\AddressTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class UserAddressAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "收货地址";
        $config->tableName = AddressTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: AddressTable::ID, label: 'ID')->hideOnForm(),

            new SelectField(field: AddressTable::USER_ID, label: '用户')
                ->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE)
            ,

            new TextField(field: AddressTable::USERNAME, label: '昵称'),
            new TextField(field: AddressTable::PHONE, label: '手机'),
            new TextField(field: AddressTable::ADDR, label: '地址'),
            new SwitchField(field: AddressTable::IS_DEFAULT, label: '默认')->setDisabled(true),
        );
    }

}