<?php

namespace App\Controller\Admin\User;

use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\EmailField;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class UserAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "用户列表";
        $config->tableName = UserTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: UserTable::ID, label: 'ID')->hideOnForm(),
            new ImageField(field: UserTable::HEAD, label: '头像')->setRequired(false)->hideOnFilter(),
            new TextField(field: UserTable::NICKNAME, label: '昵称')->setRequired(false),
            new EmailField(field: UserTable::EMAIL, label: '邮箱')->setRequired(false),
            new TextField(field: UserTable::USERNAME, label: '账号')->setRequired(false),
            new TextField(field: UserTable::PHONE, label: '手机')->setRequired(false),
            new Int2TimeField(field: UserTable::REG_TIME, label: '注册时间')->setFilterRange(true)->hideOnForm(),
        );
    }

}