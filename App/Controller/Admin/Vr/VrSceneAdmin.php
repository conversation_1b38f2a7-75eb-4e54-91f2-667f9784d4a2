<?php

namespace App\Controller\Admin\Vr;

use Generate\Tables\Datas\VrSceneTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class VrSceneAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "VR场景";
        $config->tableName = VrSceneTable::class;
    }

    /**
     * @throws Throwable
     */
    protected function configAction(ActionsConfig $actions): void
    {
        $actions->addActions(
            new Action('查看VR场景', "https://static.zhonguoyagu.com/vrr_dist/index.html",['edit'=>1])
                ->showList()->setSort(2)->setTarget('_blank')
        );
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: VrSceneTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: VrSceneTable::NAME, label: '名称'),
            new ImageField(field: VrSceneTable::THUMBNAIL, label: '缩略图'),
            new TextField(field: VrSceneTable::PANORAMA, label: '场景标识'),
            new NumberField(field: VrSceneTable::SORT, label: '排序'),
        );
    }
}