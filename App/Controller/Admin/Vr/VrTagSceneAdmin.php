<?php

namespace App\Controller\Admin\Vr;

use App\Model\VrTagModel;
use Generate\Tables\Datas\VrSceneTable;
use Generate\Tables\Datas\VrTagTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\EditorField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Fields\UrlField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;

class VrTagSceneAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "场景标注";
        $config->tableName = VrTagTable::class;

        $config->addJsFile('/pages/js/vr-tag.js');

    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {

        $scene_id = $this->get('scene_id', '', '');
        $position_yaw = $this->get('position_yaw', '', '');
        $position_pitch = $this->get('position_pitch', '', '');

        $types = [];
        foreach (VrTagModel::TYPES as $key => $value) {
            $types[] = new OptionManager($key, $value);
        }


        $fields->setFields(
            new NumberField(field: VrTagTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: VrTagTable::SCENE_ID, label: '场景')
                ->setRelation(VrSceneTable::class, VrSceneTable::ID, VrSceneTable::NAME)->setDefault($scene_id),
            new TextField(field: VrTagTable::NAME, label: '名称'),
            new TextField(field: VrTagTable::POSITION_YAW, label: '位置Yaw')->setDefault($position_yaw),
            new TextField(field: VrTagTable::POSITION_PITCH, label: '位置Pitch')->setDefault($position_pitch),
            new SelectField(field: VrTagTable::TYPE, label: '类型')->setOptions(...$types),
            new EditorField(field: VrTagTable::CONTENT, label: '内容')->hideOnList()->setRequired(false),
            new SelectField(field: VrTagTable::TO_SCENE_ID, label: '切换到场景')->setRequired(false)
                ->setRelation(VrSceneTable::class, VrSceneTable::ID, VrSceneTable::NAME),
            new UrlField(field: VrTagTable::URL, label: 'URL')->setRequired(false),
        );
    }

}