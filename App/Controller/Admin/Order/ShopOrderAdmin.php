<?php

namespace App\Controller\Admin\Order;

use Exception;
use Generate\RouterPath;
use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\InvoiceTable;
use Generate\Tables\Datas\ShopOrderTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Enum\ActionDefaultButtonEnum;
use Swlib\Admin\Fields\HiddenField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Table\TableInterface;
use Throwable;

class ShopOrderAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "订单明细";
        $config->tableName = ShopOrderTable::class;
    }


    /**
     * @throws Exception
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {
        $actions->disabledActions = [ActionDefaultButtonEnum::NEW];
        $actions->addActions(
            new Action('添加', RouterPath::AdminOrderShopOrderNew, [
                ShopOrderTable::ORDER_ID => $this->get(ShopOrderTable::ORDER_ID)
            ])
        );
    }

    /**
     * @throws Throwable
     */
    public function listsQuery(TableInterface $query): void
    {
        /** @var ShopOrderTable $query */
        $query->addWhere(ShopOrderTable::ORDER_ID, $this->get(ShopOrderTable::ORDER_ID));
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ShopOrderTable::ID, label: 'ID')->hideOnForm(),
            new HiddenField(field: ShopOrderTable::ORDER_ID, label: '订单ID'),
            new SelectField(field: ShopOrderTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: ShopOrderTable::BUSINESS_ID, label: '商品商家')->setRelation(BusinessTable::class, BusinessTable::ID, BusinessTable::BUSINESS_NAME),
            new SelectField(field: ShopOrderTable::PRODUCT_ID, label: '产品')->setRelation(ShopProductsTable::class, ShopProductsTable::ID, ShopProductsTable::NAME),
            new SelectField(field: ShopOrderTable::SKU_ID, label: '规格')->setRelation(ShopProductsSkuTable::class, ShopProductsSkuTable::ID, ShopProductsSkuTable::NAME),
            new TextField(field: ShopOrderTable::SN, label: '订单号'),
            new TextField(field: ShopOrderTable::PRICE, label: '商品价格'),
            new NumberField(field: ShopOrderTable::NUM, label: '购买数量'),
            new Int2TimeField(field: ShopOrderTable::TIME, label: '下单时间'),
            new SelectField(field: ShopOrderTable::INVOICE_ID, label: '开票时间')->setRelation(InvoiceTable::class, InvoiceTable::ID, InvoiceTable::TIME),
        );
    }
}