<?php

namespace App\Controller\Admin\Order;

use Generate\Tables\Datas\InvoiceTable;
use Generate\Tables\Datas\InvoiceTitleTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;

class InvoiceAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "开票申请";
        $config->tableName = InvoiceTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: InvoiceTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: InvoiceTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: InvoiceTable::INVOICE_TITLE_ID, label: '发票抬头')->setRelation(InvoiceTitleTable::class, InvoiceTitleTable::ID, InvoiceTitleTable::HEADER),
            new SelectField(field: InvoiceTable::TYPE, label: '发票类型')->setOptions(
                new OptionManager(1, '增值税普通发票'),
                new OptionManager(2, '增值税专用发票')
            ),
            new TextField(field: InvoiceTable::NOTES, label: '开票备注'),
            new Int2TimeField(field: InvoiceTable::TIME, label: '申请时间'),
            new TextField(field: InvoiceTable::PRICE, label: '开票金额'),
            new ImageField(field: InvoiceTable::PIC, label: '发票上传图片')->setRequired(false),
        );
    }
}