<?php

namespace App\Controller\Admin\Order;

use Generate\Tables\Datas\InvoiceTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class Invoice2Admin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "开票申请";
        $config->tableName = InvoiceTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: InvoiceTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: InvoiceTable::NOTES, label: '开票备注'),
            new Int2TimeField(field: InvoiceTable::TIME, label: '申请时间')
        );
    }
}