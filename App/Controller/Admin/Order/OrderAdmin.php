<?php

namespace App\Controller\Admin\Order;

use Generate\RouterPath;
use Generate\Tables\Datas\AddressTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\ShopOrderTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;

class OrderAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "订单管理";
        $config->tableName = OrderTable::class;
    }

    /**
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {
        $actions->addActions(
            new Action("订单明细", RouterPath::AdminOrderShopOrderLists, [
                ShopOrderTable::ORDER_ID => "%" . OrderTable::ID,
            ])->showList()
        );
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: OrderTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: OrderTable::SN, label: '订单号'),
            new TextField(field: OrderTable::ORIGINAL_PRICE, label: '金额'),
            new TextField(field: OrderTable::PRICE, label: '付款金额'),
            new SelectField(field: OrderTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new Int2TimeField(field: OrderTable::TIME, label: '下单时间'),
            new SelectField(field: OrderTable::ORDER_STATUS, label: '状态')->setOptions(
                new OptionManager(0, ''),
                new OptionManager(1, '待付款'),
                new OptionManager(2, '待发货'),
                new OptionManager(3, '待收货'),
                new OptionManager(4, '退换货'),
                new OptionManager(5, '已取消'),
                new OptionManager(6, '已超时'),
                new OptionManager(7, '退货完成'),
                new OptionManager(8, '订单完成'),
            ),
            new Int2TimeField(field: OrderTable::COMPLETE_TIME, label: '订单完成时间'),
            new TextField(field: OrderTable::MARK, label: '下单备注'),
            new SelectField(field: OrderTable::PAY_TYPE, label: '支付方式')->setOptions(
                new OptionManager(1, '支付宝支付'),
                new OptionManager(2, '微信支付')
            ),
            new Int2TimeField(field: OrderTable::PAY_TIME, label: '支付时间'),
            new TextField(field: OrderTable::PAY_NO, label: '支付流水号'),
            new SelectField(field: OrderTable::ADDR_ID, label: '收货地址')->setRelation(AddressTable::class, AddressTable::ID, AddressTable::ADDR),
        );
    }
}