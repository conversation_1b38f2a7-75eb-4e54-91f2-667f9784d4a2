<?php

namespace App\Controller\Admin\Order;

use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Table\TableInterface;
use Throwable;

class OrderRefundAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "退款订单";
        $config->tableName = OrderTable::class;
    }


    /**
     * @throws Throwable
     */
    public function listsQuery(TableInterface $query): void
    {
        // 只查询退款订单
        /** @var OrderTable $query */
        $query->addWhere(OrderTable::ORDER_STATUS, [4, 7], 'in');
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: OrderTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: OrderTable::SN, label: '订单号'),
            new TextField(field: OrderTable::PRICE, label: '付款金额'),
            new SelectField(field: OrderTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new Int2TimeField(field: OrderTable::REFUND_TIME, label: '申请退款时间'),
            new TextField(field: OrderTable::REFUND_AMOUNT, label: '申请退款金额'),
            new Int2TimeField(field: OrderTable::REFUND_HANDLE_TIME, label: '退款处理时间'),
            new Int2TimeField(field: OrderTable::REFUND_COMPLETE_TIME, label: '退款完成时间'),
            new TextField(field: OrderTable::REFUND_NO, label: '退款流水号'),
            new TextField(field: OrderTable::REFUND_NO, label: '退款流水号'),
            new SelectField(field: OrderTable::REFUND_STATUS, label: '退款状态')->setOptions(
            /**
             * 0:未发起退款
             * 1：用户申请退款
             * 2：审核通过，等待发起退款
             * 3：审核不通过，
             * 4: 审核通过 已经发起退款，尚未返回成功,需要调用查询，是否退款成功
             * 5: 审核通过，完成退款
             * 这里是用户申请退款以后，管理员审核通过后的状态
             */
                new OptionManager(0, '未发起退款'),
                new OptionManager(1, '用户申请退款'),
                new OptionManager(2, '审核通过，等待发起退款'),
                new OptionManager(3, '审核不通过'),
                new OptionManager(4, '审核通过 已经发起退款'),
                new OptionManager(5, '审核通过，完成退款'),
            ),
            new TextField(field: OrderTable::REFUND_MSG, label: '拒绝退款原因'),
        );
    }
}