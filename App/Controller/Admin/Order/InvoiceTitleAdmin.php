<?php

namespace App\Controller\Admin\Order;

use Generate\Tables\Datas\InvoiceTitleTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class InvoiceTitleAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "发票抬头";
        $config->tableName = InvoiceTitleTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: InvoiceTitleTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: InvoiceTitleTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new TextField(field: InvoiceTitleTable::EMAIL, label: '接收邮箱'),
            new TextField(field: InvoiceTitleTable::HEADER, label: '企业抬头'),
            new TextField(field: InvoiceTitleTable::NUM, label: '纳税人识别号'),
        );
    }
}