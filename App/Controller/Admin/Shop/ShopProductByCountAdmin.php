<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\ShopProductByCountTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class ShopProductByCountAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "用户购买统计";
        $config->tableName = ShopProductByCountTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ShopProductByCountTable::ID, label: 'ID')->hideOnForm(),
            new NumberField(field: ShopProductByCountTable::COUNT, label: '购买数量'),
            new TextField(field: ShopProductByCountTable::PRICE, label: '消费总金额'),
            new SelectField(field: ShopProductByCountTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: ShopProductByCountTable::BUSINESS_ID, label: '商家')->setRelation(BusinessTable::class, BusinessTable::ID, BusinessTable::BUSINESS_NAME),
            new SelectField(field: ShopProductByCountTable::PRODUCT_ID, label: '产品')->setRelation(ShopProductsTable::class, ShopProductsTable::ID, ShopProductsTable::NAME),
            new SelectField(field: ShopProductByCountTable::SKU_ID, label: '规格')->setRelation(ShopProductsSkuTable::class, ShopProductsSkuTable::ID, ShopProductsSkuTable::NAME),
        );
    }
}