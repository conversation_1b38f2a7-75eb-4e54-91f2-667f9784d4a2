<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\CouponTable;
use Generate\Tables\Datas\CouponUserTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;

class CouponUserAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "用户优惠券";
        $config->tableName = CouponUserTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: CouponUserTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: CouponUserTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: CouponUserTable::COUPON_ID, label: '优惠券')->setRelation(CouponTable::class, CouponTable::ID, CouponTable::TITLE),
            new Int2TimeField(field: CouponUserTable::START_TIME, label: '开始使用时间'),
            new Int2TimeField(field: CouponUserTable::END_TIME, label: '结束使用时间'),
            new SelectField(field: CouponUserTable::HAS_USE, label: '结束使用时间')->setOptions(
                new OptionManager(0, '未使用'),
                new OptionManager(0, '已使用')
            ),
            new SelectField(field: CouponUserTable::ORDER_SN, label: '在订单号使用了')->setRelation(OrderTable::class, OrderTable::SN, OrderTable::SN),
        );
    }
}