<?php

namespace App\Controller\Admin\Shop;


use Generate\Tables\Datas\ParityPolicyTable;
use Generate\Tables\Datas\SourceTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class ParityPolicyAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "报价政策";
        $config->tableName = ParityPolicyTable::class;
        $config->order = [
            ParityPolicyTable::ID => 'asc'
        ];
    }



    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ParityPolicyTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ParityPolicyTable::SOURCE_ID, label: '来源名称')->setRelation(SourceTable::class, SourceTable::ID, SourceTable::NAME),
            new TextField(field: ParityPolicyTable::NAME, label: '优惠券'),
            new Int2TimeField(field: ParityPolicyTable::START_TIME, label: '开始时间')->hideOnFilter(),
            new Int2TimeField(field: ParityPolicyTable::END_TIME, label: '结束时间')->hideOnFilter(),
            new TextField(field: ParityPolicyTable::OTHER, label: '其他活动'),

        );
    }


}