<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\ShopCategoriesTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class ShopCategoriesAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品分类";
        $config->tableName = ShopCategoriesTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ShopCategoriesTable::ID, label: 'ID')->hideOnForm(),
            new ImageField(field: ShopCategoriesTable::ICON, label: '图标'),
            new SelectField(field: ShopCategoriesTable::PARENT_ID, label: '上级分类')
                ->setRequired(false)->setDefault(0)
                ->setRelation(ShopCategoriesTable::class, ShopCategoriesTable::ID, ShopCategoriesTable::NAME)
            ,
            new TextField(field: ShopCategoriesTable::NAME, label: '名称'),
            new NumberField(field: ShopCategoriesTable::LEVEL, label: '等级')->hideOnForm(),

        );
    }


    /**
     * @param ShopCategoriesTable $table
     * @throws Throwable
     */
    protected function insertUpdateBefore(ShopCategoriesTable $table): void
    {
        $parentId = $table->parentId ?: 0;
        if (empty($parentId)) {
            $table->level = 1;
            $table->parentId = 0;
        } else {
            $parent = new ShopCategoriesTable()->addWhere(ShopCategoriesTable::ID, $parentId)->selectOne();
            $table->level = $parent->level + 1;
        }
    }


}