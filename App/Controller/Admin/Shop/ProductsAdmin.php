<?php

namespace App\Controller\Admin\Shop;

use App\Model\ProductsExport;
use Generate\RouterPath;
use Generate\Tables\Datas\ProductSkuTable;
use Generate\Tables\Datas\ProductsTable;
use Generate\Tables\Datas\SourceTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Action\DownloadAction;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Fields\UrlField;
use Swlib\Admin\Utils\Func;
use Swlib\Queue\MessageQueue;
use Swlib\Response\JsonResponse;
use Swlib\Router\Router;
use Throwable;

class ProductsAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "采集商品";
        $config->tableName = ProductsTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ProductsTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ProductsTable::SOURCE_ID, label: '来源')->setRelation(SourceTable::class, SourceTable::ID, SourceTable::NAME),
            new TextField(field: ProductsTable::NAME, label: '名称'),
            new ImageField(field: ProductsTable::PICTURE, label: '图片'),
            new TextField(field: ProductsTable::CODE, label: '编码'),
            new UrlField(field: ProductsTable::URL, label: '源链接'),
            new TextField(field: ProductsTable::PRICE, label: '价格'),
            new Int2TimeField(field: ProductsTable::LAST_UPDATE_TIME, label: '采集时间')
        );
    }


    /**
     * @throws Throwable
     */
    protected function configAction(ActionsConfig $actions): void
    {
        $saveDir = PUBLIC_DIR . 'export/';
        if (!is_dir($saveDir)) {
            mkdir($saveDir, 0777, true);
        }
        $fileName = '采集数据' . date('YmdHis') . '.xlsx';
        $filePath = $saveDir . $fileName;
        $actions->addActions(
            new DownloadAction(label: '导出到表格', url: RouterPath::AdminShopProductsExport, params: ['file-path' => $filePath])
                ->setProgressUrl(RouterPath::ToolMessageGetProgress)
                ->setCompleteUrl(Func::url("/export/$fileName"))
                ->setSort(1),
            new Action(label: 'SKU列表', url: RouterPath::AdminShopProductSkuLists, params: [
                ProductSkuTable::PRODUCT_ID => "%" . ProductsTable::ID
            ])->showList()

        );
    }


    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function export(): JsonResponse
    {
        $filePath = $this->get('file-path');
        $msgId = MessageQueue::push([ProductsExport::class, 'export'], [
            'filePath' => $filePath
        ]);

        return JsonResponse::success([
            'msgId' => $msgId
        ]);
    }


}