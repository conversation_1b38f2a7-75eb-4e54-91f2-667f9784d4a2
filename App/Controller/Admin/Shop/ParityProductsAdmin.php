<?php

namespace App\Controller\Admin\Shop;


use Generate\RouterPath;
use Generate\Tables\Datas\ParityProductsSkuTable;
use Generate\Tables\Datas\ParityProductsTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class ParityProductsAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "关联商品";
        $config->tableName = ParityProductsTable::class;
    }

    /**
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {
        $actions->addActions(
            new Action(label: 'SKU列表', url: RouterPath::AdminShopParityProductsSkuLists, params: [
                ParityProductsSkuTable::PRODUCT_ID => "%" . ParityProductsTable::ID
            ])->showList()
        );
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ParityProductsTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: ParityProductsTable::NAME, label: '规格名称'),
            new ImageField(field: ParityProductsTable::PICTURE, label: '规格图片'),
            new TextField(field: ParityProductsTable::PRICE, label: '销售价'),
            new TextField(field: ParityProductsTable::ID, label: 'SKU数量')->hideOnFilter()->setListFormat(function ($value) {
                return new ParityProductsSkuTable()->addWhere(ParityProductsSkuTable::PRODUCT_ID, $value)->count();
            }),
            new SwitchField(field: ParityProductsTable::IS_DISABLED, label: '禁用')->onlyOnList(),
        );
    }


}