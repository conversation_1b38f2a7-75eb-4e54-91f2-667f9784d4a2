<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\FootprintTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Enum\ActionDefaultButtonEnum;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class FootprintAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "用户足迹";
        $config->tableName = FootprintTable::class;
    }

    protected function configAction(ActionsConfig $actions): void
    {
        $actions->disabledActions = [
            ActionDefaultButtonEnum::NEW
        ];
    }


    /**
     * @throws Throwable
     */
    protected function join(FootprintTable $query): void
    {
        $query->join(ShopProductsTable::TABLE_NAME, ShopProductsTable::ID, FootprintTable::PRODUCT_ID);
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: FootprintTable::ID, label: 'ID')->hideOnForm(),
            new NumberField(field: FootprintTable::USER_ID, label: '用户ID'),
            new TextField(field: ShopProductsTable::NAME, label: '商品名称'),
            new Int2TimeField(field: FootprintTable::TIME, label: '时间'),
            new NumberField(field: FootprintTable::NUM, label: '浏览次数'),
        );
    }
}