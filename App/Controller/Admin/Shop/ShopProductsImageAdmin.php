<?php

namespace App\Controller\Admin\Shop;

use Generate\RouterPath;
use Generate\Tables\Datas\ShopProductsImageTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Enum\ActionDefaultButtonEnum;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Exception\AppException;
use Throwable;

class ShopProductsImageAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品图片";
        $config->tableName = ShopProductsImageTable::class;
    }

    /**
     * @throws Throwable
     */
    protected function join(ShopProductsImageTable $query): void
    {
        $query->addWhere(ShopProductsImageTable::PRODUCT_ID, $this->get(ShopProductsImageTable::PRODUCT_ID));
    }

    /**
     * @throws Throwable
     * @throws AppException
     */
    public function configAction(ActionsConfig $actions): void
    {
        $actions->disabledActions = [ActionDefaultButtonEnum::NEW];
        $actions->addActions(
            new Action('添加', RouterPath::AdminShopShopProductsImageNew, [
                ShopProductsImageTable::PRODUCT_ID => $this->get(ShopProductsImageTable::PRODUCT_ID)
            ])->showIndex()
        );
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ShopProductsImageTable::ID, label: 'ID')->hideOnForm(),
            new ImageField(field: ShopProductsImageTable::URL, label: '图片'),
            new NumberField(field: ShopProductsImageTable::SORT, label: '排序'),
        );
    }
}