<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\CategoriesTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class CategoriesAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "采集商品分类";
        $config->tableName = CategoriesTable::class;
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: CategoriesTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: CategoriesTable::NAME, label: '名称'),
            new SelectField(field: CategoriesTable::PARENT_ID, label: '上级分类')->setRelation(CategoriesTable::class, CategoriesTable::ID, CategoriesTable::NAME),
            new NumberField(field: CategoriesTable::LEVEL, label: '分类等级')
        );
    }

}