<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\BusinessCategoryTable;
use Generate\Tables\Datas\BusinessTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;

class BusinessAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商家管理";
        $config->tableName = BusinessTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: BusinessTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: BusinessTable::BUSINESS_NAME, label: '名称'),
            new ImageField(field: BusinessTable::LOGO, label: 'LOGO'),

            new SelectField(field: BusinessTable::CATEGORY_ID, label: '分类')
                ->setRelation(BusinessCategoryTable::class, BusinessCategoryTable::ID, BusinessCategoryTable::NAME),

            new TextField(field: BusinessTable::USERNAME, label: '联系人'),
            new TextField(field: BusinessTable::PHONE, label: '电话'),
            new TextField(field: BusinessTable::ADDR, label: '地址'),
            new Int2TimeField(field: BusinessTable::CREATE_TIME, label: '申请时间'),
            new SelectField(field: BusinessTable::STATUS, label: '状态')->setOptions(
                new OptionManager(0, '待审核'),
                new OptionManager(1, '正常'),
                new OptionManager(2, '封禁')
            ),

        );
    }
}