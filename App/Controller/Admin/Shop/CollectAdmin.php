<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\CollectTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Throwable;


class CollectAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品收藏";
        $config->tableName = CollectTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: CollectTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: CollectTable::BUSINESS_ID, label: '商品商家')->setRelation(BusinessTable::class, BusinessTable::ID, BusinessTable::BUSINESS_NAME),
            new SelectField(field: CollectTable::PRODUCT_ID, label: '收藏商品')->setRelation(ShopProductsTable::class, ShopProductsTable::ID, ShopProductsTable::NAME),
            new SelectField(field: CollectTable::SKU_ID, label: '收藏规格')->setRelation(ShopProductsSkuTable::class, ShopProductsSkuTable::ID, ShopProductsSkuTable::NAME),
            new Int2TimeField(field: CollectTable::TIME, label: '收藏时间')
        );
    }

}