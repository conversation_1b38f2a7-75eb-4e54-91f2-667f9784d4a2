<?php

namespace App\Controller\Admin\Shop;

use App\Model\ShopProductsSkuExport;
use Generate\RouterPath;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Admin\Action\DownloadAction;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Utils\Func;
use Swlib\Queue\MessageQueue;
use Swlib\Response\JsonResponse;
use Swlib\Router\Router;
use Throwable;

class ShopProductsSkuAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品SKU";
        $config->tableName = ShopProductsSkuTable::class;
    }

    /**
     * @throws Throwable
     */
    protected function configAction(ActionsConfig $actions): void
    {
        $saveDir = PUBLIC_DIR . '/export/';
        if (!is_dir($saveDir)) {
            mkdir($saveDir, 0777, true);
        }
        $fileName = '商品规格价格明细' . date('YmdHis') . '.xlsx';
        $filePath = $saveDir . $fileName;
        $actions->addActions(
            new DownloadAction(label: '导出来源价格明细', url: RouterPath::AdminShopShopProductsSkuExport, params: ['file-path' => $filePath])
                ->setProgressUrl(RouterPath::ToolMessageGetProgress)
                ->setCompleteUrl(Func::url("/export/$fileName"))
                ->setSort(1)
        );
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ShopProductsSkuTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ShopProductsSkuTable::PRODUCT_ID, label: '商品名称')->setRelation(ShopProductsTable::class, ShopProductsTable::ID, ShopProductsTable::NAME),
            new TextField(field: ShopProductsSkuTable::NAME, label: 'SKU')->hideOnForm(),
            new ImageField(field: ShopProductsSkuTable::PICTURE, label: '图片'),
            new TextField(field: ShopProductsSkuTable::PRICE, label: '价格'),
            new TextField(field: ShopProductsSkuTable::MARKET_PRICE, label: '市场价'),
            new TextField(field: ShopProductsSkuTable::SOURCE_INFO, label: '其他平台的价格信息')->onlyOnDetail(),
        );
    }

    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function export(): JsonResponse
    {
        $filePath = $this->get('file-path');
        $msgId = MessageQueue::push([ShopProductsSkuExport::class, 'export'], [
            'filePath' => $filePath
        ]);

        return JsonResponse::success([
            'msgId' => $msgId
        ]);
    }

}