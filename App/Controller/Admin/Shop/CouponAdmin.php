<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\CouponTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\EditorField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;


class CouponAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "优惠券管理";
        $config->tableName = CouponTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: CouponTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: CouponTable::TITLE, label: '优惠券标题'),
            new TextField(field: CouponTable::SUB_TITLE, label: '优惠券小标题'),
            new TextField(field: CouponTable::AMOUNT, label: '优惠券金额'),
            new TextField(field: CouponTable::CONDITION_AMOUNT, label: '优惠券使用金额门槛'),
            new TextField(field: CouponTable::CONDITION_AMOUNT, label: '优惠券使用金额门槛'),
            new Int2TimeField(field: CouponTable::PERIOD_OF_VALIDITY, label: '有效时间'),
            new Int2TimeField(field: CouponTable::START_TIME, label: '开始领取时间'),
            new Int2TimeField(field: CouponTable::END_TIME, label: '结束领取时间'),
            new EditorField(field: CouponTable::NOTES, label: '优惠券使用说明')->hideOnList(),
        );
    }

}