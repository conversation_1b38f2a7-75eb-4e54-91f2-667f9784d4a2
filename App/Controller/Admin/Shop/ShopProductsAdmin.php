<?php

namespace App\Controller\Admin\Shop;

use Generate\RouterPath;
use Generate\Tables\Datas\BrandTable;
use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\ShopCategoriesTable;
use Generate\Tables\Datas\ShopProductsImageTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\EditorField;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class ShopProductsAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品管理";
        $config->tableName = ShopProductsTable::class;
    }

    /**
     * @throws Throwable
     */
    protected function configAction(ActionsConfig $actions): void
    {
        $actions->addActions(
            new Action('商品图片', RouterPath::AdminShopShopProductsImageLists, [
                ShopProductsImageTable::PRODUCT_ID => "%" . ShopProductsTable::ID
            ])->showList()
        );
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ShopProductsTable::ID, label: 'ID')->hideOnForm(),
            new ImageField(field: ShopProductsTable::PICTURE, label: '商品主图')->setRequired(false),
            new TextField(field: ShopProductsTable::NAME, label: '名称')->setRequired(false),
            new SelectField(field: ShopProductsTable::BUSINESS_ID, label: '商家')->setRequired(false)->setRelation(BusinessTable::class, BusinessTable::ID, BusinessTable::BUSINESS_NAME),
            new SelectField(field: ShopProductsTable::CATEGORY_ID, label: '分类')->setRequired(false)
                ->setRelation(ShopCategoriesTable::class, ShopCategoriesTable::ID, ShopCategoriesTable::NAME)
            ,
            new SelectField(field: ShopProductsTable::BRAND_ID, label: '品牌')->setRequired(false)->setRelation(BrandTable::class, BrandTable::ID, BrandTable::NAME),
            new TextField(field: ShopProductsTable::CODE, label: '规格')->setRequired(false),
            new TextField(field: ShopProductsTable::PRICE, label: '价格')->setRequired(false),
            new EditorField(field: ShopProductsTable::CONTENT, label: '详情')->setRequired(false)->hideOnList(),
            new Int2TimeField(field: ShopProductsTable::LAST_UPDATE_TIME, label: '更新时间')->setRequired(false)->hideOnForm(),
        );
    }

    protected function insertUpdateBefore(ShopProductsTable $table): void
    {
        $table->lastUpdateTime = time();
    }


}