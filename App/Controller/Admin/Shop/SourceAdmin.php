<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\SourceTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Fields\UrlField;
use Throwable;

class SourceAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "来源管理";
        $config->tableName = SourceTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: SourceTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: SourceTable::NAME, label: '来源名称'),
            new Urlfield(field: SourceTable::URL, label: '来源链接'),
            new SwitchField(field: SourceTable::IS_DISABLED, label: '是否禁用'),
        );
    }
}