<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\BusinessCategoryTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class BusinessCategoryAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商家分类";
        $config->tableName = BusinessCategoryTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: BusinessCategoryTable::ID, label: 'ID')->hideOnForm(),
            new TextField(field: BusinessCategoryTable::NAME, label: '名称'),
            new TextField(field: BusinessCategoryTable::LEVEL, label: '等级'),
            new TextField(field: BusinessCategoryTable::PARENT_ID, label: '父级分类ID'),

        );
    }
}