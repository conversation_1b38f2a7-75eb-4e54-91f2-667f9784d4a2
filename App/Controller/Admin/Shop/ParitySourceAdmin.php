<?php

namespace App\Controller\Admin\Shop;

use App\Model\CrawlerProduct;
use Generate\RouterPath;
use Generate\Tables\Datas\ParityProductsSkuTable;
use Generate\Tables\Datas\ParityProductsTable;
use Generate\Tables\Datas\ParitySourceTable;
use Generate\Tables\Datas\SourceTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Enum\ActionDefaultButtonEnum;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Fields\UrlField;
use Swlib\Exception\AppException;
use Swlib\Response\RedirectResponse;
use Swlib\Router\Router;
use Throwable;

class ParitySourceAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品来源";
        $config->tableName = ParitySourceTable::class;
        $config->order = [
            ParitySourceTable::SKU_ID => 'asc'
        ];
    }


    /**
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {

        $skuId = $this->get(ParitySourceTable::SKU_ID, '', '');
        if ($skuId) {
            $actions->disabledActions = [ActionDefaultButtonEnum::NEW];
            $actions->addActions(
                new Action(label: '添加', url: RouterPath::AdminShopParitySourceNew, params: [
                    ParitySourceTable::SKU_ID => $skuId
                ])->showIndex()
            );
        }


        $actions->addActions(
            new Action(label: '立即更新', url: RouterPath::AdminShopParitySourceSourceUpdate)->showList()->showDetail(),
        );
    }


    /**
     * @throws Throwable
     * @throws AppException
     */
    public function listsQuery(ParitySourceTable $query): void
    {
        $skuId = $this->get(ParitySourceTable::SKU_ID, '', '');
        if ($skuId) {
            $query->addWhere(ParitySourceTable::SKU_ID, $skuId);
        }

    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $skuId = $this->get(ParitySourceTable::SKU_ID, '', '');

        $productConfig = new SelectField(field: ParitySourceTable::PRODUCT_ID, label: '产品名称')->setRelation(ParityProductsTable::class, ParityProductsTable::ID, ParityProductsTable::NAME);
        $skuConfig = new SelectField(field: ParitySourceTable::SKU_ID, label: '规格名称')->setRelation(ParityProductsSkuTable::class, ParityProductsSkuTable::ID, ParityProductsSkuTable::NAME);

        if ($skuId) {

            $productConfig->hideOnFilter();
            $skuConfig->hideOnFilter();

            $sku = new ParityProductsSkuTable()->addWhere(ParityProductsSkuTable::ID, $skuId)
                ->field([
                    ParityProductsTable::ID,
                    ParityProductsTable::NAME,
                    ParityProductsSkuTable::NAME,
                ])
                ->join(ParityProductsTable::TABLE_NAME, ParityProductsTable::ID, ParityProductsSkuTable::PRODUCT_ID)
                ->selectOne();
            $productId = $sku->getByField(ParityProductsTable::ID);
            $productName = $sku->getByField(ParityProductsTable::NAME);
            $skuName = $sku->getByField(ParityProductsSkuTable::NAME);

            $html = <<<HTML
<div class="d-flex align-items-end">
    <span >来源列表</span>
    <span class="fs-6">$productId#$productName  【 $skuId#$skuName 】</span>
</div>
HTML;


            $this->pageConfig->setPageName($html, false);
        }


        $fields->setFields(
            new NumberField(field: ParitySourceTable::ID, label: 'ID')->hideOnForm(),
            $productConfig, $skuConfig,
            new SelectField(field: ParitySourceTable::SOURCE_ID, label: '来源名称')->setRelation(SourceTable::class, SourceTable::ID, SourceTable::NAME),
            new ImageField(field: ParitySourceTable::PICTURE, label: '图片'),
            new TextField(field: ParitySourceTable::PRICE, label: '价格'),
            new Int2TimeField(field: ParitySourceTable::LAST_RUN_TIME, label: '上次更新时间')->hideOnForm(),
            new UrlField(field: ParitySourceTable::URL, label: '来源链接')->setRequired(false)->hideOnFilter(),
            new TextField(field: ParitySourceTable::ERR_MSG, label: '请求返回信息')->hideOnForm()->hideOnFilter(),
            new TextField(field: ParitySourceTable::URL, label: '详情')->setListFormat(function ($value, $table) {
                $id = $table->getId();
                $res = '';
                switch ($table->sourceId) {
                    case 1:
                        $res = file_get_contents(RUNTIME_DIR . "crawler/mmm/$id.txt");
                        break;
                    case 2:
                        $res = file_get_contents(RUNTIME_DIR . "crawler/lc/$id.txt");
                        break;
                    case 3:
                        $res = file_get_contents(RUNTIME_DIR . "crawler/yezx/$id.txt");
                        break;
                    case 4:
                        $res = file_get_contents(RUNTIME_DIR . "crawler/sbsc/$id.txt");
                        break;
                    case 5:
                        $res = file_get_contents(RUNTIME_DIR . "crawler/eky/$id.txt");
                        break;
                    case 7:
                        $res = file_get_contents(RUNTIME_DIR . "crawler/yyb/$id.txt");
                        break;
                }
                return $res;
            })->hideOnList()->hideOnForm()->hideOnFilter()->setTooltip(false),
            new SwitchField(field: ParitySourceTable::IS_DISABLED, label: '是否禁用')->hideOnForm()->hideOnFilter(),
            new TextField(field: ParitySourceTable::C_ID, label: '采集SkuId')->setRequired(false)->hideOnFilter(),
            new TextField(field: ParitySourceTable::C_CODE, label: '源编码')->setRequired(false)->hideOnFilter(),
        );
    }


    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function sourceUpdate(): RedirectResponse
    {
        $id = $this->get('id');
        $source = new ParitySourceTable()->addWhere(ParitySourceTable::ID, $id)->selectOne();
        if (empty($source->url) && empty($source->cId) && empty($source->cCode)) {
            throw new AppException('请先设置来源');
        }
        CrawlerProduct::run($source);
        return RedirectResponse::url($this->request->header["referer"]);
    }

}