<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\ShopProductTagTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\TextField;
use Throwable;

class ShopProductTagAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品标签";
        $config->tableName = ShopProductTagTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
           new NumberField(field: ShopProductTagTable::ID, label: 'ID')->hideOnForm(),
           new TextField(field: ShopProductTagTable::TITLE, label: '名称'),
        );
    }
}