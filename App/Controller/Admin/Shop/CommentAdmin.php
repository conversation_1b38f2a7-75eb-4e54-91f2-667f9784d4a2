<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\CommentTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\ShopProductsTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Throwable;


class CommentAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品评论";
        $config->tableName = CommentTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: CommentTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: CommentTable::PRODUCT_ID, label: '商品')->setRelation(ShopProductsTable::class, ShopProductsTable::ID, ShopProductsTable::NAME),
            new TextField(field: CommentTable::COMMENT, label: '评论内容'),
            new ImageField(field: CommentTable::IMAGES, label: '评论图片')->setMax(9),
            new SwitchField(field: CommentTable::ANONYMOUS, label: '匿名评价'),
            new NumberField(field: CommentTable::DESC_NUM, label: '描述评分'),
            new NumberField(field: CommentTable::COMPOSITE_NUM, label: '综合评分'),
            new NumberField(field: CommentTable::LOGISTICS_NUM, label: '物流评分'),
            new NumberField(field: CommentTable::CUSTOMER_SERVICE_NUM, label: '客服评分'),
            new NumberField(field: CommentTable::CUSTOMER_SERVICE_NUM, label: '客服评分'),
            new SelectField(field: CommentTable::COMMENT_TAG, label: '评论类型')->setOptions(
                new OptionManager(1, '整体评价'),
                new OptionManager(2, '性价比'),
                new OptionManager(2, '产品描述'),
            ),
            new SelectField(field: CommentTable::ORDER_ID, label: '订单')->setRelation(OrderTable::class, OrderTable::ID, OrderTable::SN),
            new SelectField(field: CommentTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new Int2TimeField(field: CommentTable::TIME, label: '评论时间'),

        );
    }

}