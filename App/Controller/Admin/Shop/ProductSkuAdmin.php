<?php

namespace App\Controller\Admin\Shop;


use Generate\Tables\Datas\ProductSkuTable;
use Generate\Tables\Datas\ProductsTable;
use Generate\Tables\Datas\SourceTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Fields\UrlField;

use Swlib\Exception\AppException;
use Throwable;

class ProductSkuAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "采集商品规格";
        $config->tableName = ProductSkuTable::class;
    }


    /**
     * @throws Throwable
     * @throws AppException
     */
    public function listsQuery(ProductSkuTable $query): void
    {
        $pid = $this->get(ProductSkuTable::PRODUCT_ID,'','');
        if($pid){
            $query->where([
                [ProductSkuTable::PRODUCT_ID, '=', $pid]
            ]);
        }
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $pid = $this->get(ProductSkuTable::PRODUCT_ID,'','');
        $fields->setFields(
            new NumberField(field: ProductSkuTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: ProductSkuTable::PRODUCT_ID, label: '产品')->setDefault($pid)->setRelation(ProductsTable::class, ProductsTable::ID, ProductsTable::NAME),
            new TextField(field: ProductSkuTable::NAME, label: '规格名称'),
            new TextField(field: ProductSkuTable::CODE, label: '编码'),
            new TextField(field: ProductSkuTable::UNIT, label: '单位'),
            new TextField(field: ProductSkuTable::PRICE, label: '销售价格'),
            new TextField(field: ProductSkuTable::MARKET_PRICE, label: '市场价'),
            new SelectField(field: ProductSkuTable::SOURCE_ID, label: '来源')->setRelation(SourceTable::class, SourceTable::ID, SourceTable::NAME),
            new TextField(field: ProductSkuTable::PRICE, label: '价格'),
            new UrlField(field: ProductSkuTable::URL, label: '源链接'),

        );
    }



}