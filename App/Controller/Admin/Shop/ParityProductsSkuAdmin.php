<?php

namespace App\Controller\Admin\Shop;

use App\Model\ParityProductsSkuExport;
use App\Model\ShopProductsImportModel;
use Generate\RouterPath;
use Generate\Tables\Datas\ParityProductsSkuTable;
use Generate\Tables\Datas\ParityProductsTable;
use Generate\Tables\Datas\ParitySourceTable;
use Swlib\Admin\Action\Action;
use Swlib\Admin\Action\DownloadAction;
use Swlib\Admin\Action\UploadAction;
use Swlib\Admin\Config\ActionsConfig;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Enum\ActionDefaultButtonEnum;
use Swlib\Admin\Fields\ImageField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Utils\Func;
use Swlib\Exception\AppException;
use Swlib\Queue\MessageQueue;
use Swlib\Response\JsonResponse;
use Swlib\Router\Router;
use Throwable;

class ParityProductsSkuAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "关联商品Sku";
        $config->tableName = ParityProductsSkuTable::class;
    }

    /**
     * @throws Throwable
     */
    public function configAction(ActionsConfig $actions): void
    {

        $productId = $this->get(ParityProductsSkuTable::PRODUCT_ID, '', '');
        if ($productId) {
            $actions->disabledActions = [ActionDefaultButtonEnum::NEW];
            $actions->addActions(
                new Action(label: '添加', url: RouterPath::AdminShopParityProductsSkuNew, params: [
                    ParityProductsSkuTable::PRODUCT_ID => $productId
                ])->showIndex()
            );
        }


        $fileName = '商品规格来源' . date('YmdHis') . '.xlsx';
        $diffName = '比对结果' . date('YmdHis') . '.xlsx';
        $saveDir = PUBLIC_DIR . 'export/';
        if (!is_dir($saveDir)) {
            mkdir($saveDir, 0777, true);
        }
        $filePath = $saveDir . $fileName;
        $diffPath = $saveDir . $diffName;

        $actions->addActions(
            new Action(label: '下载导入模板.xlsx', url: '/导入关联商品模板.xlsx')->showIndex()->setSort(1),
            new UploadAction(label: '导入关联商品.xlsx', url: RouterPath::AdminShopParityProductsSkuUploadCsv)
                ->setProgressUrl(RouterPath::ToolMessageGetProgress)->setSort(1),
            new UploadAction(label: '导入关联商品进行比对', url: RouterPath::AdminShopParityProductsSkuUploadDiff, params: ['file-path' => $diffPath])
                ->setProgressUrl(RouterPath::ToolMessageGetProgress)->setSort(1)
                ->setCompleteUrl(Func::url("/export/$diffName"))
            ,
            new DownloadAction(label: '导出商品规格来源.xlsx', url: RouterPath::AdminShopParityProductsSkuExport, params: ['file-path' => $filePath])
                ->setProgressUrl(RouterPath::ToolMessageGetProgress)->setSort(1)
                ->setCompleteUrl(Func::url("/export/$fileName"))
            ,
            new Action(label: '查看来源', url: RouterPath::AdminShopParitySourceLists, params: [
                ParitySourceTable::SKU_ID => "%" . ParityProductsSkuTable::ID
            ])->showList()
        );
    }


    /**
     * @throws Throwable
     * @throws AppException
     */
    public function listsQuery(ParityProductsSkuTable $query): void
    {
        $productId = $this->get(ParityProductsSkuTable::PRODUCT_ID, '', '');
        if ($productId != '') {
            $query->addWhere(ParityProductsSkuTable::PRODUCT_ID, $productId);
        }
    }

    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {


        $productId = $this->get(ParityProductsSkuTable::PRODUCT_ID, '', '');
        $productConfig = new SelectField(field: ParityProductsSkuTable::PRODUCT_ID, label: '产品')->setRelation(ParityProductsTable::class, ParityProductsTable::ID, ParityProductsTable::NAME);

        if ($productId) {
            $product = new ParityProductsTable()->addWhere(ParityProductsTable::ID, $productId)->selectOne();

            $productName = $product->name;
            $html = <<<HTML
<div class="d-flex align-items-end">
    <span >SKU规格列表</span>
    <span class="fs-6">$productId#$productName </span>
</div>
HTML;

            $this->pageConfig->setPageName($html, false);
            $productConfig->hideOnFilter();
        }


        $fields->setFields(
            new NumberField(field: ParityProductsSkuTable::ID, label: 'ID')->hideOnForm(),

            $productConfig,
            new TextField(field: ParityProductsSkuTable::NAME, label: '规格名称'),
            new ImageField(field: ParityProductsSkuTable::PICTURE, label: '规格图片'),
            new TextField(field: ParityProductsSkuTable::PRICE, label: '销售价'),
            new TextField(field: ParityProductsSkuTable::ID, label: '来源数量')->setListFormat(function ($value) {
                return new ParitySourceTable()->addWhere(ParitySourceTable::SKU_ID, $value)->count();
            }),
        );
    }


    /**
     * @throws Throwable
     */
    #[Router(method: 'POST')]
    public function uploadCsv(): JsonResponse
    {
        $file = $this->request->files['file'];

        $tmp_name = $file['tmp_name'];
        $name = $file['name'];

        $uploadDir = RUNTIME_DIR . 'uploadCsv/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $filePath = $uploadDir . date('YmdHis') . '-' . $name;
        move_uploaded_file($tmp_name, $filePath);

        $msgId =  MessageQueue::push([ShopProductsImportModel::class, 'import'], [
            'filePath' => $filePath,
        ]);

        return JsonResponse::success([
            'msgId' => $msgId
        ]);
    }


    /**
     * @throws Throwable
     */
    #[Router(method: 'POST')]
    public function uploadDiff(): JsonResponse
    {
        $file = $this->request->files['file'];
        $saveFilePath = $this->get('file-path');
        $name = $file['name'];
        $tmp_name = $file['tmp_name'];
        $uploadDir = RUNTIME_DIR . 'uploadCsv/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $filePath = $uploadDir . date('YmdHis') . '-' . $name;
        move_uploaded_file($tmp_name, $filePath);

        $msgId = MessageQueue::push([ParityProductsSkuExport::class, 'diff'], [
            'filePath' => $filePath,
            'saveFilePath' => $saveFilePath,
        ]);

        return JsonResponse::success([
            'msgId' => $msgId
        ]);
    }

    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function export(): JsonResponse
    {
        $filePath = $this->get('file-path');
        $msgId = MessageQueue::push([ParityProductsSkuExport::class, 'export'], [
            'filePath' => $filePath
        ]);

        return JsonResponse::success([
            'msgId' => $msgId
        ]);
    }

}