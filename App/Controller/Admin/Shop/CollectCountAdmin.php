<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\CollectCountTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Throwable;


class CollectCountAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品收藏";
        $config->tableName = CollectCountTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: CollectCountTable::ID, label: 'ID')->hideOnForm(),
            new SelectField(field: CollectCountTable::PRODUCT_ID, label: '被收藏商品')->setRelation(ShopProductsTable::class, ShopProductsTable::ID, ShopProductsTable::NAME),
            new NumberField(field: CollectCountTable::NUM, label: '收藏数量'),

        );
    }

}