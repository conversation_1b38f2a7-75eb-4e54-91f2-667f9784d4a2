<?php

namespace App\Controller\Admin\Shop;

use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\ShopCarTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Throwable;

class ShopCarAdmin extends AbstractAdmin
{

    protected function configPage(PageConfig $config): void
    {
        $config->pageName = "商品购物车";
        $config->tableName = ShopCarTable::class;
    }


    /**
     * @throws Throwable
     */
    protected function configField(PageFieldsConfig $fields): void
    {
        $fields->setFields(
            new NumberField(field: ShopCarTable::ID, label: 'ID')->hideOnForm(),
            new NumberField(field: ShopCarTable::NUM, label: '加购数量'),
            new SelectField(field: ShopCarTable::USER_ID, label: '用户')->setRelation(UserTable::class, UserTable::ID, UserTable::PHONE),
            new SelectField(field: ShopCarTable::BUSINESS_ID, label: '商家')->setRelation(BusinessTable::class, BusinessTable::ID, BusinessTable::BUSINESS_NAME),
            new SelectField(field: ShopCarTable::PRODUCT_ID, label: '产品')->setRelation(ShopProductsTable::class, ShopProductsTable::ID, ShopProductsTable::NAME),
            new SelectField(field: ShopCarTable::SKU_ID, label: '规格')->setRelation(ShopProductsSkuTable::class, ShopProductsSkuTable::ID, ShopProductsSkuTable::NAME),

        );
    }
}