<?php

namespace App\Controller\Tool;

use Swlib\Connect\PoolRedis;
use <PERSON>wlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Response\JsonResponse;
use Swlib\Router\Router;
use Swlib\Utils\Http;
use Redis;
use Throwable;

class Ys extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function getAccessToken(): JsonResponse
    {
        $token = PoolRedis::call(function (Redis $redis) {
            $cacheKEY = 'YS:accessToken';
            $accessToken = $redis->get($cacheKEY);
            if ($accessToken) {
                return $accessToken;
            }

            $http = new Http();
            $http->post('https://open.ys7.com/api/lapp/token/get', [
                'appKey' => 'fc80459c42ce41d3975c8135a7f07309',
                'appSecret' => '3cd4cc8eb2f4bbdf91715ab701b0c134'
            ]);
            $res = $http->responseArray();
            if ($res['code'] != 200) {
                throw new AppException('获取萤石云token失败');
            }

            $token = $res['data']['accessToken'];
            $redis->set($cacheKEY, $token);
            $redis->expire($cacheKEY, 86400);
            return $token;

        });
        return JsonResponse::success(['token' => $token]);
    }


      /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function getAccessToken2(): JsonResponse
    {
        $token = PoolRedis::call(function (Redis $redis) {
            $cacheKEY = 'YS:accessToken2';
            $accessToken = $redis->get($cacheKEY);
            if ($accessToken) {
                return $accessToken;
            }

            $http = new Http();
            $http->post('https://open.ys7.com/api/lapp/token/get', [
                'appKey' => '89d20fcc701c43499ce2bf64c3e252dd',
                'appSecret' => '3625fef052ca2f9171c9d66394df4be5'
            ]);
            $res = $http->responseArray();
            if ($res['code'] != 200) {
                throw new AppException('获取萤石云token失败');
            }

            $token = $res['data']['accessToken'];
            $redis->set($cacheKEY, $token);
            $redis->expire($cacheKEY, 86400);
            return $token;

        });
        return JsonResponse::success(['token' => $token]);
    }




}