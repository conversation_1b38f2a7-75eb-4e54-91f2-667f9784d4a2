<?php

namespace App\Controller\Tool;

require_once ROOT_DIR . "sdk/aliyun-oss-php-sdk-2.7.1/autoload.php";

use App\Model\PayModel;
use App\Service\EnvironmentVariableCredentialsProvider;
use Exception;
use Generate\Tables\Datas\OrderTable;
use Swlib\Connect\PoolRedis;
use <PERSON><PERSON>ib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Response\EmptyResponse;
use Swlib\Response\JsonResponse;
use Swlib\Response\RedirectResponse;
use Swlib\Response\ResponseInterface;
use Swlib\Response\TwigResponse;
use Swlib\Router\Router;
use Swlib\Utils\Language;
use OSS\OssClient;
use Protobuf\Common\Success;
use Protobuf\Order\OrderRequest;
use Protobuf\Order\PayResponse;
use Redis;
use Throwable;
use Yansongda\Pay\Pay;


class Ali extends AbstractController
{
    #[Router(method: 'POST')]
    public function upload(): JsonResponse
    {

        try {
            $file = $this->request->files['file'];


            $tmp_name = $file['tmp_name'];
            $name = $file['name'];

            $filename = $this->post('filename', '', $name);
            $fileContent = file_get_contents($tmp_name);

            $arr = explode('.', $filename);
            $hz = array_pop($arr);


            $filename = uniqid() . date('YmdHis') . '.' . $hz;

            $provider = new EnvironmentVariableCredentialsProvider();

            $endpoint = "https://oss-cn-chengdu.aliyuncs.com";
            $bucket = "fryl-shop-image";
            $object = "upload/$filename";


            $config = array(
                "provider" => $provider,
                "endpoint" => $endpoint,
            );


            $ossClient = new OssClient($config);
            $ossClient->putObject($bucket, $object, $fileContent);

            return JsonResponse::success([
                'path' => "https://static.zhonguoyagu.com/upload/$filename",
                'url' => "https://static.zhonguoyagu.com/upload/$filename", // 兼容 editor
            ]);
        } catch (Throwable $e) {
            return JsonResponse::error($e);
        }
    }

    /**
     * 支付宝 h5 支付
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function payH5(): TwigResponse
    {
        $sn = $this->get('sn');
        $quitUrl = $this->get('quit_url');
        $successUrl = $this->get('success_url');

        if (empty($sn)) {
            throw new AppException('请输入订单编号');
        }

        PoolRedis::call(function (Redis $redis) use ($successUrl, $sn) {
            // 记录好支付成功返回地址，支付成功以后就直接返回链接
            $redis->set("alipay_return_url:$sn", $successUrl, 3600);
        });

        $orderTable = new OrderTable()->addWhere(OrderTable::SN, $sn)->selectOne();
        $totalAmount = $orderTable->price;

        Pay::config(PayModel::getConfig());
        // 注意返回类型为 Response，具体见详细文档
        $response = Pay::alipay()->h5([
            'out_trade_no' => $sn,
            'total_amount' => $totalAmount,
            'subject' => Language::get('购买商品'),
            'quit_url' => $quitUrl,
            'passback_params' => urlencode($successUrl)
        ]);

        if ($response->getStatusCode() !== 200) {
            throw new AppException('支付宝支付失败');
        }

        $formString = $response->getBody()->getContents();
        return TwigResponse::render('ali/pay-h5.twig', [
            'formString' => $formString
        ]);
    }


    /**
     * 支付宝 h5 支付
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '支付失败')]
    public function payApp(OrderRequest $request): PayResponse
    {
        $sn = $request->getSn();

        if (empty($sn)) {
            throw new AppException('请输入订单编号');
        }

        $orderTable = new OrderTable()->addWhere(OrderTable::SN, $sn)->selectOne();
        $totalAmount = $orderTable->price;

        Pay::config(PayModel::getConfig());
        // 注意返回类型为 Response，具体见详细文档
        $response = Pay::alipay()->app([
            'out_trade_no' => $sn,
            'total_amount' => $totalAmount,
            'subject' => Language::get('购买商品'),
        ]);

        if ($response->getStatusCode() !== 200) {
            throw new AppException('支付宝支付失败');
        }

        $res = $response->getBody()->getContents();
        $payResponse = new PayResponse();
        $payResponse->setOrderInfo($res);
        return $payResponse;

    }


    /**
     * 查询支付状态
     * @param OrderRequest $request
     * @return Success
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '查询支付状态失败')]
    public function payQuery(OrderRequest $request): Success
    {
        $sn = $request->getSn();

        if (empty($sn)) {
            throw new AppException('请输入订单编号');
        }

        $orderTable = new OrderTable()->addWhere(OrderTable::SN, $sn)->selectOne();

        $message = new Success();
        if ($orderTable->payNo) {
            $message->setSuccess(true);
        }

        $res = PayModel::aliPayQuery($sn);
        $message->setSuccess((bool)$res);
        return $message;
    }


    /**
     * 支付宝支付成功后返回到本地址
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function payReturn(): ResponseInterface
    {
        // *************:9505/tool/ali/pay-return?
        //charset=utf-8&
        //out_trade_no=61120241017155023000022
        //&method=alipay.trade.wap.pay.return&
        //total_amount=0.01&
        //sign=bvieGmdqiAX5+B06MhQQx3OzfJ89drvyvM2g+VaTzsUHnaXfm+TkN+WLx1Fqe6liGeQQBS+/5NDeGhFDrE1IsF0zV29yYW/dAPk4zeqZOyN5IUiWH6KmeLjhetbhWieoYsWGNiFGF6h3ooMSys93AJJFGeOS7qigaoihhtrP8M1Wx/fz2/3ytJct7VDVXS4R3FvczrmchAhnKjL6PIGfGrK3666SwXG4Q3eVtEaU+EB2fycIVh0+wQ97vt1ciXHSwXqqPsRqoT/UVBaG8jaaWOnjaWPRtpg4Vzt9eZP9EGr4nXmGQDAShpaYK/iIus/xg4uWD/QpzVncBdQcHDyK4A==&
        //trade_no=2024101722001404101420262316&
        //auth_app_id=2021004187604146&
        //version=1.0&app_id=2021004187604146&
        //sign_type=RSA2&seller_id=2088550141952525&
        //timestamp=2024-10-17+16:03:40
        $sn = $this->get('out_trade_no');
        if (empty($sn)) {
            throw new Exception('订单编号不存在');
        }
        $res = PayModel::aliPayQuery($sn);

        if ($res === true) {
            $retUrl = PoolRedis::call(function (Redis $redis) use ($sn) {
                // 记录好支付成功返回地址，支付成功以后就直接返回链接
                return $redis->get("alipay_return_url:$sn");
            });
            return RedirectResponse::url($retUrl);
        }

        return EmptyResponse::new();
    }

    #[Router(method: 'GET')]
    public function payNotify()
    {

    }


}