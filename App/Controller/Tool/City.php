<?php

namespace App\Controller\Tool;

use Generate\Tables\Datas\CitysTable;
use Swlib\Controller\AbstractController;
use Swlib\Router\Router;
use Protobuf\Datas\Citys\CitysListsProto;
use Protobuf\Datas\Citys\CitysProto;
use Throwable;

class City extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '获取城市列表失败')]
    public function lists(CitysProto $request): CitysListsProto
    {
        $name = $request->getName();

        $where = [];
        if ($name) {
            $where[] = [CitysTable::NAME, 'like', "$name%"];
        } else {
            $where[] = [CitysTable::CODE, 'like', "%00"];
        }

        $cityArr = new CitysTable()->where($where)->selectAll();

        $nodes = [];
        foreach ($cityArr as $city) {

            $letter = $city->letter;
            if (!isset($nodes[$letter])) {
                $row = new CitysListsProto();
                $row->setLetter($letter);
                $nodes[$letter] = $row;
            } else {
                $row = $nodes[$letter];
            }

            $item = new CitysProto();
            $item->setId($city->id);
            $item->setName($city->name);


            $row->getLists()->offsetSet(null, $item);

        }

        $ret = new CitysListsProto();
        $ret->setSelfListsArray($nodes);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '获取热门城市列表')]
    public function hotLists(): CitysListsProto
    {
        $where = [CitysTable::IS_HOT => 1];
        $cityArr = new CitysTable()->where($where)->selectAll();

        $nodes = [];
        foreach ($cityArr as $city) {
            $item = new CitysProto();
            $item->setId($city->id);
            $item->setName($city->name);
            $nodes[] = $item;
        }

        $ret = new CitysListsProto();
        $ret->setLists($nodes);
        return $ret;
    }

}