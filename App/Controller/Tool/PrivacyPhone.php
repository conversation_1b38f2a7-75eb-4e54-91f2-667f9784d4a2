<?php

namespace App\Controller\Tool;

use App\Service\AliPhonePrivacyService;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Datas\PrivacyPhone\PrivacyPhoneProto;
use Throwable;

class PrivacyPhone extends AbstractController
{
    /**
     *  获取隐私号码
     * @throws Throwable
     * @throws AppException
     */
    #[Router(method: 'POST', errorTitle: '获取隐私号码失败')]
    public function getXPhone(PrivacyPhoneProto $request): PrivacyPhoneProto
    {
        $bPhone = $request->getSendPhone();
        $aPhone = $request->getAnswerPhone();
        if (empty($bPhone) || empty($aPhone)) {
            throw new AppException('手机号不能为空');
        }
        $xPhone = AliPhonePrivacyService::getXPhone($aPhone, $bPhone);

        $proto = new PrivacyPhoneProto();
        $proto->setPrivacyPhone($xPhone);
        return $proto;

    }
}