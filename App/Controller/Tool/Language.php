<?php

namespace App\Controller\Tool;


use <PERSON>wlib\Controller\AbstractController;
use Swlib\Router\Router;
use Protobuf\Common\LanguageLists;
use Throwable;


class Language extends AbstractController
{

    /**
     * 获取翻译语言
     * @throws Throwable
     */
    #[Router(method: "POST", cache: 3600, errorTitle: '获取语言失败')]
    public function getLang(\Protobuf\Common\Language $request): \Protobuf\Common\Language
    {
        $key = $request->getKey();
        $replace = $request->getReplace();
        if ($replace) {
            $ret = \Swlib\Utils\Language::get($key, $replace);
        } else {
            $ret = \Swlib\Utils\Language::get($key);
        }

        $message = new \Protobuf\Common\Language();
        $message->setKey($key);
        $message->setValue($ret);

        return $message;
    }


    /**
     * 获取翻译语言列表
     * @throws Throwable
     */
    #[Router(method: "POST", cache: 3600, errorTitle: '获取语言列表失败')]
    public function lists(): LanguageLists
    {
        $lists = \Swlib\Utils\Language::getLanguages();

        $nodes = [];
        foreach ($lists as $key => $value) {
            $itemMessage = new \Protobuf\Common\Language();
            $itemMessage->setKey($key);
            $itemMessage->setValue($value);
            $nodes[] = $itemMessage;
        }

        $listsMessage = new LanguageLists();
        $listsMessage->setLists($nodes);

        return $listsMessage;
    }

}