<?php

namespace App\Controller\Tool;


use Exception;
use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use <PERSON>wlib\Response\JsonResponse;
use Swlib\Router\Router;


class Upload extends AbstractController
{

    #[Router(method: 'POST')]
    public function upload(): JsonResponse
    {

        try {
            $file = $this->request->files['file'];

            $tmp_name = $file['tmp_name'];
            $name = $file['name'];

            $filename = $this->post('filename', '', $name);
            $dir = ROOT_DIR . "upload/";
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }
            $filepath = $dir . $filename;
            move_uploaded_file($tmp_name, $filepath);

            return JsonResponse::success([
                'path' => $filepath
            ]);
        } catch (Exception $e) {
            return JsonResponse::error($e);
        }
    }
}