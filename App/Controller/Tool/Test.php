<?php

namespace App\Controller\Tool;

use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use <PERSON>wlib\Enum\CtxEnum;
use Swlib\Response\JsonResponse;
use Swlib\Router\Router;
use Swlib\Utils\Func;
use Swlib\Utils\Ip;
use Swoole\Http\Request;
use Throwable;

class Test extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function header(): JsonResponse
    {
        /**@var Request $request */
        $request = CtxEnum::Request->get();

        return JsonResponse::success([
            'server' => $request->server,
            'header' => $request->header,
            'host' => Func::getHost(),
            'ip' => Ip::get(),
        ]);
    }


}