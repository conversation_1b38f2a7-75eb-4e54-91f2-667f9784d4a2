<?php

namespace App\Controller\Tool;


use Generate\Tables\Datas\VrSceneTable;
use Generate\Tables\Datas\VrTagTable;
use Swlib\Controller\AbstractController;
use Swlib\Response\JsonResponse;
use Swlib\Router\Router;
use Throwable;


class Vr extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function getScenes(): JsonResponse
    {
        $lists = new VrSceneTable()->order([
            VrSceneTable::SORT => 'asc'
        ])->selectAll();


        $ret = [];
        foreach ($lists as $list) {
            $ret[] = [
                'id' => $list->id,
                'name' => $list->name,
                'panorama' => $list->panorama,
                'thumbnail' => $list->thumbnail,
            ];
        }

        return JsonResponse::success($ret);
    }

    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function getTagsBySceneId(): JsonResponse
    {
        $scene_id = $this->get('scene_id');
        $lists = new VrTagTable()->where([
            [VrTagTable::SCENE_ID, '=', $scene_id]
        ])->selectAll();

        $ret = [];
        foreach ($lists as $list) {
            $ret[] = [
                'id' => $list->id,
                'scene_id' => $list->sceneId,
                'name' => $list->name,
                'position_yaw' => $list->positionYaw,
                'position_pitch' => $list->positionPitch,
                'content' => $list->content,
                'type' => $list->type,
                'to_scene_id' => $list->toSceneId,
                'url' => $list->url,
            ];
        }

        return JsonResponse::success($ret);
    }
}