<?php

namespace App\Controller\Tool;


use Exception;
use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use Swlib\Queue\MessageQueue;
use Swlib\Response\JsonResponse;
use Swlib\Router\Router;
use Throwable;


class Message extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function getProgress(): JsonResponse
    {
        $msgId = $this->get('msgId');
        $status = MessageQueue::getStatus($msgId);


        if ($status['error']) {
            return JsonResponse::error(new Exception($status['error']));
        }

        return JsonResponse::success([
            // -1 表示执行成功了
            'progress' => $status['isSuccess'] ? -1 : $status['progress']
        ]);
    }


}