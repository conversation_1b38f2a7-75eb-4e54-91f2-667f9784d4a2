<?php

namespace App\Controller\Api\Shop;

use Generate\Models\Datas\HyCompaniesModel;
use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Models\Datas\HyProductsSkuModel;
use Generate\Models\Datas\ShopCarModel;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Generate\Tables\Datas\ShopCarTable;
use Protobuf\Datas\ShopCar\ShopCarListsProto;
use Protobuf\Datas\ShopCar\ShopCarProto;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Swlib\Table\Db;
use Throwable;

#[Router(method: 'POST')]
class ShopCarApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '添加购物车失败')]
    public function add(Request $request): Success
    {
        $skuId = $request->getId();
        $userId = $request->getUserId();
        $num = $request->getNum() ?: 1;
        if (empty($skuId)) {
            throw new AppException('请选择商品规格');
        }

        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $sku = new HyProductsSkuTable()->where([
            [HyProductsSkuTable::ID, '=', $skuId]
        ])->selectOne();
        if (empty($sku)) {
            throw new AppException('商品不存在');
        }

        $product = new HyCompaniesServiceTable()->where([
            [HyCompaniesServiceTable::ID, '=', $sku->productId]
        ])->selectOne();
        if (empty($product)) {
            throw new AppException('商品不存在');
        }

        $carId = new ShopCarTable()->where([
            [ShopCarTable::USER_ID, '=', $userId],
            [ShopCarTable::SKU_ID, '=', $skuId]
        ])->selectField(ShopCarTable::ID);
        if (empty($carId)) {
            $data = [
                ShopCarTable::NUM => $num,
                ShopCarTable::USER_ID => $request->getUserId(),
                ShopCarTable::PRODUCT_ID => $sku->productId,
                ShopCarTable::SKU_ID => $skuId,
                ShopCarTable::BUSINESS_ID => $product->companiesId,
            ];

            $ret = new ShopCarTable()->insert($data);
        } else {
            $ret = new ShopCarTable()->where([
                [ShopCarTable::ID, '=', $carId],
            ])->update([
                ShopCarTable::NUM => Db::incr(ShopCarTable::NUM, $num),
            ]);
        }

        $success = new Success();
        $success->setSuccess((bool)$ret);
        return $success;
    }

    /**
     * 从购物车删除商品
     * @throws Throwable
     */
    #[Router(errorTitle: '购物车删除商品失败')]
    public function del(ShopCarProto $request): Success
    {
        $carId = $request->getId();
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $ret = new ShopCarTable()->where([
            [ShopCarTable::USER_ID, '=', $userId],
            [ShopCarTable::ID, '=', $carId]
        ])->delete();

        $success = new Success();
        $success->setSuccess((bool)$ret);
        return $success;
    }


    /**
     * 添加购物车数量
     * @throws Throwable
     */
    #[Router(errorTitle: '修改数量失败')]
    public function updateNum(Request $request): Success
    {
        $carId = $request->getId();
        $num = $request->getNum();
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $ret = new ShopCarTable()->where([
            [ShopCarTable::USER_ID, '=', $userId],
            [ShopCarTable::ID, '=', $carId]
        ])->update([
            ShopCarTable::NUM => $num
        ]);

        $success = new Success();
        $success->setSuccess((bool)$ret);
        return $success;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取购物车列表失败')]
    public function lists(Request $request): ShopCarListsProto
    {
//        $page = $request->getPage() ?: 1;
//        $size = $request->getSize() ?: 10;
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $shopCarTable = new ShopCarTable();
        $lists = $shopCarTable->where([
            [ShopCarTable::USER_ID, '=', $userId]
        ])->order([
            ShopCarTable::BUSINESS_ID => 'asc'
        ])->setDebugSql()->selectAll();
        $businessIds = $shopCarTable->getArrayByField(ShopCarTable::BUSINESS_ID);
        $skuIds = $shopCarTable->getArrayByField(ShopCarTable::SKU_ID);
        $productIds = $shopCarTable->getArrayByField(ShopCarTable::PRODUCT_ID);


        $ret = [];
        if ($lists) {

            // 查询出所有的商家
            $businessArr = new HyCompaniesTable()->where([
                [HyCompaniesTable::ID, 'in', $businessIds]
            ])->formatId2Array(HyCompaniesTable::ID);

            // 查询出所有的 SKU
            $skus = new HyProductsSkuTable()->where([
                [HyProductsSkuTable::ID, 'in', $skuIds]
            ])->formatId2Array(HyProductsSkuTable::ID);


            // 查询出所有的产品
            $shopProductArr = new HyCompaniesServiceTable()->where([
                [HyCompaniesServiceTable::ID, 'in', $productIds]
            ])->formatId2Array(HyCompaniesServiceTable::ID);


            foreach ($lists as $carTable) {
                $proto = ShopCarModel::formatItem($carTable);
                if (isset($businessArr[$carTable->businessId])) {
                    $proto->setCompanies(HyCompaniesModel::formatItem($businessArr[$carTable->businessId]));
                }
                if (isset($shopProductArr[$carTable->productId])) {
                    $proto->setCompaniesService(HyCompaniesServiceModel::formatItem($shopProductArr[$carTable->productId]));
                }
                if (isset($skus[$carTable->skuId])) {
                    $proto->setSku(HyProductsSkuModel::formatItem($skus[$carTable->skuId]));
                }
                $ret[] = $proto;
            }

        }

        $lists = new ShopCarListsProto();
        $lists->setLists($ret);
        return $lists;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '更新购物车规格失败')]
    public function updateSku(ShopCarProto $request): Success
    {
        $carId = $request->getId();
        $skuId = $request->getSkuId();
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }
        if (empty($carId)) {
            throw new AppException('参数错误');
        }
        if (empty($skuId)) {
            throw new AppException('参数错误');
        }

        $ret = new ShopCarTable()->where([
            [ShopCarTable::USER_ID, '=', $userId],
            [ShopCarTable::ID, '=', $carId]
        ])->update([
            ShopCarTable::SKU_ID => $skuId
        ]);

        $message = new Success();
        $message->setSuccess((bool)$ret);
        return $message;
    }
}
