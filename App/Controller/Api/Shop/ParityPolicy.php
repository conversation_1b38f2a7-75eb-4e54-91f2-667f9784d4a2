<?php

namespace App\Controller\Api\Shop;


use Generate\Tables\Datas\SourceTable;
use Swlib\Controller\AbstractController;
use Swlib\Router\Router;
use Generate\Tables\Datas\ParityPolicyTable;
use Generate\Models\Datas\ParityPolicyModel;
use Protobuf\Datas\ParityPolicy\ParityPolicyProto;
use Protobuf\Datas\ParityPolicy\ParityPolicyListsProto;
use Throwable;


#[Router(method: 'POST')]
class ParityPolicy extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(ParityPolicyProto $request): ParityPolicyListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];

        $lists = new ParityPolicyTable()->order([
            ParityPolicyTable::ID => 'DESC',
        ])->field([
            ParityPolicyTable::FIELD_ALL,
            SourceTable::NAME
        ])->join(SourceTable::TABLE_NAME, SourceTable::ID, ParityPolicyTable::SOURCE_ID)
            ->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = ParityPolicyModel::formatItem($table);
            $proto->setStartTimeStr(date('Y-m-d', $proto->getStartTime()));
            $proto->setEndTimeStr(date('Y-m-d', $proto->getEndTime()));
            $proto->setSourceIdStr($table->getByField(SourceTable::NAME));
            $protoLists[] = $proto;
        }

        $ret = new ParityPolicyListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

}