<?php

namespace App\Controller\Api\Shop;

use Generate\Tables\Datas\ShopCategoriesTable;
use <PERSON>wl<PERSON>\Controller\AbstractController;
use Swlib\Router\Router;
use Protobuf\Category\Item;
use Protobuf\Category\Lists;
use Throwable;

#[Router(method: 'POST')]
class CategoryApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(cache: 3600, errorTitle: '获取分类失败')]
    public function lists(Item $request): Lists
    {
        $parentId = $request->getParentId();

        $where = [];
        if ($parentId) {
            $where[] = [ShopCategoriesTable::PARENT_ID, '=', $parentId];
        } else {
            $where[] = [ShopCategoriesTable::PARENT_ID, '=', 0];
        }

        $lists = (new ShopCategoriesTable)->where($where)->selectAll();

        $nodes = [];
        foreach ($lists as $list) {
            $item = new Item();
            $item->setId($list->id);
            $item->setName($list->name);
            $item->setIcon($list->icon ?: '');
            $item->setParentId($list->parentId ?: 0);
            $nodes[] = $item;
        }

        $message = new Lists();
        $message->setItems($nodes);
        return $message;
    }


}