<?php

namespace App\Controller\Api\Shop;

use Generate\Tables\Datas\CommentTable;
use Generate\Tables\Datas\ShopOrderTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use <PERSON><PERSON>ib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Comment\CommentImage;
use Protobuf\Comment\CommentItem;
use Protobuf\Comment\CommentLists;
use Protobuf\Comment\CommentTag;
use Protobuf\Comment\CommentTagLists;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Throwable;


/**
 * 评论
 */
#[Router(method: 'POST')]
class CommentApi extends AbstractController
{

    const  array COMMENT_TAGS = [
        1 => '整体评价',
        2 => '性价比',
        3 => '产品描述',
    ];


    #[Router(errorTitle: '获取评论标签列表失败')]
    public function tags(): CommentTagLists
    {
        $nodes = [];
        foreach (self::COMMENT_TAGS as $key => $value) {
            $item = new CommentTag();
            $item->setId($key);
            $item->setName($value);
            $nodes[] = $item;
        }

        $lists = new CommentTagLists();
        $lists->setTotal(count(self::COMMENT_TAGS));
        $lists->setLists($nodes);

        return $lists;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发布评论失败')]
    public function add(CommentItem $request): Success
    {

        $images = $request->getImages();
        $imagesArr = [];
        if ($images) {
            /** @var CommentImage $image */
            foreach ($images as $image) {
                $imagesArr[] = $image->getUrl();
            }
        }

        $comment = $request->getComment();
        $images = $imagesArr ? implode(',', $imagesArr) : '';
        $anonymous = $request->getAnonymous() ? 1 : 0;
        $desc_num = $request->getDescNum();
        $composite_num = $request->getCompositeNum();
        $logistics_num = $request->getLogisticsNum();
        $customer_service_num = $request->getCustomerServiceNum();
        $comment_tag = $request->getTag();
        $the_order_id = $request->getOrderId();
        $user_id = $request->getOrderId();


        if (empty($comment)) {
            throw new AppException('请输入评论内容');
        }

        if (empty($the_order_id)) {
            throw new AppException('请选择订单');
        }

        if (empty($user_id)) {
            throw new AppException('请登录');
        }

        $productId = new ShopOrderTable()->addWhere(ShopOrderTable::ORDER_ID, $the_order_id)->selectField(ShopOrderTable::PRODUCT_ID);
        if (empty($productId)) {
            throw new AppException('订单不存在');
        }

        $data = [
            CommentTable::COMMENT => $comment,
            CommentTable::IMAGES => $images,
            CommentTable::ANONYMOUS => $anonymous,
            CommentTable::DESC_NUM => $desc_num,
            CommentTable::COMPOSITE_NUM => $composite_num,
            CommentTable::LOGISTICS_NUM => $logistics_num,
            CommentTable::CUSTOMER_SERVICE_NUM => $customer_service_num,
            CommentTable::COMMENT_TAG => $comment_tag->getId(),
            CommentTable::ORDER_ID => $the_order_id,
            CommentTable::USER_ID => $user_id,
            CommentTable::PRODUCT_ID => $productId,
            CommentTable::TIME => time()
        ];

        $ret = new CommentTable()->insert($data);

        $message = new Success();
        $message->setSuccess((bool)$ret);
        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取评论失败')]
    public function myComment(Request $request): CommentLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }


        $lists = new CommentTable()->page($page, $size)
            ->field([
                CommentTable::FIELD_ALL,
                ShopProductsTable::NAME,
                ShopProductsSkuTable::NAME,
            ])
            ->where([
                [CommentTable::USER_ID, '=', $userId]
            ])
            ->select();


        // 查询出所有的产品

        $nodes = [];
        /** @var CommentTable $list */
        foreach ($lists as $list) {

            $images = [];
            if ($imgArr = $list->images) {
                foreach (explode(',', $imgArr) as $item) {
                    $imgItem = new CommentImage();
                    $imgItem->setUrl($item);
                    $images[] = $imgItem;
                }
            }

            $tag = new CommentTag();
            $tagId = $list->commentTag;
            $tag->setId($tagId);
            $tag->setName(self::COMMENT_TAGS[$tagId]);


            $item = new CommentItem();
            $item->setId($list->id);
            $item->setComment($list->comment);
            $item->setImages($images);
            $item->setAnonymous((bool)$list->anonymous);
            $item->setDescNum($list->descNum);
            $item->setCompositeNum($list->compositeNum);
            $item->setLogisticsNum($list->logisticsNum);
            $item->setCustomerServiceNum($list->customerServiceNum);
            $item->setTag($tag);
            $item->setOrderId($list->orderId);
            $item->setUserId($list->userId);
            $item->setTime(date('Y-m-d H:i:s', $list->time));
            $nodes[] = $item;
        }


        $lists = new CommentLists();
        $lists->setLists($nodes);
        return $lists;

    }

}