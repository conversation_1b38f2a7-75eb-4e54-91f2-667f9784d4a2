<?php

namespace App\Controller\Api\Shop;

use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\CollectCountTable;
use Generate\Tables\Datas\CollectTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Collect\CollectItem;
use Protobuf\Collect\CollectLists;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Throwable;

/**
 * 商品收藏
 */
#[Router(method: 'POST')]
class CollectApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '添加收藏失败')]
    public function add(CollectItem $request): Success
    {
        $userId = $request->getUserId();
        $productId = $request->getProductId();
        $skuId = $request->getSkuId();
        $businessId = $request->getBusinessId();

        if (empty($userId)) {
            throw new AppException("请登录");
        }
        if (empty($productId)) {
            throw new AppException("请选择商品");
        }

        $data = [
            CollectTable::USER_ID => $userId,
            CollectTable::PRODUCT_ID => $productId,
            CollectTable::TIME => time()
        ];
        if ($skuId) {
            $data[CollectTable::SKU_ID] = $skuId;
        }
        if ($businessId) {
            $data[CollectTable::BUSINESS_ID] = $businessId;
        }


        new CollectTable()->insert($data);


        $id = new CollectCountTable()->where([
            [CollectCountTable::PRODUCT_ID, '=', $productId]
        ])->selectField(CollectCountTable::ID);
        if ($id) {
            $ret = new CollectCountTable()->where([
                [CollectCountTable::ID, '=', $id]
            ])->update([
                CollectCountTable::NUM => CollectCountTable::NUM . '+1'
            ]);
        } else {
            $ret = new CollectCountTable()->insert([
                CollectCountTable::PRODUCT_ID => $productId,
                CollectCountTable::NUM => 1
            ]);
        }

        $message = new Success();
        $message->setSuccess((bool)$ret);
        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取是否收藏失败')]
    public function exists(CollectItem $request): CollectItem
    {
        $userId = $request->getUserId();
        $productId = $request->getProductId();

        if (empty($userId)) {
            throw new AppException("请登录");
        }
        if (empty($productId)) {
            throw new AppException("请选择商品");
        }


        $id = new CollectTable()->where([
            [CollectTable::USER_ID, '=', $userId],
            [CollectTable::PRODUCT_ID, '=', $productId]
        ])->selectField(CollectTable::ID);

        $request->setId(intval($id));
        return $request;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取收藏列表失败')]
    public function myCollect(Request $page): CollectLists
    {
        $userId = $page->getUserId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }


        $collectTable = new CollectTable();
        $lists = $collectTable->where([
            [CollectTable::USER_ID, '=', $userId]
        ])->page($page->getPage(), $page->getSize())->order([
            CollectTable::TIME => 'desc'
        ])->selectAll();
        $businessIds = $collectTable->getArrayByField(CollectTable::BUSINESS_ID);
        $productIds = $collectTable->getArrayByField(CollectTable::PRODUCT_ID);
        $skuIds = $collectTable->getArrayByField(CollectTable::SKU_ID);

        $total = new CollectTable()->where([
            [CollectTable::USER_ID, '=', $userId]
        ])->count();


        // 查询出所有的商家
        $businessArr = new BusinessTable()->where([
            [BusinessTable::ID, 'in', $businessIds]
        ])->formatId2Name(BusinessTable::ID, BusinessTable::BUSINESS_NAME);

        // 查询出所有的产品
        $shopProductArr = new ShopProductsTable()->field([
            ShopProductsTable::ID,
            ShopProductsTable::NAME,
            ShopProductsTable::PICTURE,
            ShopProductsTable::PRICE,
        ])->where([
            [ShopProductsTable::ID, 'in', $productIds]
        ])->formatId2Array(ShopProductsTable::ID);

        // 查询出所有的 SKU
        $shopProductSkuArr = new ShopProductsSkuTable()->where([
            [ShopProductsSkuTable::ID, 'in', $skuIds]
        ])->formatId2Name(ShopProductsSkuTable::ID, ShopProductsSkuTable::NAME);


        // 收藏数量
        $counts = new CollectCountTable()->where([
            [CollectCountTable::PRODUCT_ID, 'in', $productIds]
        ])->formatId2Name(CollectCountTable::PRODUCT_ID, CollectCountTable::NUM);


        $nodes = [];
        foreach ($lists as $v) {
            $item = new CollectItem();
            $item->setId($v->id);

            $item->setBusinessName($businessArr[$v->businessId] ?? "");

            $productId = $v->productId;

            $product = $shopProductArr[$productId];
            $item->setProductName($product->name);
            $item->setProductId($product->id);
            $item->setProductPicture($product->picture);
            $item->setProductPrice($product->price);
            $item->setSkuName($shopProductSkuArr[$v->skuId]);
            $item->setSkuId($v->skuId);
            $item->setNum($counts[$productId]);
            $nodes[] = $item;
        }

        $lists = new CollectLists();
        $lists->setLists($nodes);
        $lists->setTotal($total);
        return $lists;

    }


}