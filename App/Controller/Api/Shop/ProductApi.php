<?php

namespace App\Controller\Api\Shop;

use App\Model\HistorySearchModel;
use App\Model\ShopProductsModel;
use Generate\Tables\Datas\BrandTable;
use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\ShopProductByCountTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Protobuf\Order\OrderRequest;
use Protobuf\Product\HistorySearchLists;
use Protobuf\Product\ShopProductByCountItem;
use Protobuf\Product\ShopProductByCountLists;
use Protobuf\Product\ShopProductItem;
use Protobuf\Product\ShopProductLists;
use Protobuf\Product\ShopProductRequest;
use Protobuf\Product\ShopProductSku;
use Throwable;

#[Router(method: 'POST')]
class ProductApi extends AbstractController
{

    /**
     * 集采专区
     * @throws Throwable
     */
    #[Router(cache: 3600, errorTitle: '获取商品列表失败')]
    public function centralizedProcurementZone(ShopProductRequest $request): ShopProductLists
    {
        $model = new ShopProductsModel();
        return $model->getLists($request);
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取商品列表失败')]
    public function lists(ShopProductRequest $request): ShopProductLists
    {
        $model = new ShopProductsModel();
        $where = $model->getListsWhere($request);

        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $sort = $request->getSort();
        $sortField = $request->getSortField();

        $order = [];
        if ($sort && $sortField) {
            $order[$sortField] = $sort;
        }

        $shopProductsTable = new ShopProductsTable();
        $lists = $shopProductsTable->order($order)->page($page, $size)->where($where)->selectAll();
        $businessIds = $shopProductsTable->getArrayByField(ShopProductsTable::BUSINESS_ID);
        $brandIds = $shopProductsTable->getArrayByField(ShopProductsTable::BRAND_ID);
        $productIds = $shopProductsTable->getArrayByField(ShopProductsTable::ID);


        $total = new ShopProductsTable()->where($where)->count();

        $ret = [];

        if ($lists) {
            // 查询出所有的商家
            $businessArr = new BusinessTable()->where([
                [BusinessTable::ID, 'in', $businessIds]
            ])->formatId2Name(BusinessTable::ID, BusinessTable::BUSINESS_NAME);

            // 查询出所有的品牌
            $brandArr = new BrandTable()->where([
                [BrandTable::ID, 'in', $brandIds]
            ])->formatId2Name(BrandTable::ID, BrandTable::NAME);


            $skuRet = [];
            /** @var ShopProductsSkuTable $sku */
            foreach (new ShopProductsSkuTable()->where([
                [ShopProductsSkuTable::PRODUCT_ID, 'in', $productIds]
            ])->generator() as $sku) {
                $productId = $sku->productId;
                if (!array_key_exists($productId, $skuRet)) {
                    $skuRet[$productId] = [];
                }
                $skuMsg = new ShopProductSku();
                $skuMsg->setName($sku->name);
                $skuMsg->setPrice($sku->price);
                $skuMsg->setMarketPrice($sku->marketPrice);
                $skuMsg->setPicture($sku->picture);
                $skuMsg->setUnit($sku->unit);
                $skuRet[$productId][] = $skuMsg;
            }

            $imgRet = $model->getImages($productIds);

            foreach ($lists as $list) {
                $item = new ShopProductItem();
                $item->setId($list->id);
                $item->setPicture($list->picture);
                $item->setName($list->name);
                $item->setCode($list->code);
                $item->setSaleNum($list->saleNum);
                $item->setPrice($list->price);
                $item->setMarketPrice($list->marketPrice);
                $item->setBrand($brandArr[$list->brandId] ?? '');
                $item->setBusinessName($businessArr[$list->businessId] ?? '');
                $item->setSkus($skuRet[$list->id]);
                $item->setImages($imgRet[$list->id] ?? []);
                $ret[] = $item;
            }
        }


        $message = new ShopProductLists();
        $message->setLists($ret);
        $message->setTotal($total);

        return $message;
    }

    /**
     * 获取用户搜索历史
     * @throws Throwable
     */
    #[Router(errorTitle: '获取搜索历史列表失败')]
    public function historySearch(Request $page): HistorySearchLists
    {
        return HistorySearchModel::lists($page);
    }


    /**
     * 获取热门搜索历史
     * @throws Throwable
     */
    #[Router(errorTitle: '获取热门搜索历史失败')]
    public function historySearchByHot(Request $page): HistorySearchLists
    {
        return HistorySearchModel::listsByHot($page);
    }

    /**
     * 清空搜索历史
     * @throws Throwable
     */
    #[Router(errorTitle: '清空搜索历史失败')]
    public function historySearchClear(Request $page): Success
    {
        return HistorySearchModel::clear($page);
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取热门商品列表失败')]
    public function hotShopProducts(Request $page): ShopProductLists
    {
        $pageNo = $page->getPage() ?: 1;
        $pageSize = $page->getSize() ?: 10;

        $lists = new ShopProductsTable()->page($pageNo, $pageSize)->order([
            ShopProductsTable::HOT_SORT => 'desc'
        ])->selectAll();

        $total = new ShopProductsTable()->count();

        $ret = [];
        if ($lists) {
            // 查询出所有的商家
            $businessArr = [];
            $businessIdArr = array_filter(array_unique(array_column($lists, ShopProductsTable::BUSINESS_ID)));
            if ($businessIdArr) {
                $businessArr = new BusinessTable()->where([
                    [BusinessTable::ID, 'in', $businessIdArr]
                ])->formatId2Name(BusinessTable::ID, BusinessTable::BUSINESS_NAME);
            }

            foreach ($lists as $list) {
                $item = new ShopProductItem();
                $item->setId($list->id);
                $item->setPicture($list->picture);
                $item->setName($list->name);
                $item->setSaleNum($list->saleNum ?: 0);
                $item->setCode($list->code);
                $item->setPrice($list->price);
                $item->setMarketPrice($list->marketPrice);
                $item->setBusinessName($businessArr[$list->businessId] ?? '');
                $ret[] = $item;
            }

        }

        $message = new ShopProductLists();
        $message->setLists($ret);
        $message->setTotal($total);
        return $message;

    }


    /**
     * @throws Throwable
     */
    #[Router(cache: 3600, errorTitle: '获取商品详情失败')]
    public function detail(ShopProductItem $request): ShopProductItem
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException('请选择商品');
        }

        $model = new ShopProductsModel();

        $data = new ShopProductsTable()->where([
            [ShopProductsTable::ID, '=', $id]
        ])->selectOne();


        $productSkus = $model->getProductSkus([
            $data->id
        ]);

        $brands = $model->getBrands([$data->brandId]);
        $imgRet = $model->getImages([$data->id]);

        return $model->formatDetail($data, $productSkus, $brands, $imgRet);

    }


    /**
     * 获取我的常买
     * @throws Throwable
     */
    #[Router(errorTitle: '获取我的常买失败')]
    public function shopProductByCount(OrderRequest $request): ShopProductByCountLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }

        $shopProductByCountTable = new ShopProductByCountTable();
        $lists = $shopProductByCountTable->page($page, $size)->order([
            ShopProductByCountTable::COUNT => 'desc'
        ])->where([
            [ShopProductByCountTable::USER_ID, '=', $request->getUserId()]
        ])->selectAll();


        // 查询出所有的商家
        $businessArr = new BusinessTable()
            ->where([
                [BusinessTable::ID, 'in', $shopProductByCountTable->getArrayByField(ShopProductByCountTable::BUSINESS_ID)]
            ])->formatId2Name(fieldId: BusinessTable::ID, fieldName: BusinessTable::BUSINESS_NAME);

        // 查询出所有的产品
        $shopProductArr = new ShopProductsTable()->field([
            ShopProductsTable::ID,
            ShopProductsTable::NAME,
            ShopProductsTable::PICTURE,
        ])->where([
            [ShopProductsTable::ID, 'in', $shopProductByCountTable->getArrayByField(ShopProductByCountTable::PRODUCT_ID)]
        ])->formatId2Array(ShopProductsTable::ID);

        // 查询出所有的 SKU
        $shopProductSkuArr = new ShopProductsSkuTable()->field([
            ShopProductsSkuTable::ID,
            ShopProductsSkuTable::NAME,
            ShopProductsSkuTable::PRICE,
        ])->where([
            [ShopProductsSkuTable::ID, 'in', $shopProductByCountTable->getArrayByField(ShopProductByCountTable::SKU_ID)]
        ])->formatId2Array(ShopProductsSkuTable::ID);


        $nodes = [];
        foreach ($lists as $list) {
            /** @var $shopProduct ShopProductsTable */
            $shopProductTable = $shopProductArr[$list->productId];

            $businessName = $businessArr[$list->businessId];
            $productName = $shopProductTable->name;
            $productPicture = $shopProductTable->picture;
            $skuTable = $shopProductSkuArr[$list->skuId];

            $item = new ShopProductByCountItem();
            $item->setCount($list->count);
            $item->setPrice($list->price);
            $item->setBusinessName($businessName);
            $item->setProductName($productName);
            $item->setSkuId($list->skuId);
            $item->setSkuName($skuTable->name);
            $item->setSkuPrice($skuTable->price);
            $item->setPicture($productPicture);
            $nodes[] = $item;
        }

        $lists = new ShopProductByCountLists();
        $lists->setLists($nodes);
        return $lists;

    }


}