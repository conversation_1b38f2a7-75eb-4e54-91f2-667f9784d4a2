<?php

namespace App\Controller\Api\Shop;

use Generate\Tables\Datas\ParityProductsSkuTable;
use Generate\Tables\Datas\ParityProductsTable;
use Generate\Tables\Datas\ParitySourceTable;
use Generate\Tables\Datas\SourceTable;
use <PERSON><PERSON>ib\Controller\AbstractController;
use Swlib\Router\Router;
use Protobuf\Product\ShopProductItem;
use Protobuf\Product\ShopProductLists;
use Protobuf\Product\ShopProductRequest;
use Protobuf\Product\ShopProductSku;
use Protobuf\Product\ShopSourcePrice;
use Throwable;

class Parity<PERSON><PERSON> extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '获取比价商品失败')]
    public function lists(ShopProductRequest $request): ShopProductLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $keyword = $request->getKeyword();


        $query = new ParityProductsTable();
        $query = $query->page($page, $size)
            ->addWhere(ParityProductsTable::PRICE, 0, '>')
            ->addWhere(ParityProductsTable::IS_DISABLED, 0);
        if ($keyword) {
            $query->addWhere(ParityProductsTable::NAME, "%$keyword%", 'like');
        }

        $products = $query->selectAll();
        $productIds = $query->getArrayByField(ParityProductsTable::ID);

        $skus = [];
        foreach (new ParityProductsSkuTable()->where([
            [ParityProductsSkuTable::PRODUCT_ID, 'in', $productIds],
            [ParityProductsSkuTable::PRICE, '>', 0],
        ])->selectAll() as $sku) {
            $productId = $sku->productId;
            if (!isset($skus[$productId])) {
                $skus[$productId] = [];
            }
            $skus[$productId][] = $sku;
        }


        $sources = [];
        foreach (new ParitySourceTable()->setDebugSql()
                     ->join(SourceTable::TABLE_NAME, ParitySourceTable::SOURCE_ID, SourceTable::ID)
                     ->where([
                         [SourceTable::IS_DISABLED, '<>', 1],
                         [ParitySourceTable::IS_DISABLED, '<>', 1],
                         [ParitySourceTable::PRODUCT_ID, 'in', $productIds],
                         [ParitySourceTable::PRICE, '>', 0]
                     ])->selectAll() as $source) {
            $productId = $source->productId;
            if (!isset($sources[$productId])) {
                $sources[$productId] = [];
            }
            $sources[$productId][] = $source;
        }

        $sourceNames = new SourceTable()->formatId2Name(SourceTable::ID, SourceTable::NAME);

        $nodes = [];
        foreach ($products as $product) {
            $skuNodes = [];
            if (isset($skus[$product->id])) {
                /** @var ParityProductsSkuTable $sku */
                foreach ($skus[$product->id] as $sku) {
                    $sourceNodes = [];
                    if (isset($sources[$product->id])) {
                        /** @var ParitySourceTable $source */
                        foreach ($sources[$product->id] as $source) {
                            if ($source->skuId != $sku->id) continue;
                            $sourcePriceMessage = new ShopSourcePrice();
                            $sourcePriceMessage->setPrice($source->price);
                            $sourcePriceMessage->setUrl($source->url);
                            $sourcePriceMessage->setPicture($source->picture);
                            $sourcePriceMessage->setSourceName($sourceNames[$source->sourceId]);
                            if ($source->price > 0) {
                                $sourceNodes[] = $sourcePriceMessage;
                            }
                        }
                    }

                    if ($sourceNodes) {
                        usort($sourceNodes, function ($a, $b) {
                            return $a->getPrice() <=> $b->getPrice();
                        });
                        $skuMessage = new ShopProductSku();
                        $skuMessage->setId($sku->id);
                        $skuMessage->setName($sku->name);
                        $skuMessage->setPrice($sku->price);
                        $skuMessage->setSourcePrices($sourceNodes);
                        $skuNodes[] = $skuMessage;
                    }
                }
            }
            if ($skuNodes) {
                $productMessage = new ShopProductItem();
                $productMessage->setId($product->id);
                $productMessage->setPrice($product->price);
                $productMessage->setPicture($product->picture);
                $productMessage->setName($product->name);
                $productMessage->setSkus($skuNodes);
                $nodes[] = $productMessage;
            }


        }
        $message = new ShopProductLists();
        $message->setLists($nodes);
        return $message;

    }
}