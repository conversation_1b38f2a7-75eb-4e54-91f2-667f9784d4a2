<?php

namespace App\Controller\Api\Shop;

use Generate\Tables\Datas\FootprintTable;
use Generate\Tables\Datas\ShopProductsTable;
use <PERSON><PERSON>ib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Protobuf\Footprint\MyFootprintItem;
use Protobuf\Footprint\MyFootprintItemsByDay;
use Protobuf\Footprint\MyFootprintLists;
use Throwable;

#[Router(method: 'POST')]
class FootprintApi extends AbstractController
{


    /**
     * 我的足迹
     * @throws Throwable
     */
    #[Router(errorTitle: '获取足迹失败')]
    public function lists(Request $request): MyFootprintLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }

        // 先分页查询出用户的足迹
        $footprintTable = new FootprintTable();
        $footprintTable->addWhere(FootprintTable::USER_ID, $userId)->page($page, $size)->selectAll();
        // 获得这些足迹是那些天的
        $days = $footprintTable->getArrayByField(FootprintTable::THE_DAY);

        $count = new FootprintTable()->addWhere(FootprintTable::USER_ID, $userId)->count();


        // 把这些天的足迹查询出
        $footprintTable = new FootprintTable();
        $lists = $footprintTable->where([
            [FootprintTable::THE_DAY, 'in', $days]
        ])->selectAll();
        $productIds = $footprintTable->getArrayByField(FootprintTable::PRODUCT_ID);

        $listsByDay = [];
        foreach ($lists as $detail) {
            $day = $detail->theDay;
            if (!array_key_exists($day, $listsByDay)) {
                $listsByDay[$day] = [];
            }
            $listsByDay[$day][] = $detail;
        }


        // 查询出所有的产品
        $shopProductArr = new ShopProductsTable()->field([
            ShopProductsTable::ID,
            ShopProductsTable::NAME,
            ShopProductsTable::PICTURE,
        ])->where([
            [ShopProductsTable::ID, 'in', $productIds]
        ])->formatId2Array(ShopProductsTable::ID);

        $rets = [];
        foreach ($listsByDay as $day => $list) {
            $myFootprintItemsByDay = new MyFootprintItemsByDay();
            $myFootprintItemsByDay->setTotal(count($list));
            $myFootprintItemsByDay->setTheDay($day);

            $nodes = [];
            /** @var FootprintTable $v */
            foreach ($list as $v) {
                $productId = $v->productId;
                $product = $shopProductArr[$productId];
                $item = new MyFootprintItem();
                $item->setProductId($productId);
                $item->setPicture($product?->picture ?? '');
                $nodes[] = $item;
            }
            $myFootprintItemsByDay->setLists($nodes);

            $rets[] = $myFootprintItemsByDay;
        }

        $lists = new MyFootprintLists();
        $lists->setTotal($count);
        $lists->setLists($rets);
        return $lists;
    }


    /**
     * 添加我的足迹
     * @throws Throwable
     */
    #[Router(errorTitle: '添加足迹失败')]
    public function add(MyFootprintItem $request): Success
    {
        $userId = $request->getUserId();
        $productId = $request->getProductId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }
        if (empty($productId)) {
            throw new AppException("请选择商品");
        }


        $id = new FootprintTable()->where([
            [FootprintTable::USER_ID, '=', $userId],
            [FootprintTable::PRODUCT_ID, '=', $productId],
        ])->selectField(FootprintTable::ID);


        if (empty($id)) {
            $ret = new FootprintTable()->insert([
                FootprintTable::USER_ID => $userId,
                FootprintTable::PRODUCT_ID => $productId,
                FootprintTable::THE_DAY => date('Y-m-d'),
                FootprintTable::TIME => time(),
                FootprintTable::NUM => 1,
            ]);
        } else {
            $ret = new FootprintTable()->where([
                [FootprintTable::ID, '=', $id]
            ])->update([
                FootprintTable::NUM => FootprintTable::NUM . '+1',
                FootprintTable::TIME => time(),
            ]);
        }
        $lists = new Success();
        $lists->setSuccess((bool)$ret);
        return $lists;
    }


}