<?php

namespace App\Controller\Api\Shop;

use Generate\Tables\Datas\CouponTable;
use Generate\Tables\Datas\CouponUserTable;
use <PERSON>wlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Request;
use Protobuf\Coupon\CouponItem;
use Protobuf\Coupon\CouponLists;
use Throwable;

#[Router(method: 'POST')]
class Coupon<PERSON><PERSON> extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取优惠券列表失败')]
    public function lists(Request $page): CouponLists
    {
        $userId = $page->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }
        $table = new CouponUserTable();
        $lists = $table->where([
            [CouponUserTable::USER_ID, '=', $userId],
        ])->page($page->getPage(), $page->getSize())->selectAll();

        $couponIds = $table->getArrayByField(CouponUserTable::COUPON_ID);

        $total = new CouponUserTable()->where([
            [CouponUserTable::USER_ID, '=', $userId],
        ])->count();

        $coupons = new CouponTable()->where([
            [CouponTable::ID, 'in', $couponIds]
        ])->formatId2Array(CouponTable::ID);


        $ret = [];
        foreach ($lists as $list) {
            $coupon = $coupons[$list->id];
            $item = new CouponItem();
            $item->setId($list->id);
            $item->setTitle($coupon->title);
            $item->setSubTitle($coupon->subTitle);
            $item->setAmount($coupon->amount);
            $item->setConditionAmount($coupon->conditionAmount);
            $item->setStartTime($list->startTime);
            $item->setEndTime($list->endTime);
            $item->setNotes($coupon->notes);
            $item->setUserId($list->userId);
            $item->setHasUse((bool)$list->hasUse);

            $ret[] = $item;
        }

        $lists = new CouponLists();
        $lists->setLists($ret);
        $lists->setTotal($total);
        return $lists;

    }


}