<?php

namespace App\Controller\Api\Auth;


use Generate\Tables\Datas\AuthRecordsTable;
use Swlib\Controller\AbstractController;
use Swlib\Router\Router;
use Generate\Models\Datas\AuthLevelsModel;
use Generate\Tables\Datas\AuthLevelsTable;
use Protobuf\Datas\AuthLevels\AuthLevelsProto;
use Protobuf\Datas\AuthLevels\AuthLevelsListsProto;
use Throwable;


/*
* 认证等级权益表
*/

#[Router(method: 'POST')]
class AuthLevelsApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取认证等级权益表列表数据失败')]
    public function lists(AuthLevelsProto $request): AuthLevelsListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [
            AuthLevelsTable::STATUS => 1,
        ];
        $order = [AuthLevelsTable::LEVEL_ORDER => "asc"];
        $authLevelsTable = new AuthLevelsTable();
        $lists = $authLevelsTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = AuthLevelsModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new AuthLevelsListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }


}