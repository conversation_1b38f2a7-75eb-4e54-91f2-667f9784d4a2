<?php

namespace App\Controller\Api\Auth;


use App\Model\PayModel;
use Generate\Models\Datas\AuthLevelsModel;
use Protobuf\Datas\AuthLevels\AuthLevelsProto;
use Protobuf\Datas\AuthRecords\AuthRecordsListsProto;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Swlib\Table\Db;
use Generate\Models\Datas\AuthRecordsModel;
use Generate\Tables\Datas\AuthUserInfoTable;
use Generate\Tables\Datas\AuthRecordsTable;
use Generate\Tables\Datas\AuthLevelsTable;
use Generate\Tables\Datas\UserTable;
use Protobuf\Datas\AuthRecords\AuthRecordsProto;
use Throwable;
use Yansongda\Artful\Exception\ContainerException;
use Yansongda\Pay\Pay;


/*
* 用户认证信息表
*/

#[Router(method: 'POST')]
class AuthUserInfoApi extends AbstractController
{

    /**
     * 创建认证记录并获取微信支付信息
     * @throws Throwable
     */
    #[Router(errorTitle: '创建认证记录失败')]
    public function createAuthRecord(AuthRecordsProto $request): AuthRecordsProto
    {
        $userId = $request->getUserId();
        $toLevelId = $request->getToLevelId();

        if (empty($userId)) {
            throw new AppException('请输入用户ID');
        }

        if (empty($toLevelId)) {
            throw new AppException('请输入认证等级ID');
        }

        // 验证用户是否存在
        $user = new UserTable()->where([
            UserTable::ID => $userId,
        ])->selectOne();

        if (empty($user)) {
            throw new AppException('用户不存在');
        }

        if (empty($user->openid)) {
            throw new AppException('请先完成微信登录');
        }

        // 验证认证等级是否存在
        $authLevel = new AuthLevelsTable()->where([
            AuthLevelsTable::ID => $toLevelId,
            AuthLevelsTable::STATUS => 1,
        ])->selectOne();

        if (empty($authLevel)) {
            throw new AppException('认证等级不存在或已禁用');
        }


        // 检查是否已有未完成的认证记录
        $existingRecord = new AuthRecordsTable()->where([
            AuthRecordsTable::USER_ID => $userId,
            AuthRecordsTable::TO_LEVEL_ID => $toLevelId,
            AuthRecordsTable::AUTH_STATUS => 0, // 待认证
            AuthRecordsTable::PAYMENT_STATUS => [0, 1], // 待支付或支付中
        ])->selectOne();

        if ($existingRecord) {
            // 存在未完成的认证记录，请先完成支付或取消之前的认证
            return $this->_retCreateAuthRecord($user->openid, $existingRecord, $authLevel);
        }


        // 获取用户当前认证信息
        $currentAuthInfo = new AuthUserInfoTable()->where([
            AuthUserInfoTable::USER_ID => $userId,
        ])->selectOne();

        $authType = 'initial';
        $currLevel = null;
        if ($currentAuthInfo) {
            if ($currentAuthInfo->currentLevelId == $toLevelId) {
                throw new AppException('已经认证无需重复认证！');
            }
            $currLevel = new AuthLevelsTable()->where([
                AuthLevelsTable::ID => $toLevelId,
                AuthLevelsTable::STATUS => 1,
            ])->selectOne();
            if ($authLevel->depositAmount <= $currLevel->depositAmount) {
                throw new AppException('不支持降级认证！');
            }
            $authType = 'upgrade';
        }


        // 计算认证金额
        $amount = $authLevel->depositAmount;

        // 如果是升级，计算差额
        if ($authType === 'upgrade' && $currLevel) {
            $amount = $amount - $currLevel->depositAmount;
        }

        // 生成认证记录编号
        $recordNo = 'AUTH' . date('YmdHis') . rand(1000, 9999);

        // 创建认证记录
        $authRecord = new AuthRecordsTable();
        $authRecord->userId = $userId;
        $authRecord->recordNo = $recordNo;
        $authRecord->authType = $authType;
        $authRecord->fromLevelId = $currLevel ? $currLevel->id : 0;
        $authRecord->toLevelId = $toLevelId;
        $authRecord->amount = $amount;
        $authRecord->actualPaid = 0;
        $authRecord->paymentMethod = 'wechat';
        $authRecord->paymentStatus = 0; // 待支付
        $authRecord->authStatus = 0; // 待认证
        $authRecord->paymentOrderNo = $recordNo; // 使用认证记录编号作为支付订单号

        $recordId = $authRecord->insert();

        if (!$recordId) {
            throw new AppException('创建认证记录失败');
        }


        // 返回认证记录信息和支付信息
        return $this->_retCreateAuthRecord($user->openid, $authRecord, $authLevel);
    }

    /**
     * @throws Throwable
     * @throws ContainerException
     */
    private function _retCreateAuthRecord($openId, AuthRecordsTable $table, AuthLevelsTable $level): AuthRecordsProto
    {


        // 获取微信支付信息
        $params = [
            'out_trade_no' => $table->recordNo,
            'description' => '用户认证-' . $level->levelName,
            'amount' => [
                'total' => $level->depositAmount * 100, // 转换为分
                'currency' => 'CNY',
            ],
            'payer' => [
                'openid' => $openId,
            ]
        ];

        Pay::config(PayModel::getConfig());
        $response = Pay::wechat()->mini($params);
        $paymentResponse = $response->toArray();

        $table->refundAmount = 0;
        $table->refundStatus = 0;
        $proto = AuthRecordsModel::formatItem($table);
        $proto->setPaymentReturnData(json_encode($paymentResponse)); // 将支付信息返回给前端

        return $proto;
    }

    /**
     * 查询认证支付状态并更新认证信息
     * @throws Throwable
     */
    #[Router(errorTitle: '查询认证支付状态失败')]
    public function checkAuthPayment(AuthRecordsProto $request): AuthRecordsProto
    {
        $recordNo = $request->getRecordNo();

        if (empty($recordNo)) {
            throw new AppException('请输入认证记录编号');
        }

        // 查询认证记录
        $authRecord = new AuthRecordsTable()->where([
            AuthRecordsTable::RECORD_NO => $recordNo,
        ])->selectOne();

        if (empty($authRecord)) {
            throw new AppException('认证记录不存在');
        }

        // 如果已经支付成功，直接返回
        if ($authRecord->paymentStatus == 2) {
            return AuthRecordsModel::formatItem($authRecord);
        }

        // 查询微信支付状态
        Pay::config(PayModel::getConfig());

        $order = [
            'out_trade_no' => $recordNo,
            '_action' => 'mini', // 查询小程序支付
        ];

        $result = Pay::wechat()->query($order);
        $result = $result->toArray();

        $proto = AuthRecordsModel::formatItem($authRecord);

        $trade_state = $result['trade_state'];
        if ($trade_state != 'SUCCESS') {
            // 支付未成功
            return $proto;
        }

        // 支付成功，使用事务处理数据更新
        $transaction_id = $result['transaction_id'];
        $payTime = isset($result['success_time']) ? strtotime($result['success_time']) : time();

        Db::transaction(function () use ($authRecord, $transaction_id, $payTime, $result) {
            // 更新认证记录
            $updateData = [
                AuthRecordsTable::PAYMENT_STATUS => 2, // 支付成功
                AuthRecordsTable::PAYMENT_TIME => date('Y-m-d H:i:s', $payTime),
                AuthRecordsTable::PAYMENT_TRANSACTION_ID => $transaction_id,
                AuthRecordsTable::ACTUAL_PAID => $authRecord->amount,
                AuthRecordsTable::AUTH_STATUS => 2, // 认证成功
                AuthRecordsTable::AUTH_TIME => date('Y-m-d H:i:s'),
                AuthRecordsTable::PAYMENT_RETURN_DATA => json_encode($result),
            ];

            $updateResult = new AuthRecordsTable()->where([
                AuthRecordsTable::ID => $authRecord->id,
            ])->update($updateData);

            if (!$updateResult) {
                throw new AppException('更新认证记录失败');
            }

            // 更新用户认证信息
            $this->updateUserAuthInfo($authRecord->userId, $authRecord->toLevelId, $authRecord->amount);


        });

        $authRecord = new AuthRecordsTable()->where([
            AuthRecordsTable::RECORD_NO => $recordNo,
        ])->selectOne();
        return AuthRecordsModel::formatItem($authRecord);
    }

    /**
     * 取消认证记录
     * @throws Throwable
     */
    #[Router(errorTitle: '取消认证失败')]
    public function cancelAuthRecord(AuthRecordsProto $request): Success
    {
        $recordNo = $request->getRecordNo();
        $userId = $request->getUserId();

        if (empty($recordNo)) {
            throw new AppException('请输入认证记录编号');
        }

        if (empty($userId)) {
            throw new AppException('请输入用户ID');
        }

        // 查询认证记录
        $authRecord = new AuthRecordsTable()->where([
            AuthRecordsTable::RECORD_NO => $recordNo,
            AuthRecordsTable::USER_ID => $userId,
        ])->selectOne();

        if (empty($authRecord)) {
            throw new AppException('认证记录不存在');
        }

        // 检查认证记录状态是否允许取消
        // 1. 支付状态为 0(待支付)、1(支付中)、3(支付失败) 的记录可以直接取消
        // 2. 支付状态为 2(支付成功) 但认证状态为 3(认证失败) 的记录也可以取消，但需要退款
        // 3. 支付状态为 2(支付成功) 且认证状态为 2(认证成功) 的记录不能取消
        // 4. 认证状态为 4(已取消) 的记录不能重复取消

        if ($authRecord->authStatus == 4) {
            throw new AppException('认证记录已经取消，不能重复取消');
        }

        if ($authRecord->paymentStatus == 2 && $authRecord->authStatus == 2) {
            throw new AppException('认证已成功，不能取消');
        }

        $needRefund = false;

        // 如果已经支付成功但认证失败，需要处理退款
        if ($authRecord->paymentStatus == 2 && $authRecord->authStatus == 3) {
            $needRefund = true;
            // 这里可以添加退款逻辑
            // 暂时只更新状态，实际项目中需要调用支付平台的退款接口
        }

        // 更新认证记录状态为已取消
        $updateData = [
            AuthRecordsTable::AUTH_STATUS => 4, // 已取消
            AuthRecordsTable::AUTH_TIME => date('Y-m-d H:i:s'),
            AuthRecordsTable::AUTH_REMARK => $needRefund ? '用户主动取消认证（需要退款）' : '用户主动取消认证',
        ];

        // 如果需要退款，更新退款状态
        if ($needRefund) {
            $updateData[AuthRecordsTable::REFUND_STATUS] = 1; // 退款中
            $updateData[AuthRecordsTable::REFUND_TIME] = date('Y-m-d H:i:s');
            $updateData[AuthRecordsTable::REFUND_REASON] = '用户主动取消认证';
        }

        $updateResult = new AuthRecordsTable()->where([
            AuthRecordsTable::ID => $authRecord->id,
        ])->update($updateData);

        if (!$updateResult) {
            throw new AppException('取消认证失败');
        }

        $msg = new Success();
        $msg->setSuccess(true);
        return $msg;
    }

    /**
     * 更新用户认证信息
     * @throws Throwable
     */
    private function updateUserAuthInfo(int $userId, int $toLevelId, float $amount): void
    {
        // 查询用户当前认证信息
        $userAuthInfo = new AuthUserInfoTable()->where([
            AuthUserInfoTable::USER_ID => $userId,
        ])->selectOne();

        if ($userAuthInfo) {
            // 更新现有认证信息
            $newDepositPaid = $userAuthInfo->depositPaid + $amount;
            $newAvailableDeposit = $userAuthInfo->availableDeposit + $amount;

            $updateResult = new AuthUserInfoTable()->where([
                AuthUserInfoTable::ID => $userAuthInfo->id,
            ])->update([
                AuthUserInfoTable::CURRENT_LEVEL_ID => $toLevelId,
                AuthUserInfoTable::DEPOSIT_PAID => $newDepositPaid,
                AuthUserInfoTable::AVAILABLE_DEPOSIT => $newAvailableDeposit,
                AuthUserInfoTable::AUTH_STATUS => 1, // 有效
                AuthUserInfoTable::AUTH_START_TIME => date('Y-m-d H:i:s'),
                AuthUserInfoTable::AUTH_END_TIME => date('Y-m-d H:i:s', strtotime('+1 year')), // 一年后到期
            ]);

            if (!$updateResult) {
                throw new AppException('更新用户认证信息失败');
            }
        } else {
            // 创建新的认证信息
            $insertResult = new AuthUserInfoTable()->insert([
                AuthUserInfoTable::USER_ID => $userId,
                AuthUserInfoTable::CURRENT_LEVEL_ID => $toLevelId,
                AuthUserInfoTable::DEPOSIT_PAID => $amount,
                AuthUserInfoTable::DEPOSIT_USED => 0,
                AuthUserInfoTable::AVAILABLE_DEPOSIT => $amount,
                AuthUserInfoTable::AUTH_STATUS => 1, // 有效
                AuthUserInfoTable::AUTH_START_TIME => date('Y-m-d H:i:s'),
                AuthUserInfoTable::AUTH_END_TIME => date('Y-m-d H:i:s', strtotime('+1 year')), // 一年后到期
            ]);

            if (!$insertResult) {
                throw new AppException('创建用户认证信息失败');
            }
        }
    }

    /**
     * 获取用户认证记录列表
     * @throws Throwable
     */
    #[Router(errorTitle: '获取用户认证记录失败')]
    public function getUserAuthRecords(AuthRecordsProto $request): AuthRecordsListsProto
    {
        $userId = $request->getUserId();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        if (empty($userId)) {
            throw new AppException('请输入用户ID');
        }

        $authRecordsTable = new AuthRecordsTable();
        $where = [
            AuthRecordsTable::USER_ID => $userId,
        ];
        $order = [AuthRecordsTable::CREATED_AT => 'desc'];

        $lists = $authRecordsTable->where($where)->order($order)->page($page, $size)->selectAll();

        $nodes = [];
        foreach ($lists as $list) {
            $nodes[] = AuthRecordsModel::formatItem($list);
        }
        $proto = new AuthRecordsListsProto();
        $proto->setLists($nodes);

        return $proto;
    }

    /**
     * 获取当前用户的认证等级信息
     * @throws Throwable
     */
    #[Router(errorTitle: '获取用户认证等级信息失败')]
    public function getUserAuthLevel(AuthRecordsProto $request): AuthLevelsProto
    {
        $userId = $request->getUserId();

        if (empty($userId)) {
            throw new AppException('请输入用户ID');
        }

        // 查询用户当前认证信息
        $userAuthInfo = new AuthUserInfoTable()->where([
            AuthUserInfoTable::USER_ID => $userId,
        ])->selectOne();


        // 检查认证是否有效
        // 检查认证是否过期
        if ($userAuthInfo && $userAuthInfo->authStatus == 1) {
            $currentLevelId = $userAuthInfo->currentLevelId;
        } else {
            // 获取价格为0的默认认证等级（非认证等级）
            $defaultLevel = new AuthLevelsTable()->where([
                AuthLevelsTable::DEPOSIT_AMOUNT => 0,
                AuthLevelsTable::STATUS => 1
            ])->order([
                AuthLevelsTable::LEVEL_ORDER => 'desc' // 取优先级最低的免费等级
            ])->selectOne();
            $currentLevelId = $defaultLevel->id;
        }

        $authLevel = new AuthLevelsTable()->where([
            AuthLevelsTable::ID => $currentLevelId,
        ])->selectOne();


        // 使用扩展字段返回结果
        return AuthLevelsModel::formatItem($authLevel);
    }
}