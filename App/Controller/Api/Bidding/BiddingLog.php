<?php

namespace App\Controller\Api\Bidding;


use Generate\Models\Datas\BiddingShopModel;
use Generate\Models\Datas\HyCompaniesModel;
use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Models\Datas\KfSessionsModel;
use Generate\Models\Datas\ZpLeaveModel;
use Generate\Tables\Datas\BiddingShopTable;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\KfSessionsTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Swlib\Controller\AbstractController;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Swlib\Utils\Server;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\BiddingLogModel;
use Generate\Tables\Datas\BiddingLogTable;
use Protobuf\Datas\BiddingLog\BiddingLogProto;
use Protobuf\Datas\BiddingLog\BiddingLogListsProto;
use Throwable;


#[Router(method: 'POST')]
class BiddingLog extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(BiddingLogProto $request): Success
    {
        $table = BiddingLogModel::request($request);
        if (empty($table->biddingShopId)) {
            throw new AppException('请输入竞价商品ID');
        }
        if (empty($table->desc)) {
            throw new AppException('请输入参与竞价时对商品的介绍');
        }
        if (empty($table->price)) {
            throw new AppException('请输入报价价格');
        }
        if (empty($table->userId)) {
            throw new AppException('请输入用户ID');
        }
        $table->contactPhone = $table->contactPhone ?: '';
        $table->time = time();

        $res = $table->save([
            BiddingLogTable::BIDDING_SHOP_ID => $table->biddingShopId,
            BiddingLogTable::USER_ID => $table->userId,
        ]);

        if ($res) {
            // 发送到 task 进程进行后续计算
            Server::task([__CLASS__, 'saveEvent'], [
                'userId' => $table->userId,
                'biddingShopId' => $table->biddingShopId,
                'price' => $table->price,
            ]);
        }

        $find = new BiddingShopTable()->field([
            BiddingShopTable::USER_ID,
            BiddingShopTable::NAME,
        ])->where([
            BiddingShopTable::ID => $table->biddingShopId,
        ])->selectOne();
        $agentId = $find->userId;
        $name = $find->name;

        $companiesName = new ZpCompaniesTable()->where([
            ZpCompaniesTable::USER_ID => $table->userId,
        ])->selectField(ZpCompaniesTable::NAME);


        $sessionTable = new KfSessionsTable();
        $sessionTable->startTime = time();
        $sessionTable->lastTime = time();
        $sessionTable->status = KfSessionsModel::StatusOpen;
        $sessionTable->userId = $table->userId;
        $sessionTable->agentId = $agentId;
        $sessionTable->targetId = $table->biddingShopId;
        $sessionTable->targetType = 'bidding_shop';

        $sessionTable->save([
            KfSessionsTable::USER_ID => $sessionTable->userId,
            KfSessionsTable::AGENT_ID => $sessionTable->agentId,
            KfSessionsTable::TARGET_ID => $sessionTable->targetId,
            KfSessionsTable::TARGET_TYPE => $sessionTable->targetType,
        ]);


        Event::emit('MessageSendEvent', [
            'agentId' => $agentId,
            'sessionId' => $sessionTable->id,
            'targetType' => 'bidding_shop',
            'targetId' => $table->biddingShopId,
            'userId' => $table->userId,
            'content' => "[$companiesName]对您的商品[$name]进行了报价，价格为：$table->price",
            'type' => 'text',
        ]);


        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     *  task 进程进行报价后续计算
     * @throws Throwable
     */
    public function saveEvent(array $data): void
    {
        $userId = $data['userId'];
        $biddingShopId = $data['biddingShopId'];
        $price = $data['price'];

        $count = new BiddingLogTable()->where([
            BiddingLogTable::BIDDING_SHOP_ID => $biddingShopId
        ])->count();

        $sum = new BiddingLogTable()->where([
            BiddingLogTable::BIDDING_SHOP_ID => $biddingShopId
        ])->sum(BiddingLogTable::PRICE);

        $max = new BiddingLogTable()->where([
            BiddingLogTable::BIDDING_SHOP_ID => $biddingShopId
        ])->max(BiddingLogTable::PRICE);


        $min = new BiddingLogTable()->where([
            BiddingLogTable::BIDDING_SHOP_ID => $biddingShopId
        ])->min(BiddingLogTable::PRICE);

        $minName = new BiddingLogTable()->where([
            BiddingLogTable::BIDDING_SHOP_ID => $biddingShopId,
            BiddingLogTable::PRICE => $min,
        ])->join(ZpCompaniesTable::TABLE_NAME, ZpCompaniesTable::USER_ID, BiddingLogTable::USER_ID)
            ->selectField(ZpCompaniesTable::NAME);

        new BiddingShopTable()->where([
            BiddingShopTable::ID => $biddingShopId
        ])->update([
            BiddingShopTable::BIDDING_COUNT => $count,
            BiddingShopTable::BIDDING_MIN => $min,
            BiddingShopTable::BIDDING_NAME => $minName,
            BiddingShopTable::BIDDING_MAX => $max,
            BiddingShopTable::BIDDING_AVG => $sum / $count,
        ]);


        $info = new BiddingShopTable()->where([
            BiddingShopTable::ID => $biddingShopId
        ])->selectOne();

        $companies = new ZpCompaniesTable()->where([
            ZpCompaniesTable::USER_ID => $userId,
        ])->selectOne();

        Event::emit('LeaveAddEvent', [
            'send_user_id' => $userId,
            'to_user_id' => $info->userId,
            'content' => "$companies->name 对您的商品[$info->name]进行了报价，价格为：$price",
            'target_id' => $info->id,
            'target_type' => ZpLeaveModel::TargetTypeJingjia,
            'msg_type' => ZpLeaveModel::MsgTypeJj,
        ]);


    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(BiddingLogProto $request): BiddingLogListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 1000;
        $biddingShopId = $request->getBiddingShopId();
        $userId = $request->getUserId();
        $querySortField = $request->getQuerySortField() ?: BiddingLogTable::ID;
        $querySortType = $request->getQuerySortType() ?: 'desc';

        $where = [];

        if ($biddingShopId) {
            $where[] = [BiddingLogTable::BIDDING_SHOP_ID, '=', $biddingShopId];
        } else if ($userId) {
            $where[] = [BiddingLogTable::USER_ID, '=', $userId];
        }

        if (empty($where)) {
            throw new AppException('参数错误');
        }

        $order = [$querySortField => $querySortType];
        $biddingLogTable = new BiddingLogTable();
        $lists = $biddingLogTable->order($order)
            ->where($where)->page($page, $size)
            ->selectAll();

        $shopIds = $biddingLogTable->getArrayByField(BiddingLogTable::BIDDING_SHOP_ID);
        $userIds = $biddingLogTable->getArrayByField(BiddingLogTable::USER_ID);


        $shops = new HyCompaniesServiceTable()->field(HyCompaniesServiceTable::FIELD_ALL)->where([
            [HyCompaniesServiceTable::ID, 'in', $shopIds],
        ])->formatId2Array(HyCompaniesServiceTable::ID);


        $companies = new HyCompaniesTable()->where([
            [HyCompaniesTable::TYPE, '=', 1],
            [HyCompaniesTable::USER_ID, 'in', $userIds],
        ])->field(HyCompaniesTable::FIELD_ALL)->formatId2Array(HyCompaniesTable::USER_ID);

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = BiddingLogModel::formatItem($table);

            if(isset($shops[$table->biddingShopId])){
                $proto->setShop(HyCompaniesServiceModel::formatItem($shops[$table->biddingShopId]));
            }
            if(isset($companies[$table->userId])){
                $proto->setCompanies(HyCompaniesModel::formatItem($companies[$table->userId]));
            }


            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new BiddingLogListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(BiddingLogProto $request): BiddingLogProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new BiddingLogTable()->where([
            BiddingLogTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }


        $info = new BiddingShopTable()->where([
            BiddingShopTable::ID => $table->biddingShopId
        ])->selectOne();


        Event::emit('LeaveAddEvent', [
            'send_user_id' => $info->userId,
            'to_user_id' => $table->userId,
            'content' => " 您对商品[$info->name]的报价已经被查看",
            'target_id' => $info->id,
            'target_type' => ZpLeaveModel::TargetTypeJingjia,
            'msg_type' => ZpLeaveModel::MsgTypeJj,
        ]);

        return BiddingLogModel::formatItem($table);
    }

}