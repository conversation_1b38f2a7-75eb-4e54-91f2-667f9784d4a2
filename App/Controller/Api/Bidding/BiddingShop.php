<?php

namespace App\Controller\Api\Bidding;


use Generate\Tables\Datas\CitysTable;
use <PERSON>wlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\BiddingShopModel;
use Generate\Tables\Datas\BiddingShopTable;
use Protobuf\Datas\BiddingShop\BiddingShopProto;
use Protobuf\Datas\BiddingShop\BiddingShopListsProto;
use Throwable;


#[Router(method: 'POST')]
class BiddingShop extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(BiddingShopProto $request): Success
    {
        $table = BiddingShopModel::request($request);


        if (empty($table->name)) {
            throw new AppException('请输入商品名称');
        }
        if (empty($table->budgetMin)) {
            throw new AppException('请输入最低预算');
        }
        if (empty($table->budgetMax)) {
            throw new AppException('请输入最高预算');
        }
        if (empty($table->ask)) {
            throw new AppException('请输入商品要求');
        }
        if (empty($table->addr)) {
            throw new AppException('请选择区域');
        }
        if (empty($table->userId)) {
            throw new AppException('请输入用户ID');
        }
        $table->time = time();
        $table->contactPhone = $table->contactPhone ?: '';
        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(BiddingShopProto $request): BiddingShopListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $name = $request->getName();
        $userId = $request->getUserId();
        $addr = $request->getAddr();
        $min = $request->getBudgetMin();
        $max = $request->getBudgetMax();

        $where = [];
        if ($name) {
            $where[] = [BiddingShopTable::NAME, "like", "%$name%"];
        }
        if ($userId) {
            $where[] = [BiddingShopTable::USER_ID, "=", $userId];
        }
        if ($addr) {
            $where[] = [BiddingShopTable::ADDR, "=", $addr];
        }
        if ($min) {
            $where[] = [BiddingShopTable::BUDGET_MIN, ">=", $min];
        }
        if ($max) {
            $where[] = [BiddingShopTable::BUDGET_MAX, "<", $max];
        }
        $order = [BiddingShopTable::ID => "desc"];
        $biddingShopTable = new BiddingShopTable();
        $lists = $biddingShopTable->order($order)->where($where)->page($page, $size)->selectAll();
        $addrIds = $biddingShopTable->getArrayByField(BiddingShopTable::ADDR);

        $cityId2Name = new CitysTable()->where([
            [CitysTable::ID, "in", $addrIds]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);


        $protoLists = [];
        foreach ($lists as $table) {
            $proto = BiddingShopModel::formatItem($table);
            // 其他自定义字段格式化
            $proto->setAddrStr($cityId2Name[$table->addr] ?? "");
            $protoLists[] = $proto;
        }

        $ret = new BiddingShopListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(BiddingShopProto $request): BiddingShopProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new BiddingShopTable()->where([
            BiddingShopTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return BiddingShopModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(BiddingShopProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new BiddingShopTable()->where([
            BiddingShopTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

}