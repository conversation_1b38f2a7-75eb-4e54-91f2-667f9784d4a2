<?php

namespace App\Controller\Api\Business;

use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\ShopOrderTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Generate\Tables\Datas\OrderRefundTable;
use Swlib\Table\JoinEnum;
use Protobuf\BusinessCounts\BusinessCountsProto;
use Protobuf\Datas\HyCompaniesService\HyCompaniesServiceProto;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Throwable;

#[Router(method: 'POST')]
class CountsApi extends AbstractController
{
    /**
     * 获取商家统计数据
     *
     * @param HyCompaniesServiceProto $request
     * @return BusinessCountsProto
     * @throws AppException
     * @throws Throwable
     */
    #[Router(errorTitle: '获取统计数据失败')]
    public function by(HyCompaniesServiceProto $request): BusinessCountsProto
    {
        $businessId = $request->getId();
        $startTime = $request->getStartAt();
        $endTime = $request->getExpiresAt();

        // 如果没有提供时间范围，默认查询当月数据
        if (empty($startTime) || empty($endTime)) {
            $startTime = date('Y-m-01'); // 当月第一天
            $endTime = date('Y-m-t'); // 当月最后一天
        }

        if ($businessId <= 0) {
            throw new AppException('商家ID不能为空');
        }

        // 验证商家是否存在
        $business = new HyCompaniesTable()->where([
            [HyCompaniesTable::ID, '=', $businessId]
        ])->selectOne();

        if (empty($business)) {
            throw new AppException('商家不存在');
        }

        // 构建时间条件
        $timeConditions = [];
        if ($startTime) {
            $timeConditions[] = [ShopOrderTable::TIME, '>=', strtotime($startTime)];
        }
        if ($endTime) {
            $timeConditions[] = [ShopOrderTable::TIME, '<=', strtotime($endTime)];
        }

        // 1. 上架产品数量
        $shelfCount = $this->getShelfCount($businessId);

        // 2. 销售产品数量
        $soldProductCount = $this->getSoldProductCount($businessId);

        // 3. 销售额
        $totalSales = $this->getTotalSales($businessId, $timeConditions);

        // 4. 销售毛利
        $grossProfit = $this->getGrossProfit($totalSales, $businessId, $timeConditions);

        // 5. 毛利率
        $profitMargin = $totalSales > 0 ? ($grossProfit / $totalSales) * 100 : 0;

        // 6. 客户数
        $customerCount = $this->getCustomerCount($businessId, $timeConditions);

        // 7. 订单数量
        $orderCount = $this->getOrderCount($businessId, $timeConditions);

        // 8. 客单价
        $averageOrderValue = $orderCount > 0 ? $totalSales / $orderCount : 0;

        // 9. 退款金额
        $refundAmount = $this->getRefundAmount($businessId, $timeConditions);

        $msg = new BusinessCountsProto();
        $msg->setShelfCount($shelfCount);
        $msg->setSoldProductCount($soldProductCount);
        $msg->setTotalSales(round($totalSales, 2));
        $msg->setGrossProfit(round($grossProfit, 2));
        $msg->setProfitMargin(round($profitMargin, 2));
        $msg->setCustomerCount($customerCount);
        $msg->setOrderCount($orderCount);
        $msg->setAverageOrderValue(round($averageOrderValue, 2));
        $msg->setWithdrawableAmount(round($business->availableBalance, 2));
        $msg->setLockedAmount(round($business->lockedBalance, 2));
        $msg->setRefundAmount(round($refundAmount, 2));

        return $msg;
    }

    /**
     * 获取上架产品数量
     * @throws Throwable
     */
    private function getShelfCount(int $businessId): int
    {
        return new HyCompaniesServiceTable()
            ->where([
                [HyCompaniesServiceTable::COMPANIES_ID, '=', $businessId],
                [HyCompaniesServiceTable::IS_PUB, '=', 1]
            ])
            ->count();
    }

    /**
     * 获取销售产品数量（去重）
     * @throws Throwable
     */
    private function getSoldProductCount(int $businessId): int
    {
        return new HyCompaniesServiceTable()
            ->where([
                [HyCompaniesServiceTable::COMPANIES_ID, '=', $businessId],
                [HyCompaniesServiceTable::IS_PUB, '=', 1],
                [HyCompaniesServiceTable::IS_SALE, '=', 1]
            ])
            ->count();
    }

    /**
     * 获取总销售额
     * @throws Throwable
     */
    private function getTotalSales(int $businessId, array $timeConditions): float
    {
        $timeConditions[] = [ShopOrderTable::BUSINESS_ID, '=', $businessId];

        $shopOrderTable = new ShopOrderTable();
        return $shopOrderTable->where($timeConditions)->sum(ShopOrderTable::PRICE, ShopOrderTable::NUM);
    }

    /**
     * 获取销售毛利
     * 毛利 = 销售额 - 成本
     * @param float $totalSales 总销售额
     * @param int $businessId 商家ID
     * @param array $timeConditions 时间条件
     * @return float 毛利
     * @throws Throwable
     */
    private function getGrossProfit(float $totalSales, int $businessId, array $timeConditions): float
    {
        // 构建查询条件
        $conditions = $timeConditions;
        $conditions[] = [ShopOrderTable::BUSINESS_ID, '=', $businessId];

        // 查询该商家在指定时间范围内的所有订单，关联SKU表获取成本
        $shopOrderTable = new ShopOrderTable();
        $orders = $shopOrderTable
            ->field([
                ShopOrderTable::NUM,
                ShopOrderTable::PRICE,
                HyProductsSkuTable::COST
            ])
            ->join(HyProductsSkuTable::TABLE_NAME, HyProductsSkuTable::ID, ShopOrderTable::SKU_ID, JoinEnum::LEFT)
            ->where($conditions)
            ->selectAll();

        $totalCost = 0;
        foreach ($orders as $order) {
            // 获取成本，如果没有设置成本或成本为0，则跳过
            $cost = $order->getByField(HyProductsSkuTable::COST, 0);
            if ($cost > 0) {
                // 总成本 = 单个成本 * 数量
                $totalCost += $cost * $order->num;
            }
        }

        // 毛利 = 销售额 - 总成本
        return $totalSales - $totalCost;
    }

    /**
     * 获取客户数（去重）
     * @throws Throwable
     */
    private function getCustomerCount(int $businessId, array $timeConditions): int
    {
        $timeConditions[] = [ShopOrderTable::BUSINESS_ID, '=', $businessId];

        return new ShopOrderTable()
            ->where($timeConditions)->count(ShopOrderTable::USER_ID, true);


    }

    /**
     * 获取订单数量
     * @throws Throwable
     */
    private function getOrderCount(int $businessId, array $timeConditions): int
    {
        $timeConditions[] = [ShopOrderTable::BUSINESS_ID, '=', $businessId];
        return new ShopOrderTable()
            ->where($timeConditions)->count(ShopOrderTable::SN, true);
    }

    /**
     * 获取退款金额
     * @param int $businessId 商家ID
     * @param array $timeConditions 时间条件
     * @return float 退款金额
     * @throws Throwable
     */
    private function getRefundAmount(int $businessId, array $timeConditions): float
    {
        // 构建退款查询条件
        $refundConditions = [
            [OrderRefundTable::BUSINESS_ID, '=', $businessId],
            // 只统计已处理的退款（状态为1=同意）
            [OrderRefundTable::HANDLE_REFUND_STATUS, '=', 1]
        ];

        // 如果有时间条件，需要根据退款处理时间进行过滤
        if (!empty($timeConditions)) {
            foreach ($timeConditions as $condition) {
                if ($condition[0] === ShopOrderTable::TIME) {
                    // 将订单时间条件转换为退款处理时间条件
                    // 注意：handle_refund_time 是 datetime 类型，需要转换时间戳为日期格式
                    $dateTime = date('Y-m-d H:i:s', $condition[2]);
                    $refundConditions[] = [OrderRefundTable::HANDLE_REFUND_TIME, $condition[1], $dateTime];
                }
            }
        }

        // 计算退款金额：退款数量 * 实际退款单价
        return new OrderRefundTable()
            ->where($refundConditions)
            ->sum(OrderRefundTable::REFUND_NUM, OrderRefundTable::HANDLE_REFUND_PRICE);
    }
}
