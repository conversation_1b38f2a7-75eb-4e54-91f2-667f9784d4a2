<?php

namespace App\Controller\Api\Business;

use Generate\Tables\Datas\BusinessCategoryTable;
use Generate\Tables\Datas\BusinessTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Business\BusinessCategory;
use Protobuf\Business\BusinessCategoryLists;
use Protobuf\Business\BusinessItem;
use Protobuf\Business\BusinessListRequest;
use Protobuf\Business\BusinessLists;
use Protobuf\Common\Success;
use Throwable;

#[Router(method: 'POST')]
class BusinessApi extends AbstractController
{

    /**
     * 获取商家分类
     * @throws Throwable
     */
    #[Router(cache: 3600, errorTitle: '获取商家分类失败')]
    public function category(BusinessCategory $request): BusinessCategoryLists
    {

        $parentId = $request->getParentId();

        $db = new BusinessCategoryTable();

        $where = [];
        if ($parentId) {
            $where[] = [BusinessCategoryTable::PARENT_ID, '=', $parentId];
        } else {
            $where[] = [BusinessCategoryTable::PARENT_ID, 'is null'];
        }


        $nodes = [];
        foreach ($db->where($where)->selectAll() as $list) {
            $item = new BusinessCategory();
            $item->setId($list->id);
            $item->setName($list->name);
            $item->setParentId($list->parentId ?: 0);
            $nodes[] = $item;
        }

        $message = new BusinessCategoryLists();
        $message->setLists($nodes);
        return $message;
    }


    /**
     * 商家申请入住
     * @throws Throwable
     */
    #[Router(errorTitle: '商家申请入驻失败')]
    public function joinIn(BusinessItem $request): Success
    {
        if (!$request->getBusinessName()) {
            throw new AppException("请输入商家名称");
        }
        if (!$request->getUsername()) {
            throw new AppException("请输入姓名");
        }
        if (!$request->getPhone()) {
            throw new AppException("请输入电话");
        }
        if (!$request->getAddr()) {
            throw new AppException("请输入地址");
        }
        if (!$request->getLicense()) {
            throw new AppException("请输入营业执照");
        }

        $model = new BusinessTable();
        $model->businessName = $request->getBusinessName();
        $model->username = $request->getUsername();
        $model->phone = $request->getPhone();
        $model->addr = $request->getAddr();
        $model->info = $request->getInfo();
        $model->license = $request->getLicense();
        $model->otherLicense = $request->getOtherLicense();
        $model->isAuth = $request->getIsAuth() ? 1 : 0;
        $model->createTime = time();
        $model->categoryId = null;
        $model->insert();

        $success = new Success();
        $success->setSuccess(true);
        return $success;
    }

    /**
     * 获取商家列表
     * @throws Throwable
     */
    #[Router(cache: 3600, errorTitle: '获取商家列表失败')]
    public function lists(BusinessListRequest $request): BusinessLists
    {
        $page = $request->getPage();
        $pageSize = $request->getSize();
        $keyword = $request->getKeyword();


        $where = [];
        if ($keyword) {
            $where[] = [BusinessTable::BUSINESS_NAME, 'like', '"%' . $keyword . '%"'];
        }

        $dbLists = new BusinessTable()->page($page, $pageSize)->where($where)->selectAll();
        $lists = [];
        foreach ($dbLists as $dbList) {
            $item = new BusinessItem();
            $item->setBusinessName($dbList->businessName);
            $item->setLogo($dbList->logo);
            $item->setInfo($dbList->info);
        }


        $businessListsMessage = new BusinessLists();
        $businessListsMessage->setLists($lists);
        return $businessListsMessage;
    }


}