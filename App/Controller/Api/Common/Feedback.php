<?php

namespace App\Controller\Api\Common;


use Generate\Tables\Datas\FeedbackTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Protobuf\Datas\Feedback\FeedbackProto;
use Protobuf\Datas\Feedback\FeedbackListsProto;
use Protobuf\Datas\Feedback\FeedbackStatusEnum;
use Throwable;


#[Router(method: 'POST')]
class Feedback extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(FeedbackProto $request): Success
    {
        $data = $this->_request($request);
        $pk = FeedbackTable::PRI_KEY;
        if (isset($data[$pk]) && $data[$pk]) {
            $res = new FeedbackTable()->where([
                $pk => $data[$pk]
            ])->update($data);
        } else {
            $res = new FeedbackTable()->insert($data);
        }

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(FeedbackProto $request): FeedbackListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];

        $lists = new FeedbackTable()->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $protoLists[] = $this->_formatItem($table);
        }

        $ret = new FeedbackListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function showDetail(FeedbackProto $request): FeedbackProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new FeedbackTable()->where([
            FeedbackTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return $this->_formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(FeedbackProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new FeedbackTable()->where([
            FeedbackTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    private function _request(FeedbackProto $request): array
    {
        $id = $request->getId();
        $desc = $request->getDesc();
        $pics = $request->getPics();
        $userId = $request->getUserId();

        if (empty($desc)) {
            throw new AppException('请输入问题描述');
        }
        if (empty($userId)) {
            throw new AppException('请登录');
        }


        return [
            FeedbackTable::ID => $id,
            FeedbackTable::DESC => $desc,
            FeedbackTable::PICS => $pics,
            FeedbackTable::TIME => time(),
            FeedbackTable::STATUS => FeedbackStatusEnum::WAIT,
        ];
    }

    /**
     * @throws Throwable
     */
    private function _formatItem(FeedbackTable $table): FeedbackProto
    {
        $proto = new FeedbackProto();
        $proto->setId($table->id);
        $proto->setDesc($table->desc);
        $pics = $table->pics;
        $proto->setPics($pics ?  : []);
        $proto->setTimeStr(date('Y-m-d H:i:s', $table->time));
        $proto->setStatus($table->status);
        $proto->setHandleTimeStr(date('Y-m-d H:i:s', $table->handleTime));
        $proto->setUserId($table->userId);
        return $proto;
    }
}