<?php

namespace App\Controller\Api\Common;

use Generate\Tables\Datas\BannerAdTable;
use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use <PERSON>wlib\Router\Router;
use Protobuf\Banner\BannerItem;
use Protobuf\Banner\BannerLists;
use <PERSON>bu<PERSON>\Banner\BannerRequest;
use Throwable;

class Banner<PERSON>pi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(method: 'POST', cache: 3600, errorTitle: '获取banner配置失败')]
    public function lists(BannerRequest $request): BannerLists
    {
        $posId = $request->getPosId();
        $lists = new BannerAdTable()->where([
            [BannerAdTable::IS_ENABLE, '=', 1],
            [BannerAdTable::BANNER_POS_ID, '=', $posId],
        ])->selectAll();

        $nodes = [];
        foreach ($lists as $list) {
            $item = new BannerItem();
            $item->setId($list->id);
            $item->setUrl($list->url);
            $item->setPath($list->path);
            $item->setUrlType($list->urlType);
            $nodes[] = $item;
        }

        $message = new BannerLists();
        $message->setLists($nodes);
        return $message;
    }


}