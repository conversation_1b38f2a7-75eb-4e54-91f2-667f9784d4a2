<?php

namespace App\Controller\Api\Common;


use Swlib\Controller\AbstractController;
use Swlib\Router\Router;
use Generate\Tables\Datas\LanguageTable;
use Protobuf\Common\Success;
use Protobuf\Datas\Language\LanguageProto;
use Protobuf\Datas\Language\LanguageListsProto;
use Throwable;


#[Router(method: 'POST')]
class Language extends AbstractController
{


    /**
     * 这是查询语言列表，例如 简体中文 繁体中文 英文
     * @throws Throwable
     */
    #[Router(cache: 3600, errorTitle: '获取语言列表数据失败')]
    public function getLanguages(): LanguageListsProto
    {

        $languages = \Swlib\Utils\Language::getLanguages();

        $protoLists = [];
        foreach ($languages as $key => $value) {
            $proto = new LanguageProto();
            $proto->setKey($key);
            $proto->setValue($value);
            $protoLists[] = $proto;
        }

        $ret = new LanguageListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }


    /**
     * 这是查询翻译列表，例如 你好 翻译的是 hello
     * @throws Throwable
     */
    #[Router(cache: 3600, errorTitle: '获取翻译列表数据失败')]
    public function getTranslates(LanguageProto $request): LanguageListsProto
    {
        $lang = $request->getKey();
        // 获取语言常量
        $langConst = constant(LanguageTable::class . '::' . strtoupper($lang));
        $languages = new  LanguageTable()->field([
            LanguageTable::KEY,
            $langConst,
        ])->generator();


        $protoLists = [];
        /** @var LanguageTable $table */
        foreach ($languages as $table) {
            $proto = new LanguageProto();
            $proto->setKey($table->key);
            $proto->setValue($table->getByField($langConst));
            $protoLists[] = $proto;
        }

        $ret = new LanguageListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }


    /**
     * 设置翻译的使用时间，长时间未使用的可以删除
     * @throws Throwable
     */
    #[Router(errorTitle: '设置使用时间失败')]
    public function saveUse(LanguageProto $request): Success
    {
        $key = $request->getKey();


        $id = new LanguageTable()->where([
            LanguageTable::KEY => $key,
        ])->selectField(LanguageTable::ID);

        if (empty($id)) {
            new LanguageTable()->insert([
                LanguageTable::KEY => $key,
                LanguageTable::ZH => $key,
                LanguageTable::USE_TIME => time(),
            ]);
        } else {
            new LanguageTable()->where([
                LanguageTable::ID => $id,
            ])->update([
                LanguageTable::USE_TIME => time(),
            ]);
        }


        $msg = new Success();
        $msg->setSuccess(true);
        return $msg;

    }

}