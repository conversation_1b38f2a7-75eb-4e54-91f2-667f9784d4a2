<?php

namespace App\Controller\Api\Common;

use Generate\Tables\Datas\ArticleTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Response\TwigResponse;
use Swlib\Router\Router;
use Protobuf\Common\Article;
use Protobuf\Common\Request;
use Throwable;

/**
 * 文章信息列表
 */
class ArticleApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '获取内容详情失败')]
    public function detail(Request $request): Article
    {
        $id = $request->getId();

        if (empty($id)) {
            throw new AppException('参数错误');
        }

        $table = new ArticleTable()->addWhere(ArticleTable::ID, $id)->cache()->selectOne();

        if (empty($table)) {
            throw new AppException('参数错误');
        }
        $msg = new Article();
        $msg->setId($table->id);
        $msg->setTitle($table->title);
        $msg->setContent($table->content);

        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(method: 'GET')]
    public function show(): TwigResponse
    {
        $id = $this->get('id', '%s 参数是必填的', 'id');
        $table = new ArticleTable()->addWhere(ArticleTable::ID, $id)->cache()->selectOne();
        return TwigResponse::render('article/content.twig', ['table' => $table]);
    }

}