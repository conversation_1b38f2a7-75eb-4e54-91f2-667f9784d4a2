<?php

namespace App\Controller\Api\Common;

use Generate\Tables\Datas\CountriesTable;
use Swlib\Controller\AbstractController;
use Swlib\Router\Router;
use Protobuf\Common\CountryItem;
use Protobuf\Common\CountryLists;
use Throwable;

/**
 * 国家相关的信息获取接口
 */
class Countries<PERSON>pi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '获取国家信息失败')]
    public function lists(): CountryLists
    {

        $ret = [];
        foreach ((new CountriesTable)->selectAll() as $table) {
            $item = new CountryItem();
            $item->setId($table->id);
            $item->setCountryCode($table->countryCode);
            $item->setName($table->name);
            $item->setIconPath($table->iconPath);
            $item->setPhonePrefix($table->phonePrefix);
            $ret[] = $item;
        }


        $message = new CountryLists();
        $message->setLists($ret);
        return $message;
    }


}