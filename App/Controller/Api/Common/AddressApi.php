<?php

namespace App\Controller\Api\Common;

use Generate\Tables\Datas\AddressTable;
use Protobuf\Datas\Address\AddressListsProto;
use Protobuf\Datas\Address\AddressProto;
use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use <PERSON>wlib\Exception\AppException;
use Swlib\Router\Router;
use Swlib\Table\Db;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Throwable;

class AddressApi extends AbstractController
{

    /**
     * 添加或者修改收货地址
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '保存地址失败')]
    public function addOrEdit(AddressProto $request): Success
    {
        $id = $request->getId();
        $addressTable = new AddressTable();
        $addressTable->isDefault = $request->getIsDefault();
        $addressTable->userId = $request->getUserId();
        $addressTable->username = $request->getUsername();
        $addressTable->phone = $request->getPhone();
        $addressTable->addr = $request->getAddr();


        if (empty($addressTable->userId)) {
            throw new AppException('请登录');
        }

        if (empty($addressTable->username)) {
            throw new AppException('请输入姓名');
        }

        if (empty($addressTable->phone)) {
            throw new AppException('请输入电话');
        }

        if (empty($addressTable->addr)) {
            throw new AppException('请输入地址');
        }


        Db::transaction(function () use ($addressTable, $id) {
            if ($addressTable->isDefault) {
                (new AddressTable)->where([
                    [AddressTable::USER_ID, '=', $addressTable->userId],
                ])->update([
                    AddressTable::IS_DEFAULT => 0
                ]);
            }

            if ($id) {
                (new AddressTable)->where([
                    [AddressTable::ID, '=', $id],
                    [AddressTable::USER_ID, '=', $addressTable->userId],
                ])->update($addressTable->__toArray());
            } else {
                (new AddressTable)->insert($addressTable->__toArray());
            }
        });


        $message = new Success();
        $message->setSuccess(true);
        return $message;
    }


    /**
     * 设置默认
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '设置默认失败')]
    public function setDefault(AddressProto $request): Success
    {

        $id = $request->getId();
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        (new AddressTable)->where([
            [AddressTable::USER_ID, '=', $userId]
        ])->update([
            AddressTable::IS_DEFAULT => 0
        ]);

        (new AddressTable)->where([
            [AddressTable::ID, '=', $id]
        ])->update([
            AddressTable::IS_DEFAULT => 1
        ]);

        $message = new Success();
        $message->setSuccess(true);
        return $message;
    }


    /**
     * 获取默认地址
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '获取默认地址失败')]
    public function getDefault(AddressProto $request): AddressProto
    {

        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        /** @var AddressTable $find */
        $find = (new AddressTable)->where([
            [AddressTable::USER_ID, '=', $userId],
            [AddressTable::IS_DEFAULT, '=', 1],
        ])->selectOne();

        $message = new AddressProto();
        if (empty($find)) {
            $message->setAddr('');
        } else {
            $message->setId($find->id);
            $message->setUsername($find->username);
            $message->setPhone($find->phone);
            $message->setAddr($find->addr);
            $message->setIsDefault($find->isDefault);
            $message->setUserId($find->userId);
        }

        return $message;
    }


    /**
     * 删除地址
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '删除地址失败')]
    public function del(AddressProto $request): Success
    {
        $id = $request->getId();
        $userId = $request->getUserId();

        (new AddressTable)->where([
            [AddressTable::USER_ID, '=', $userId],
            [AddressTable::ID, '=', $id]
        ])->delete();

        $message = new Success();
        $message->setSuccess(true);
        return $message;
    }


    /**
     * 获取地址列表
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '获取列表失败')]
    public function lists(Request $request): AddressListsProto
    {

        $query = (new AddressTable)->page($request->getPage() ?: 1, $request->getSize() ?: 10)->where([
            [AddressTable::USER_ID, '=', $request->getUserId()]
        ])->order([
            AddressTable::ID => 'desc'
        ]);

        $nodes = [];
        foreach ($query->selectAll() as $list) {
            $item = new AddressProto();
            $item->setUsername($list->username);
            $item->setPhone($list->phone);
            $item->setAddr($list->addr);
            $item->setIsDefault($list->isDefault);
            $item->setUserId($list->userId);
            $item->setId($list->id);
            $nodes[] = $item;
        }


        $message = new AddressListsProto();
        $message->setLists($nodes);
        return $message;
    }


}