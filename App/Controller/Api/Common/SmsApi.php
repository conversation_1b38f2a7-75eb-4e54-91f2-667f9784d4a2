<?php

namespace App\Controller\Api\Common;

use App\Service\SmsService;
use Protobuf\Datas\User\UserProto;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Success;
use Throwable;

#[Router(method: 'POST')]
class SmsApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发送短信验证码失败')]
    public function send(UserProto $request): Success
    {
        $phone = $request->getPhone();
        if (empty($phone)) {
            throw new AppException("请输入手机号码");
        }

        // 正则匹配手机号码
        $pattern = '/^1([3456789])\d{9}$/';
        if (!preg_match($pattern, $phone)) {
            throw new AppException("手机号码输入错误");
        }

        SmsService::send($phone);

        $message = new Success();
        $message->setSuccess(true);
        return $message;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发送语言验证码失败')]
    public function sendYY(UserProto $request): Success
    {
        $phone = $request->getPhone();
        if (empty($phone)) {
            throw new AppException("请输入手机号码");
        }

        // 正则匹配手机号码
        $pattern = '/^1([3456789])\d{9}$/';
        if (!preg_match($pattern, $phone)) {
            throw new AppException("手机号码输入错误");
        }

        SmsService::send($phone, true);

        $message = new Success();
        $message->setSuccess(true);
        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发送验证码失败')]
    public function check(UserProto $request): Success
    {
        $phone = $request->getPhone();
        if (empty($phone)) {
            throw new AppException("请输入手机号码");
        }

        $code = $request->getCode();
        if (empty($code)) {
            throw new AppException("请输入验证码");
        }

        SmsService::check($phone, $code);

        $message = new Success();
        $message->setSuccess(true);
        return $message;
    }


}