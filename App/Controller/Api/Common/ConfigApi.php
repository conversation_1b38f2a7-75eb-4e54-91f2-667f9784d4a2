<?php

namespace App\Controller\Api\Common;

use Generate\Tables\Datas\ConfigTable;
use <PERSON>wlib\Controller\AbstractController;
use Swlib\Router\Router;
use Protobuf\Config\ConfigItem;
use Protobuf\Config\ConfigLists;
use Throwable;

class ConfigApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(method: 'POST', cache: 3600, errorTitle: '获取配置失败')]
    public function getConfig(ConfigItem $request): ConfigLists
    {
        $mark = $request->getMark();
        $markArr = explode(',', $mark);


        $lists = new ConfigTable()->where([
            [ConfigTable::IS_ENABLE, '=', 1],
            [ConfigTable::MARK, 'in', $markArr],
        ])->selectAll();

        $nodes = [];
        foreach ($lists as $list) {
            $item = new ConfigItem();
            $item->setMark($list->mark);
            $item->setValue($list->value);
            $nodes[] = $item;
        }

        $message = new ConfigLists();
        $message->setLists($nodes);
        return $message;
    }


}