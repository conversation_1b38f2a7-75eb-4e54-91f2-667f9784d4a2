<?php

namespace App\Controller\Api\HuoYuan;


use Generate\Tables\Datas\HyCompaniesTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\HyCompaniesWithdrawalLogModel;
use Generate\Tables\Datas\HyCompaniesWithdrawalLogTable;
use Protobuf\Datas\HyCompaniesWithdrawalLog\HyCompaniesWithdrawalLogProto;
use Protobuf\Datas\HyCompaniesWithdrawalLog\HyCompaniesWithdrawalLogListsProto;
use Throwable;


/*
* 账户余额提现日志
*/

#[Router(method: 'POST')]
class HyCompaniesWithdrawalLogApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存账户余额提现日志失败')]
    public function save(HyCompaniesWithdrawalLogProto $request): Success
    {
        $table = HyCompaniesWithdrawalLogModel::request($request);


        $hyCompanies = new HyCompaniesTable()->where([
            [HyCompaniesTable::ID, '=', $table->companiesId]
        ])->selectOne();
        if (empty($hyCompanies)) {
            throw new AppException('参数错误');
        }


        if (empty($table->score)) {
            throw new AppException('请输入本次金额');
        }
        $table->time = time();

        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取账户余额提现日志列表数据失败')]
    public function lists(HyCompaniesWithdrawalLogProto $request): HyCompaniesWithdrawalLogListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        $order = [HyCompaniesWithdrawalLogTable::PRI_KEY => "desc"];
        $hyCompaniesWithdrawalLogTable = new HyCompaniesWithdrawalLogTable();
        $lists = $hyCompaniesWithdrawalLogTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = HyCompaniesWithdrawalLogModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new HyCompaniesWithdrawalLogListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

}