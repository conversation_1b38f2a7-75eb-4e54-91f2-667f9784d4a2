<?php

namespace App\Controller\Api\HuoYuan;


use App\Service\HyCompaniesService;
use App\Service\WxService;
use Generate\Tables\Datas\HyCompaniesTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Queue\MessageQueue;
use Swlib\Router\Router;
use Generate\Models\Datas\HyCompaniesWithdrawalLogModel;
use Generate\Tables\Datas\HyCompaniesWithdrawalLogTable;
use Protobuf\Datas\HyCompaniesWithdrawalLog\HyCompaniesWithdrawalLogProto;
use Protobuf\Datas\HyCompaniesWithdrawalLog\HyCompaniesWithdrawalLogListsProto;
use Swlib\Table\Db;
use Swlib\Utils\Log;
use Throwable;


/*
* 账户余额提现日志
*/

#[Router(method: 'POST')]
class HyCompaniesWithdrawalLogApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存账户余额提现日志失败')]
    public function save(HyCompaniesWithdrawalLogProto $request): Success
    {
        $table = HyCompaniesWithdrawalLogModel::request($request);

        // 1. 基础参数校验
        $hyCompanies = new HyCompaniesTable()->where([
            [HyCompaniesTable::ID, '=', $table->companiesId]
        ])->selectOne();
        if (empty($hyCompanies)) {
            throw new AppException('商家不存在');
        }

        if (empty($table->score)) {
            throw new AppException('请输入提现金额');
        }

        // 2. 金额校验（score字段存储的是分，需要转换为元进行比较）
        $withdrawalAmount = $table->score / 100; // 转换为元
        if ($withdrawalAmount <= 0) {
            throw new AppException('提现金额必须大于0');
        }

        if ($withdrawalAmount < 1) {
            throw new AppException('提现金额不能少于1元');
        }

        if ($hyCompanies->availableBalance < $withdrawalAmount) {
            throw new AppException('可用余额不足');
        }

        // 3. 微信提现特殊校验
        if ($table->isWechat) {
            if (empty($table->extStr)) {
                throw new AppException('请提供收款用户OpenID');
            }

            // 微信转账金额限制（1-200元）
            if ($table->score < 100 || $table->score > 20000) {
                throw new AppException('微信提现金额必须在1-200元之间');
            }
        }

        // 4. 设置初始状态和时间
        $table->time = time();
        $table->isComplete = 0; // 初始状态为未完成

        // 5. 开启事务处理
        return Db::transaction(function () use ($table, $hyCompanies, $withdrawalAmount) {
            // 保存提现记录
            $res = $table->save();
            if (!$res) {
                throw new AppException('保存提现记录失败');
            }

            try {
                if ($table->isWechat) {
                    // 微信提现流程
                    $this->processWechatWithdrawal($table, $hyCompanies, $withdrawalAmount);
                } elseif ($table->isBank) {
                    // 银行卡提现（暂时不实现）
                    throw new AppException('银行卡提现功能暂未开放');
                } elseif ($table->isAlipay) {
                    // 支付宝提现（暂时不实现）
                    throw new AppException('支付宝提现功能暂未开放');
                } else {
                    throw new AppException('请选择提现方式');
                }

                $msg = new Success();
                $msg->setSuccess(true);
                return $msg;
            } catch (Throwable $e) {
                // 记录错误日志
                Log::save("提现处理失败 - 提现记录ID: {$table->id}, 错误: " . $e->getMessage());
                throw $e;
            }
        });
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取账户余额提现日志列表数据失败')]
    public function lists(HyCompaniesWithdrawalLogProto $request): HyCompaniesWithdrawalLogListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        $order = [HyCompaniesWithdrawalLogTable::PRI_KEY => "desc"];
        $hyCompaniesWithdrawalLogTable = new HyCompaniesWithdrawalLogTable();
        $lists = $hyCompaniesWithdrawalLogTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = HyCompaniesWithdrawalLogModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new HyCompaniesWithdrawalLogListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * 处理微信提现
     * @param HyCompaniesWithdrawalLogTable $withdrawalLog
     * @param HyCompaniesTable $company
     * @param float $withdrawalAmount
     * @throws Throwable
     */
    private function processWechatWithdrawal(HyCompaniesWithdrawalLogTable $withdrawalLog, HyCompaniesTable $company, float $withdrawalAmount): void
    {
        // 1. 先扣除商家可用余额（冻结资金）
        $message = "微信提现扣款 - 提现记录ID: {$withdrawalLog->id}";
        $deductResult = HyCompaniesService::decrAvailableBalance(
            $company->id,
            $withdrawalAmount,
            $message
        );

        if (!$deductResult) {
            throw new AppException('扣除余额失败');
        }

        // 2. 生成商户订单号
        $orderNo = 'WX_WITHDRAW_' . date('YmdHis') . '_' . $withdrawalLog->id . '_' . rand(1000, 9999);

        // 3. 调用微信转账接口
        $openid = $withdrawalLog->extStr; // OpenID存储在extStr字段中
        $transferResult = WxService::transfer(
            $openid,
            $withdrawalLog->score, // 金额（分）
            $orderNo,
            '' // 用户姓名（可选）
        );

        // 4. 更新提现记录的商户订单号
        $withdrawalLog->update([
            HyCompaniesWithdrawalLogTable::EXT_STR => $openid . '|' . $orderNo, // 存储OpenID和订单号
            HyCompaniesWithdrawalLogTable::MSG => $message
        ]);

        // 5. 处理转账结果
        if (isset($transferResult['code']) && $transferResult['code'] === 'FAIL') {
            // 转账失败，回滚余额
            HyCompaniesService::incrAvailableBalance(
                $company->id,
                $withdrawalAmount,
                "微信提现失败回滚 - 提现记录ID: {$withdrawalLog->id}, 原因: " . ($transferResult['message'] ?? '未知错误')
            );

            // 更新提现记录状态
            $withdrawalLog->update([
                HyCompaniesWithdrawalLogTable::IS_COMPLETE => 1,
                HyCompaniesWithdrawalLogTable::COMPLETE_TIME => time(),
                HyCompaniesWithdrawalLogTable::COMPLETE_MSG => '转账失败: ' . ($transferResult['message'] ?? '未知错误')
            ]);

            throw new AppException('微信转账失败: ' . ($transferResult['message'] ?? '未知错误'));
        }

        // 6. 转账请求成功，创建延迟队列任务查询转账状态
        $queueData = [
            'withdrawal_log_id' => $withdrawalLog->id,
            'company_id' => $company->id,
            'order_no' => $orderNo,
            'withdrawal_amount' => $withdrawalAmount,
            'query_count' => 0, // 查询次数计数
            'max_query_count' => 30 // 最大查询次数
        ];

        // 创建延迟队列任务，5秒后开始第一次查询
        MessageQueue::push(
            [self::class, 'queryWechatTransferStatus'],
            $queueData,
            5, // 5秒后开始查询
            30, // 最大执行30次
            [5, 10, 20, 30, 60, 90, 120, 180, 240, 300, 600, 1200, 1800, 3600] // 重试间隔
        );

        Log::save("微信提现请求成功 - 提现记录ID: {$withdrawalLog->id}, 订单号: {$orderNo}");
    }

    /**
     * 查询微信转账状态（队列处理方法）
     * @param array $data
     * @return bool true表示任务完成，false表示需要继续重试
     * @throws Throwable
     */
    public static function queryWechatTransferStatus(array $data): bool
    {
        $withdrawalLogId = $data['withdrawal_log_id'];
        $companyId = $data['company_id'];
        $orderNo = $data['order_no'];
        $withdrawalAmount = $data['withdrawal_amount'];
        $queryCount = $data['query_count'] ?? 0;
        $maxQueryCount = $data['max_query_count'] ?? 30;

        try {
            // 更新查询次数
            $queryCount++;

            // 查询提现记录
            $withdrawalLog = new HyCompaniesWithdrawalLogTable()->where([
                [HyCompaniesWithdrawalLogTable::ID, '=', $withdrawalLogId]
            ])->selectOne();

            if (!$withdrawalLog) {
                Log::save("查询微信转账状态失败 - 提现记录不存在: {$withdrawalLogId}");
                return true; // 记录不存在，停止查询
            }

            // 如果已经完成，停止查询
            if ($withdrawalLog->isComplete) {
                return true;
            }

            // 调用微信查询接口
            $queryResult = WxService::queryTransferByOrderNo($orderNo);

            if (!$queryResult || isset($queryResult['code'])) {
                // 查询失败或返回错误
                if ($queryCount >= $maxQueryCount) {
                    // 达到最大查询次数，标记为查询超时
                    self::handleTransferTimeout($withdrawalLog, $companyId, $withdrawalAmount, $orderNo);
                    return true;
                }

                Log::save("查询微信转账状态失败 - 第{$queryCount}次查询, 订单号: {$orderNo}, 错误: " . json_encode($queryResult));
                return false; // 继续重试
            }

            // 根据查询结果处理
            $state = $queryResult['state'] ?? '';

            switch ($state) {
                case 'SUCCESS':
                    // 转账成功
                    self::handleTransferSuccess($withdrawalLog, $queryResult);
                    return true;

                case 'FAIL':
                case 'CANCELLED':
                    // 转账失败或被取消，回滚余额
                    self::handleTransferFailed($withdrawalLog, $companyId, $withdrawalAmount, $queryResult);
                    return true;

                case 'ACCEPTED':
                case 'PROCESSING':
                case 'WAIT_USER_CONFIRM':
                case 'TRANSFERING':
                    // 转账处理中，继续查询
                    if ($queryCount >= $maxQueryCount) {
                        // 达到最大查询次数，但转账仍在处理中
                        self::handleTransferTimeout($withdrawalLog, $companyId, $withdrawalAmount, $orderNo);
                        return true;
                    }

                    Log::save("微信转账处理中 - 第{$queryCount}次查询, 订单号: {$orderNo}, 状态: {$state}");
                    return false; // 继续重试

                default:
                    // 未知状态
                    if ($queryCount >= $maxQueryCount) {
                        self::handleTransferTimeout($withdrawalLog, $companyId, $withdrawalAmount, $orderNo);
                        return true;
                    }

                    Log::save("微信转账状态未知 - 第{$queryCount}次查询, 订单号: {$orderNo}, 状态: {$state}");
                    return false; // 继续重试
            }

        } catch (Throwable $e) {
            Log::save("查询微信转账状态异常 - 订单号: {$orderNo}, 错误: " . $e->getMessage());

            if ($queryCount >= $maxQueryCount) {
                // 达到最大查询次数，标记为异常
                try {
                    self::handleTransferTimeout($withdrawalLog ?? null, $companyId, $withdrawalAmount, $orderNo);
                } catch (Throwable $ex) {
                    Log::save("处理转账超时异常 - 订单号: {$orderNo}, 错误: " . $ex->getMessage());
                }
                return true;
            }

            return false; // 继续重试
        }
    }

    /**
     * 处理转账成功
     * @param HyCompaniesWithdrawalLogTable $withdrawalLog
     * @param array $queryResult
     * @throws Throwable
     */
    private static function handleTransferSuccess(HyCompaniesWithdrawalLogTable $withdrawalLog, array $queryResult): void
    {
        $transferBillNo = $queryResult['transfer_bill_no'] ?? '';
        $updateTime = $queryResult['update_time'] ?? '';

        $withdrawalLog->update([
            HyCompaniesWithdrawalLogTable::IS_COMPLETE => 1,
            HyCompaniesWithdrawalLogTable::COMPLETE_TIME => time(),
            HyCompaniesWithdrawalLogTable::COMPLETE_MSG => '转账成功',
            HyCompaniesWithdrawalLogTable::COMPLETE_SERIAL_NUMBER => $transferBillNo
        ]);

        Log::save("微信转账成功 - 提现记录ID: {$withdrawalLog->id}, 微信订单号: {$transferBillNo}");
    }

    /**
     * 处理转账失败
     * @param HyCompaniesWithdrawalLogTable $withdrawalLog
     * @param int $companyId
     * @param float $withdrawalAmount
     * @param array $queryResult
     * @throws Throwable
     */
    private static function handleTransferFailed(HyCompaniesWithdrawalLogTable $withdrawalLog, int $companyId, float $withdrawalAmount, array $queryResult): void
    {
        $failReason = $queryResult['fail_reason'] ?? '转账失败';

        // 回滚余额
        HyCompaniesService::incrAvailableBalance(
            $companyId,
            $withdrawalAmount,
            "微信提现失败回滚 - 提现记录ID: {$withdrawalLog->id}, 原因: {$failReason}"
        );

        // 更新提现记录状态
        $withdrawalLog->update([
            HyCompaniesWithdrawalLogTable::IS_COMPLETE => 1,
            HyCompaniesWithdrawalLogTable::COMPLETE_TIME => time(),
            HyCompaniesWithdrawalLogTable::COMPLETE_MSG => "转账失败: {$failReason}"
        ]);

        Log::save("微信转账失败 - 提现记录ID: {$withdrawalLog->id}, 原因: {$failReason}");
    }

    /**
     * 处理转账查询超时
     * @param HyCompaniesWithdrawalLogTable|null $withdrawalLog
     * @param int $companyId
     * @param float $withdrawalAmount
     * @param string $orderNo
     * @throws Throwable
     */
    private static function handleTransferTimeout(?HyCompaniesWithdrawalLogTable $withdrawalLog, int $companyId, float $withdrawalAmount, string $orderNo): void
    {
        if (!$withdrawalLog) {
            Log::save("处理转账超时失败 - 提现记录不存在, 订单号: {$orderNo}");
            return;
        }

        // 查询超时，暂不回滚余额，标记为需要人工处理
        $withdrawalLog->update([
            HyCompaniesWithdrawalLogTable::IS_COMPLETE => 0, // 保持未完成状态
            HyCompaniesWithdrawalLogTable::COMPLETE_MSG => "转账状态查询超时，需要人工核实 - 订单号: {$orderNo}"
        ]);

        Log::save("微信转账查询超时 - 提现记录ID: {$withdrawalLog->id}, 订单号: {$orderNo}, 需要人工核实");
    }

}