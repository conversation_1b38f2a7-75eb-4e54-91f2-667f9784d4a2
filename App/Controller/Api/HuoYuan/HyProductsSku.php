<?php

namespace App\Controller\Api\HuoYuan;


use App\Service\ExportHyProductsSkuService;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\ShopOrderTable;
use Protobuf\Datas\HyCompaniesService\HyCompaniesServiceProto;
use Swlib\Controller\AbstractController;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\HyProductsSkuModel;
use Generate\Tables\Datas\HyProductsSkuTable;
use Protobuf\Datas\HyProductsSku\HyProductsSkuProto;
use Protobuf\Datas\HyProductsSku\HyProductsSkuListsProto;
use Swlib\Table\Db;
use Swlib\Table\Expression;
use Swlib\Utils\Func;
use Throwable;


#[Router(method: 'POST')]
class HyProductsSku extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(HyProductsSkuProto $request): Success
    {
        $table = HyProductsSkuModel::request($request);

        if (empty($table->productId)) {
            throw new AppException('请输入产品ID');
        }
        if (empty($table->name)) {
            throw new AppException('请输入规格名称');
        }

        $res = $table->save();
        Event::emit('update.product.minPrice', [
            'productId' => $table->productId
        ]);
        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(HyProductsSkuProto $request): HyProductsSkuListsProto
    {
        $productId = $request->getProductId();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 1000;

        $where = [
            HyProductsSkuTable::PRODUCT_ID => $productId,
        ];
        $order = [HyProductsSkuTable::ID => "desc"];
        $hyProductsSkuTable = new HyProductsSkuTable();
        $lists = $hyProductsSkuTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            if (empty($table->price)) {
                $table->price = 0;
            }
            $proto = HyProductsSkuModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new HyProductsSkuListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(HyProductsSkuProto $request): HyProductsSkuProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new HyProductsSkuTable()->where([
            HyProductsSkuTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return HyProductsSkuModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '统计SKU失败')]
    public function count(): HyProductsSkuListsProto
    {
        $count = new HyProductsSkuTable()->count();
        $msg = new HyProductsSkuListsProto();
        $msg->setTotal($count);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(HyProductsSkuProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new HyProductsSkuTable()->where([
            HyProductsSkuTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '设置成本失败')]
    public function setCost(HyProductsSkuProto $request): Success
    {
        $id = $request->getId();
        $cost = $request->getCost();
        if (empty($id) || empty($cost)) {
            throw new AppException("参数错误");
        }

        $res = new HyProductsSkuTable()->where([
            HyProductsSkuTable::ID => $id,
        ])->update([
            HyProductsSkuTable::COST => $cost,
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * 批量增加点击量
     * @throws Throwable
     */
    #[Router(errorTitle: '增加点击量失败')]
    public function increaseClick(HyProductsSkuProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("请提供SKU ID");
        }

        // 增加点击量
        $res = new HyProductsSkuTable()->where([
            [HyProductsSkuTable::PRODUCT_ID, '=', $id]
        ])->update([
            HyProductsSkuTable::CLICK => Db::incr(HyProductsSkuTable::CLICK)
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * 批量增加点击量
     * @throws Throwable
     */
    #[Router(errorTitle: '增加库存失败')]
    public function updateInventory(HyProductsSkuProto $request): Success
    {
        $id = $request->getId();
        $inventory = $request->getInventory();
        if (empty($id) || empty($inventory)) {
            throw new AppException("请提供SKU ID");
        }

        // 增加点击量
        $res = new HyProductsSkuTable()->where([
            [HyProductsSkuTable::ID, '=', $id]
        ])->update([
            HyProductsSkuTable::INVENTORY => Db::incr(HyProductsSkuTable::INVENTORY,$inventory)
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function byCompanies(HyCompaniesServiceProto $request): HyProductsSkuListsProto
    {
        $userId = $request->getUserId();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $action = $request->getExtStr();

        $companiesId = new HyCompaniesTable()->where([
            HyCompaniesTable::USER_ID => $userId,
            HyCompaniesTable::TYPE => 1,
        ])->selectField(HyCompaniesTable::ID);
        if (empty($companiesId)) {
            throw new AppException('参数错位');
        }


        $where = [
            [HyCompaniesServiceTable::COMPANIES_ID, '=', $companiesId],
        ];

        // 查询库存不足的
        if ($action === 'inventory') {
            $where[] = [HyProductsSkuTable::INVENTORY, '<', 100];
        }

        $order = [HyProductsSkuTable::ID => "desc"];
        $hyProductsSkuTable = new HyProductsSkuTable();
        $lists = $hyProductsSkuTable->order($order)
            ->field(HyProductsSkuTable::FIELD_ALL)
            ->join(HyCompaniesServiceTable::TABLE_NAME, HyCompaniesServiceTable::ID, HyProductsSkuTable::PRODUCT_ID)
            ->where($where)->page($page, $size)->selectAll();

        $total = new HyProductsSkuTable()->order($order)
            ->join(HyCompaniesServiceTable::TABLE_NAME, HyCompaniesServiceTable::ID, HyProductsSkuTable::PRODUCT_ID)
            ->where($where)->count(HyProductsSkuTable::ID);

        // 查询产品名称
        $productIds = $hyProductsSkuTable->getArrayByField(HyProductsSkuTable::PRODUCT_ID);
        $products = [];
        if ($productIds) {
            $products = new HyCompaniesServiceTable()->where([
                [HyCompaniesServiceTable::ID, 'in', $productIds],
            ])->formatId2Name(HyCompaniesServiceTable::ID, HyCompaniesServiceTable::PRODUCT_NAME);
        }


        if ($action !== 'inventory') {
            // 查询销售数量
            $skuIds = $hyProductsSkuTable->getArrayByField(HyProductsSkuTable::ID);
            $saleNums = [];
            $usersBuySkuId = [];
            if ($skuIds) {

                $countField = new Expression('count(DISTINCT shop_order.user_id)');
                $usersBuySkuId = new ShopOrderTable()->where([
                    [ShopOrderTable::SKU_ID, 'in', $skuIds],
                ])->group(ShopOrderTable::SKU_ID)->formatId2Name(ShopOrderTable::SKU_ID, $countField);

                $sumField = ShopOrderTable::getFuncField(ShopOrderTable::NUM, 'SUM');
                $saleNums = new ShopOrderTable()->where([
                    [ShopOrderTable::SKU_ID, 'in', $skuIds],
                ])->group(ShopOrderTable::SKU_ID)->formatId2Name(ShopOrderTable::SKU_ID, $sumField);

            }
        }


        $protoLists = [];
        foreach ($lists as $table) {
            if (empty($table->price)) {
                $table->price = 0;
            }

            // 产品名称
            $proto = HyProductsSkuModel::formatItem($table);
            if (isset($products[$table->productId])) {
                $proto->setProductName($products[$table->productId]);
            }


            if (isset($saleNums[$table->id])) {
                $saleNum = $saleNums[$table->id];
                // 销售额
                $saleVolume = $saleNum * $table->price;
                $proto->setSalesVolume($saleVolume);

                //成本
                $cost = $saleNum * $table->cost;

                // 毛利
                $grossProfit = $saleVolume - $cost;
                $proto->setGrossProfit($grossProfit);

                // 毛利率
                $grossProfitMargin = ($grossProfit / $saleVolume) * 100;
                $proto->setGrossProfitMargin($grossProfitMargin);

            }

            if (isset($usersBuySkuId[$table->id])) {
                // 购买人数
                $userNum = $usersBuySkuId[$table->id];

                // 转化率
                if ($table->view) {
                    $conversionRate = ($userNum / $table->view) * 100;
                    $proto->setConversionRate($conversionRate);
                }
            }


            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new HyProductsSkuListsProto();
        $ret->setLists($protoLists);
        $ret->setTotal($total);
        return $ret;
    }


    /**
     * 导出SKU数据
     * @throws Throwable
     */
    #[Router(errorTitle: '导出数据失败')]
    public function export(HyCompaniesServiceProto $request): HyProductsSkuProto
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('缺少用户ID参数');
        }

        // 生成文件名
        $fileName = 'hy_products_sku_export_' . date('YmdHis') . '.xlsx';
        $filePath = 'export/' . $fileName;

        // 启动导出任务
        $taskId = ExportHyProductsSkuService::export([
            'filePath' => $filePath,
            'userId' => $userId
        ]);


        // 创建一个包含任务ID的proto
        $proto = new HyProductsSkuProto();
        $proto->setExtStr($taskId);
        $proto->setProductName(Func::getHost() . '/' . $filePath);

        return $proto;
    }

    /**
     * 查询导出进度
     * @throws Throwable
     */
    #[Router(errorTitle: '查询导出进度失败')]
    public function exportProgress(HyCompaniesServiceProto $request): HyProductsSkuProto
    {
        $taskId = $request->getExtStr();
        if (empty($taskId)) {
            throw new AppException('缺少任务ID参数');
        }

        $progress = ExportHyProductsSkuService::getProgress($taskId);


        // 创建一个包含进度信息的proto
        $proto = new HyProductsSkuProto();
        $proto->setExtStr($taskId);
        $proto->setExtInt((int)$progress);


        return $proto;
    }

}
