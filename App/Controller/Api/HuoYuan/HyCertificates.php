<?php

namespace App\Controller\Api\HuoYuan;


use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\HyCertificatesModel;
use Generate\Tables\Datas\HyCertificatesTable;
use Protobuf\Datas\HyCertificates\HyCertificatesProto;
use Protobuf\Datas\HyCertificates\HyCertificatesListsProto;
use Throwable;


#[Router(method: 'POST')]
class HyCertificates extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(HyCertificatesProto $request): Success
    {
        $table = HyCertificatesModel::request($request);

        if (empty($table->companyId)) {
            throw new AppException('请输入所属公司ID');
        }
        if (empty($table->certType)) {
            throw new AppException('请输入证书类型');
        }
        if (empty($table->imagePath)) {
            throw new AppException('请输入证书图片路径');
        }
//        if (empty($table->certName)) {
//            throw new AppException('请输入证书名称');
//        }
//        if (empty($table->certNo)) {
//            throw new AppException('请输入证书编号');
//        }

//        if (empty($table->issueDate)) {
//            throw new AppException('请输入颁发日期');
//        }
//        if (empty($table->expiryDate)) {
//            throw new AppException('请输入过期日期');
//        }

        $table->time = time();

        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(HyCertificatesProto $request): HyCertificatesProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new HyCertificatesTable()->where([
            HyCertificatesTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return HyCertificatesModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(HyCertificatesProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new HyCertificatesTable()->where([
            HyCertificatesTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}