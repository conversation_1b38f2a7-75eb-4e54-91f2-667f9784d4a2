<?php

namespace App\Controller\Api\HuoYuan;


use <PERSON><PERSON>ib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\HyProductImagesModel;
use Generate\Tables\Datas\HyProductImagesTable;
use Protobuf\Datas\HyProductImages\HyProductImagesProto;
use Protobuf\Datas\HyProductImages\HyProductImagesListsProto;
use Throwable;


#[Router(method: 'POST')]
class HyProductImages extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(HyProductImagesProto $request): Success
    {
        $table = HyProductImagesModel::request($request);

        if (empty($table->productId)) {
            throw new AppException('请输入产品ID');
        }
        if (empty($table->imagePath)) {
            throw new AppException('请输入图片路径');
        }
//        if (empty($table->isMain)) {
//            throw new AppException('请输入是否主图');
//        }
//        if (empty($table->sortOrder)) {
//            throw new AppException('请输入排序序号');
//        }


        $table->time = time();
        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(HyProductImagesProto $request): HyProductImagesListsProto
    {
        $productId = $request->getProductId();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [
            HyProductImagesTable::PRODUCT_ID => $productId,
        ];
        $order = [HyProductImagesTable::ID => "desc"];
        $hyProductImagesTable = new HyProductImagesTable();
        $lists = $hyProductImagesTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = HyProductImagesModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new HyProductImagesListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(HyProductImagesProto $request): HyProductImagesProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new HyProductImagesTable()->where([
            HyProductImagesTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return HyProductImagesModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(HyProductImagesProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new HyProductImagesTable()->where([
            HyProductImagesTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}