<?php

namespace App\Controller\Api\HuoYuan;


use Generate\Tables\Datas\HyProductsSkuTable;
use Protobuf\Datas\HyProductsSku\HyProductsSkuProto;
use <PERSON>wlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\HyProductsModel;
use Generate\Tables\Datas\HyProductsTable;
use Protobuf\Datas\HyProducts\HyProductsProto;
use Protobuf\Datas\HyProducts\HyProductsListsProto;
use Throwable;


#[Router(method: 'POST')]
class HyProducts extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(HyProductsProto $request): Success
    {
        $table = HyProductsModel::request($request);


        if (empty($table->companyId)) {
            throw new AppException('请输入所属公司ID');
        }
        if (empty($table->name)) {
            throw new AppException('请输入产品名称');
        }
        if (empty($table->description)) {
            throw new AppException('请输入产品描述');
        }
//        if (empty($table->certificateNo)){
//            throw new AppException('请输入产品证件号');
//        }
//        if (empty($table->insuranceCode)){
//            throw new AppException('请输入医保编码');
//        }

        $skus = $request->getSkus();


        $table->time = time();
        $res = $table->save();


        new HyProductsSkuTable()->where([
            HyProductsSkuTable::PRODUCT_ID => $table->id,
        ])->delete();

        $skuSave = [];
        /** @var HyProductsSkuProto $sku */
        foreach ($skus as $sku) {
            $skuSave[] = [
                HyProductsSkuTable::PRODUCT_ID => $table->id,
                HyProductsSkuTable::NAME => $sku->getName(),
            ];
        }

        if ($skuSave) {
            new HyProductsSkuTable()->insertAll($skuSave);
        }

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(HyProductsProto $request): HyProductsListsProto
    {
        $companyId = $request->getCompanyId();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $name = $request->getName();

        $where = [

        ];
        if ($companyId) {
            $where[] = [HyProductsTable::COMPANY_ID, '=', $companyId];
        }
        if ($name) {
            $where[] = [HyProductsTable::NAME, 'like', "%$name%"];
        }
        $order = [HyProductsTable::ID => "desc"];
        $hyProductsTable = new HyProductsTable();
        $lists = $hyProductsTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = HyProductsModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new HyProductsListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(HyProductsProto $request): HyProductsProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new HyProductsTable()->where([
            HyProductsTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return HyProductsModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(HyProductsProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new HyProductsTable()->where([
            HyProductsTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}