<?php

namespace App\Controller\Api\HuoYuan;


use App\Model\CustomProductsModel;
use App\Model\DrugDisplaysModel;
use App\Model\HyProductsModel;
use Generate\Models\Datas\AuthLevelsModel;
use Generate\Models\Datas\AuthUserInfoModel;
use Generate\Tables\Datas\AuthLevelsTable;
use Generate\Tables\Datas\AuthUserInfoTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\HyCompaniesModel;
use Generate\Tables\Datas\HyCompaniesTable;
use Protobuf\Datas\HyCompanies\HyCompaniesProto;
use Protobuf\Datas\HyCompanies\HyCompaniesListsProto;
use Throwable;


#[Router(method: 'POST')]
class HyCompanies extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(HyCompaniesProto $request): Success
    {
        $table = HyCompaniesModel::request($request);
        if (empty($table->userId)) {
            throw new AppException('请输入userId');
        }
        if (empty($table->type)) {
            throw new AppException('请选择入驻类型');
        }
        if (empty($table->name)) {
            throw new AppException('请输入公司名称');
        }
//        if (empty($table->phone)) {
//            throw new AppException('请输入联系电话');
//        }
//        if (empty($table->address)) {
//            throw new AppException('请输入公司地址');
//        }
//        if (empty($table->introduction)) {
//            throw new AppException('请输入公司详细介绍');
//        }

//        if (empty($table->logo)){
//            throw new AppException('请输入公司Logo图片路径');
//        }
//        if (empty($table->mainProducts)){
//            throw new AppException('请输入主营产品简介');
//        }
//        if (empty($table->bannerImage)){
//            throw new AppException('请输入公司背景图片');
//        }
//        if (empty($table->status)){
//            throw new AppException('请输入入驻状态');
//        }


        if ($table->medicinePermit) {
            $table->isDrug = true;
        }
        $table->status = HyCompaniesModel::StatusPending;

        $table->time = time();
        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取供应商/公司信息表列表数据失败')]
    public function lists(HyCompaniesProto $request): HyCompaniesListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $keyword = $request->getExtStr();
        $isDrug = $request->getIsDrug(); // 是否药品厂家
        $isCustom = $request->getIsCustom(); // 是否定制厂家
        $type = $request->getType();
        // 是否查询特邀商家
        $isSpecialInvitation = $request->getIsSpecialInvitation();
        if (empty($type)) {
            $type = 1;
        }

        $order = [HyCompaniesTable::PRI_KEY => "desc"];
        $query = new HyCompaniesTable()->order($order)->page($page, $size);

        if ($isDrug) {
            $query->addWhere(HyCompaniesTable::IS_DRUG, 1);
        } elseif ($isCustom) {
            $query->addWhere(HyCompaniesTable::IS_CUSTOM, 1);
        }

        if ($isSpecialInvitation) {
            $query->addWhere(HyCompaniesTable::IS_SPECIAL_INVITATION, 1);
        }

        if ($keyword) {
            if ($isDrug) {
                // 查询药品列表
                $keywordHyCompaniesIds = DrugDisplaysModel::getListsByKeyword($keyword);
            } elseif ($isCustom) {
                // 查询定制产品列表
                $keywordHyCompaniesIds = CustomProductsModel::getListsByKeyword($keyword);
            } else {
                // 查询产品列表
                $keywordHyCompaniesIds = HyProductsModel::getListsByKeyword($keyword);
            }


            if ($keywordHyCompaniesIds) {
                $hyCompaniesIdString = implode(',', $keywordHyCompaniesIds);
                $query->addWhereRaw(" and (id in ($hyCompaniesIdString) or name  like ?)", ["%$keyword%"]);
            } else {
                $query->addWhereRaw(" and name like ?", ["%$keyword%"]);
            }
        }

        $query->addWhere(HyCompaniesTable::STATUS, HyCompaniesModel::StatusApproved);
        $query->addWhere(HyCompaniesTable::TYPE, $type);
        $lists = $query->selectAll();


        $ids = [];
        $userIds = [];
        foreach ($lists as $table) {
            $ids[] = $table->id;
            $userIds[] = $table->userId;
        }


        // 是查询药品企业
        if ($isDrug) {
            $drugs = DrugDisplaysModel::getDrugLists($ids);
        }

        // 是查询定制企业
        if ($isCustom) {
            $customs = CustomProductsModel::getCustomProductsLists($ids);
        }

        // 查询认证信息
        $auths = [];
        if ($userIds) {
            $auths = new AuthUserInfoTable()->where([
                [AuthUserInfoTable::USER_ID, 'in', $userIds],
            ])->formatId2Array(AuthUserInfoTable::USER_ID);
        }

        $levels = [];
        if ($auths) {
            $levelIds = [];
            foreach ($auths as $auth) {
                $levelIds[] = $auth->currentLevelId;
            }
            $levels = new AuthLevelsTable()->where([
                [AuthLevelsTable::ID, 'in', $levelIds],
            ])->formatId2Array(AuthLevelsTable::ID);
        }


        $protoLists = [];
        foreach ($lists as $table) {
            $table->isDrug = $table->isDrug ? 1 : 0;
            $proto = HyCompaniesModel::formatItem($table);
            // 其他自定义字段格式化


            // 是查询药品企业
            if (isset($drugs[$table->id]) && $isDrug) {
                $proto->setDrugs($drugs[$table->id]);
            }

            // 是查询定制企业
            if (isset($customs[$table->id]) && $isCustom) {
                $proto->setCustomProducts($customs[$table->id]);
            }

            if (isset($auths[$table->userId])) {
                $authProto = AuthUserInfoModel::formatItem($auths[$table->userId]);
                $levelProto = AuthLevelsModel::formatItem($levels[$authProto->getCurrentLevelId()]);
                $authProto->setAuthLevel($levelProto);
                $proto->setAuth($authProto);
            }


            $protoLists[] = $proto;
        }

        $ret = new HyCompaniesListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(HyCompaniesProto $request): HyCompaniesProto
    {
        $userId = $request->getUserId();
        $id = $request->getId();
        $type = $request->getType();


        $where = [];
        if ($userId) {
            $where[] = [HyCompaniesTable::USER_ID, '=', $userId];
        }
        if ($id) {
            $where[] = [HyCompaniesTable::ID, '=', $id];
        }
        if ($type) {
            $where[] = [HyCompaniesTable::TYPE, '=', $type];
        }

        if (empty($where)) {
            throw new AppException("缺少参数");
        }

        $table = new HyCompaniesTable()->where($where)->selectOne();
        if (empty($table)) {
            return new HyCompaniesProto();
        }

        return HyCompaniesModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(HyCompaniesProto $request): Success
    {
        $userId = $request->getUserId();
        $id = $request->getId();
        if (empty($userId) || empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new HyCompaniesTable()->where([
            HyCompaniesTable::USER_ID => $userId,
            HyCompaniesTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存提现信息失败')]
    public function updateWithdrawalInfo(HyCompaniesProto $request): Success
    {
        $userId = $request->getUserId();
        $id = $request->getId();
        if (empty($userId) || empty($id)) {
            throw new AppException("参数错误");
        }

        $update = [];
        if ($bank_name = $request->getBankName()) {
            $update[HyCompaniesTable::BANK_NAME] = $bank_name;
        }
        if ($bank_account = $request->getBankAccount()) {
            $update[HyCompaniesTable::BANK_ACCOUNT] = $bank_account;
        }
        if ($bankOpening = $request->getBankOpening()) {
            $update[HyCompaniesTable::BANK_OPENING] = $bankOpening;
        }
        if ($alipay_account = $request->getAlipayAccount()) {
            $update[HyCompaniesTable::ALIPAY_ACCOUNT] = $alipay_account;
        }
        if ($wechat_account = $request->getWechatAccount()) {
            $update[HyCompaniesTable::WECHAT_ACCOUNT] = $wechat_account;
        }

        $res = false;
        if ($update) {
            $res = new HyCompaniesTable()->where([
                HyCompaniesTable::USER_ID => $userId,
                HyCompaniesTable::ID => $id,
            ])->update($update);
        }


        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


}