<?php

namespace App\Controller\Api\Order;

use App\Model\OrderModel;
use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\InvoiceTable;
use Generate\Tables\Datas\InvoiceTitleTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\ShopOrderTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Swlib\Table\JoinEnum;
use Swlib\Table\Db;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Protobuf\Invoice\InvoiceItem;
use Protobuf\Invoice\InvoiceLists;
use Protobuf\Invoice\InvoiceRequest;
use Protobuf\Invoice\InvoiceType;
use Protobuf\Invoice\InvoiceTypes;
use Protobuf\Order\OrderLists;
use Protobuf\Order\OrderRequest;
use Throwable;

/**
 * 发票相关
 */
#[Router(method: 'POST')]
class InvoiceApi extends AbstractController
{

    const  array Types = [
        1 => '增值税普通发票',
        2 => '增值税专用发票',
    ];

    /**
     * 获取发票类型
     * @return InvoiceTypes
     */
    #[Router(cache: 86400 * 7, errorTitle: '获取发票类型失败')]
    public function types(): InvoiceTypes
    {
        $types = [];
        foreach (self::Types as $key => $value) {
            $item = new InvoiceType();
            $item->setId($key);
            $item->setName($value);
            $types[] = $item;
        }

        $lists = new InvoiceTypes();
        $lists->setLists($types);
        $lists->setTotal(count($types));
        return $lists;
    }


    /**
     * 获取上次开票的发票抬头,前台开票的时候可以填充
     * @throws Throwable
     */
    #[Router(errorTitle: '获取发票抬头失败')]
    public function getTitle(Request $request): InvoiceItem
    {
        $userId = $request->getUserId();
        $detail = new InvoiceTitleTable()
            ->where([
                [InvoiceTitleTable::USER_ID, '=', $userId]
            ])
            ->order([
                InvoiceTitleTable::ID => 'desc'
            ])
            ->selectOne();

        $message = new InvoiceItem();
        $message->setEmail($detail?->email ?: '');
        $message->setHeader($detail?->header ?: '');
        $message->setNum($detail?->num ?: '');
        return $message;
    }


    /**
     * 获取已开发票列表
     * @throws Throwable
     */
    #[Router(errorTitle: '获取已开发票列表失败')]
    public function lists(Request $request): InvoiceLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }

        $lists = new InvoiceTable()
            ->field([
                InvoiceTitleTable::FIELD_ALL,
                InvoiceTable::FIELD_ALL
            ])
            ->where([
                [InvoiceTable::USER_ID, '=', $userId]
            ])
            ->page($page, $size)
            ->join(InvoiceTitleTable::TABLE_NAME, InvoiceTitleTable::ID, InvoiceTable::INVOICE_TITLE_ID)
            ->selectAll();


        $messageNodes = [];
        foreach ($lists as $detail) {
            $message = $this->getMessage($detail);

            $messageNodes[] = $message;
        }

        $message = new InvoiceLists();
        $message->setLists($messageNodes);

        return $message;
    }


    /**
     * 获取发票详情
     * @throws Throwable
     */
    #[Router(errorTitle: '获取发票详情失败')]
    public function detail(InvoiceItem $request): InvoiceItem
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $detail = new InvoiceTable()
            ->field([
                InvoiceTitleTable::FIELD_ALL,
                InvoiceTable::FIELD_ALL
            ])
            ->where([
                [InvoiceTable::ID, '=', $id]
            ])
            ->join(InvoiceTitleTable::TABLE_NAME, InvoiceTitleTable::ID, InvoiceTable::INVOICE_TITLE_ID)
            ->selectOne();

        if (empty($detail)) {
            throw new AppException("数据不存在");
        }

        return $this->getMessage($detail);
    }


    /**
     * 申请发票
     * @throws Throwable
     */
    #[Router(errorTitle: '申请发票失败')]
    public function applyFor(InvoiceRequest $request): Success
    {
        $userId = $request->getUserId();
        $email = $request->getEmail();
        $type = $request->getTypeId();
        $header = $request->getHeader();
        $num = $request->getNum();
        $price = $request->getPrice();
        $businessId = $request->getBusinessId();
        $notes = $request->getNotes();

        if (empty($userId)) {
            throw new AppException("请登录");
        }
        if (empty($email)) {
            throw new AppException("邮箱不能为空");
        }
        if (empty($type)) {
            throw new AppException("请选择发票类型");
        }
        if (empty($header)) {
            throw new AppException("请输入发票抬头");
        }
        if (empty($num)) {
            throw new AppException("请输入税号");
        }


        $countPrice = new ShopOrderTable()
            ->addWhere(ShopOrderTable::USER_ID, $userId)
            ->addWhere(ShopOrderTable::BUSINESS_ID, $businessId)
            ->sum(ShopOrderTable::PRICE);

        if ($countPrice != $price) {
            throw new AppException("开票金额有误");
        }


        // 记录发票抬头
        $titleId = new InvoiceTitleTable()
            ->addWhere(InvoiceTitleTable::USER_ID, $userId)
            ->addWhere(InvoiceTitleTable::NUM, $num)
            ->selectField(InvoiceTitleTable::ID);
        if (empty($titleId)) {
            $titleId = new InvoiceTitleTable()->insert([
                InvoiceTitleTable::USER_ID => $userId,
                InvoiceTitleTable::EMAIL => $email,
                InvoiceTitleTable::HEADER => $header,
                InvoiceTitleTable::NUM => $num,
            ]);
        }


        Db::transaction(function () use ($userId, $titleId, $type, $notes, $price, $businessId) {
            // 记录本次开票信息
            $invoiceId = new InvoiceTable()->insert([
                InvoiceTable::USER_ID => $userId,
                InvoiceTable::INVOICE_TITLE_ID => $titleId,
                InvoiceTable::TYPE => $type,
                InvoiceTable::NOTES => $notes,
                InvoiceTable::PRICE => $price,
                InvoiceTable::TIME => time()
            ]);

            $updateRet = new ShopOrderTable()
                ->addWhere(ShopOrderTable::USER_ID, $userId)
                ->addWhere(ShopOrderTable::BUSINESS_ID, $businessId)
                ->update([
                    ShopOrderTable::INVOICE_ID => $invoiceId
                ]);
            if (empty($updateRet)) {
                throw new AppException("数据更新失败");
            }
        });


        $success = new Success();
        $success->setSuccess(true);
        return $success;

    }


    /**
     * 获取代开发票的订单
     * @throws Throwable
     */
    #[Router(errorTitle: '获取代开发票的订单')]
    public function orders(OrderRequest $request): OrderLists
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }


        $shopOrderTable = new ShopOrderTable();
        $shopOrders = $shopOrderTable
            ->field([
                ShopOrderTable::FIELD_ALL,
                BusinessTable::BUSINESS_NAME,
                ShopProductsTable::NAME,
                ShopProductsSkuTable::NAME,
                ShopProductsSkuTable::PICTURE
            ])
            ->where([
                [ShopOrderTable::USER_ID, '=', $userId],
                [ShopOrderTable::INVOICE_ID, '=', 0],
            ])
            ->join(BusinessTable::TABLE_NAME, BusinessTable::ID, ShopOrderTable::BUSINESS_ID, JoinEnum::LEFT)
            ->join(ShopProductsTable::TABLE_NAME, ShopProductsTable::ID, ShopOrderTable::PRODUCT_ID, JoinEnum::LEFT)
            ->join(ShopProductsSkuTable::TABLE_NAME, ShopProductsSkuTable::ID, ShopOrderTable::SKU_ID, JoinEnum::LEFT)
            ->selectAll();
        $sns = $shopOrderTable->getArrayByField(ShopOrderTable::SN);


        // 查询出分页的订单sn
        $orders = new OrderTable()
            ->field(OrderTable::FIELD_ALL)
            ->where([
                [OrderTable::SN, 'in', $sns]
            ])
            ->selectAll();


        $message = new OrderLists();
        $lists = OrderModel::listsByBusiness($orders, $shopOrders, []);
        $message->setBusinessLists($lists);

        return $message;
    }

    /**
     * @param InvoiceTable $detail
     * @return InvoiceItem
     * @throws Throwable
     */
    private function getMessage(InvoiceTable $detail): InvoiceItem
    {
        $type = new InvoiceType();
        $type->setId($detail->type);
        $type->setName(self::Types[$detail->type]);

        $message = new InvoiceItem();
        $message->setId($detail->id);
        $message->setEmail($detail->getByField(InvoiceTitleTable::EMAIL));
        $message->setType($type);
        $message->setHeader($detail->getByField(InvoiceTitleTable::HEADER));
        $message->setNum($detail->getByField(InvoiceTitleTable::NUM));
        $message->setTime(date('Y-m-d H:i:s', $detail->time));
        $message->setNotes($detail->notes);
        $message->setPrice($detail->price);
        $message->setPic($detail->pic);
        return $message;
    }

}