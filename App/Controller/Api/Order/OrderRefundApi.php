<?php

namespace App\Controller\Api\Order;


use App\Model\PayModel;
use App\Service\OrderLogisticsService;
use Generate\Models\Datas\OrderModel;
use Generate\Tables\Datas\ShopOrderTable;
use Protobuf\Datas\HyCompaniesService\HyCompaniesServiceProto;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Queue\MessageQueue;
use Swlib\Router\Router;
use Generate\Models\Datas\OrderRefundModel;
use Generate\Tables\Datas\OrderRefundTable;
use Protobuf\Datas\OrderRefund\OrderRefundProto;
use Protobuf\Datas\OrderRefund\OrderRefundListsProto;
use Swlib\Table\Db;
use Throwable;
use Generate\Models\Datas\HyCompaniesModel;
use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Models\Datas\HyProductsSkuModel;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Generate\Tables\Datas\OrderTable;
use Yansongda\Artful\Exception\ContainerException;
use Yansongda\Artful\Exception\InvalidParamsException;
use Yansongda\Artful\Exception\ServiceNotFoundException;
use Yansongda\Pay\Pay;
use App\Service\HyCompaniesService;


/*
* 订单退款信息
*/

#[Router(method: 'POST')]
class OrderRefundApi extends AbstractController
{


    /**
     * 申请退款
     * @throws Throwable
     */
    #[Router(errorTitle: '申请退款失败')]
    public function applyRefund(OrderRefundProto $request): Success
    {
        $sn = $request->getSn();
        $skuId = $request->getSkuId();
        $refundNum = $request->getRefundNum();
        $refundOnly = $request->getRefundOnly();
        $refundMsg = $request->getRefundMsg();
        $userId = $request->getUserId();

        // 参数验证
        if (empty($sn)) {
            throw new AppException('请输入订单号');
        }
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        // 判断是整个订单退款还是单个SKU退款
        $isFullOrderRefund = empty($skuId);

        if (!$isFullOrderRefund && (empty($refundNum) || $refundNum <= 0)) {
            throw new AppException('请输入正确的退款数量');
        }

        // 前置检查：查询所有已申请退款的总数量
        if (!$isFullOrderRefund) {
            $totalRefundedNum = new OrderRefundTable()->where([
                OrderRefundTable::SN => $sn,
                OrderRefundTable::SKU_ID => $skuId,
                OrderRefundTable::USER_ID => $userId,
            ])->sum(OrderRefundTable::REFUND_NUM);

            if ($totalRefundedNum > 0) {
                // 将已退款数量保存到请求对象中，供后续逻辑使用
                $request->setExtInt($totalRefundedNum);
            }
        }

        // 查询订单信息
        $orderTable = new OrderTable()
            ->where([
                OrderTable::SN => $sn,
                OrderTable::USER_ID => $userId,
            ])
            ->selectOne();

        if (empty($orderTable)) {
            throw new AppException("未找到订单信息");
        }

        // 获取订单中的所有商品
        $shopOrderItems = new ShopOrderTable()
            ->where([
                ShopOrderTable::SN => $sn,
                ShopOrderTable::USER_ID => $userId,
            ])
            ->selectAll();

        if (empty($shopOrderItems)) {
            throw new AppException("未找到订单商品信息");
        }

        if ($isFullOrderRefund) {
            // 整个订单退款
            $result = $this->handleFullOrderRefund($orderTable, $shopOrderItems, $refundOnly, $refundMsg);
        } else {
            // 单个SKU退款
            // 查询发货信息
            $shopOrderTable = null;
            foreach ($shopOrderItems as $item) {
                if ($item->skuId == $skuId) {
                    $shopOrderTable = $item;
                    break;
                }
            }

            if (empty($shopOrderTable)) {
                throw new AppException("未找到指定的商品规格信息");
            }

            // 验证退款数量是否大于发货数量
            if ($refundNum > $shopOrderTable->num) {
                throw new AppException("申请退款数量不能大于下单数量$shopOrderTable->num");
            }

            // sku 已经申请退款 了多少个数量了；
            $dbRefundNum = new OrderRefundTable()->where([
                OrderRefundTable::SN => $shopOrderTable->sn,
                OrderRefundTable::SKU_ID => $shopOrderTable->skuId,
                OrderRefundTable::USER_ID => $shopOrderTable->userId,
            ])->sum(OrderRefundTable::REFUND_NUM);

            // 检查已申请退款数量加上本次申请数量是否超过下单数量
            if ($refundNum + $dbRefundNum > $shopOrderTable->num) {
                throw new AppException("申请退款数量不能大于下单数量{$shopOrderTable->num}，已申请退款数量为$dbRefundNum");
            }

            // 判断是否为订单中的唯一SKU，或者是否退款数量等于总数量
            $isOnlySkuInOrder = count($shopOrderItems) === 1;
            $isFullSkuRefund = ($refundNum + $dbRefundNum) == $shopOrderTable->num;

            // 处理单个SKU退款
            $result = $this->handleSingleSkuRefund(
                $orderTable,
                $shopOrderTable,
                $refundNum,
                $dbRefundNum,
                $refundOnly,
                $refundMsg,
                $isOnlySkuInOrder && $isFullSkuRefund
            );
        }

        $msg = new Success();
        $msg->setSuccess($result);
        return $msg;
    }

    /**
     * 处理整个订单退款
     * @param OrderTable $orderTable 订单信息
     * @param array $shopOrderItems 订单中的所有商品
     * @param bool $refundOnly 是否仅退款不退货
     * @param string $refundMsg 退款原因
     * @return bool 处理结果
     * @throws Throwable
     */
    private function handleFullOrderRefund(OrderTable $orderTable, array $shopOrderItems, bool $refundOnly, string $refundMsg): bool
    {
        // 检查每个SKU是否已经有退款申请，如果总退款数量已经达到或超过下单数量则不允许再次申请
        /** @var ShopOrderTable $shopOrderItem */
        foreach ($shopOrderItems as $shopOrderItem) {
            $existingRefundNum = new OrderRefundTable()->where([
                [OrderRefundTable::SN, '=', $shopOrderItem->sn],
                [OrderRefundTable::SKU_ID, '=', $shopOrderItem->skuId],
                [OrderRefundTable::USER_ID, '=', $shopOrderItem->userId],
                [OrderRefundTable::HANDLE_REFUND_STATUS, 'IN', [0, 1]]
            ])->sum(OrderRefundTable::REFUND_NUM);
            if ($existingRefundNum >= $shopOrderItem->num) {
                throw new AppException("$shopOrderItem->skuName 已申请退款数量为 $existingRefundNum 不能再次申请退款");
            }
        }
        return Db::transaction(function () use ($orderTable, $shopOrderItems, $refundOnly, $refundMsg) {
            // 更新订单状态为退款中
            new OrderTable()->where([
                OrderTable::ID => $orderTable->id
            ])->update([
                OrderTable::STATUS => OrderModel::StatusRefund,
                OrderTable::REFUND_TIME => time(),
                OrderTable::REFUND_MSG => $refundMsg ?: '',
            ]);

            // 为每个SKU创建退款记录
            $insertAll = [];
            /** @var ShopOrderTable $shopOrderItem */
            foreach ($shopOrderItems as $shopOrderItem) {

                $refNumber = $shopOrderItem->num - $shopOrderItem->quantityShipped;

                $insertAll[] = [
                    OrderRefundTable::BUSINESS_ID => $shopOrderItem->businessId,
                    OrderRefundTable::SN => $shopOrderItem->sn,
                    OrderRefundTable::ORDER_ID => $shopOrderItem->orderId,
                    OrderRefundTable::PRODUCT_ID => $shopOrderItem->productId,
                    OrderRefundTable::SKU_ID => $shopOrderItem->skuId,
                    OrderRefundTable::USER_ID => $shopOrderItem->userId,
                    OrderRefundTable::SHOR_ORDER_ID => $shopOrderItem->id,
                    OrderRefundTable::REFUND_NUM => $refNumber,
                    OrderRefundTable::REFUND_ONLY => $refundOnly ? 1 : 0,
                    OrderRefundTable::REFUND_MSG => $refundMsg ?: '',
                    OrderRefundTable::REFUND_PRICE => $shopOrderItem->price,
                ];

                // 更新ShopOrderTable中的退货数量
                new ShopOrderTable()->where([
                    ShopOrderTable::ID => $shopOrderItem->id
                ])->update([
                    // 退货数量是 下单数量减去已经发货的数量
                    ShopOrderTable::RETURNED_QUANTITY => $refNumber
                ]);
            }

            new OrderRefundTable()->insertAll($insertAll);

            return true;
        });
    }

    /**
     * 处理单个SKU退款
     * @param OrderTable $orderTable 订单信息
     * @param ShopOrderTable $shopOrderTable 要退款的商品信息
     * @param int $refundNum 退款数量
     * @param int $dbRefundNum 已申请退款数量
     * @param bool $refundOnly 是否仅退款不退货
     * @param string $refundMsg 退款原因
     * @param bool $isFullRefund 是否为完全退款（订单中唯一SKU且全部退款）
     * @return bool 处理结果
     * @throws Throwable
     */
    private function handleSingleSkuRefund(
        OrderTable     $orderTable,
        ShopOrderTable $shopOrderTable,
        int            $refundNum,
        int            $dbRefundNum,
        bool           $refundOnly,
        string         $refundMsg,
        bool           $isFullRefund
    ): bool
    {
        return Db::transaction(function () use ($orderTable, $shopOrderTable, $refundNum, $dbRefundNum, $refundOnly, $refundMsg, $isFullRefund) {
            // 创建退款记录
            new OrderRefundTable()->insert([
                OrderRefundTable::BUSINESS_ID => $shopOrderTable->businessId,
                OrderRefundTable::SN => $shopOrderTable->sn,
                OrderRefundTable::ORDER_ID => $shopOrderTable->orderId,
                OrderRefundTable::PRODUCT_ID => $shopOrderTable->productId,
                OrderRefundTable::SKU_ID => $shopOrderTable->skuId,
                OrderRefundTable::USER_ID => $shopOrderTable->userId,
                OrderRefundTable::SHOR_ORDER_ID => $shopOrderTable->id,
                OrderRefundTable::REFUND_NUM => $refundNum,
                OrderRefundTable::REFUND_ONLY => $refundOnly ? 1 : 0,
                OrderRefundTable::REFUND_MSG => $refundMsg ?: '',
                OrderRefundTable::REFUND_PRICE => $shopOrderTable->price,
            ]);

            // 更新ShopOrderTable中的退货数量
            new ShopOrderTable()->where([
                ShopOrderTable::SN => $shopOrderTable->sn,
                ShopOrderTable::SKU_ID => $shopOrderTable->skuId,
            ])->update([
                ShopOrderTable::RETURNED_QUANTITY => Db::incr(ShopOrderTable::RETURNED_QUANTITY, $refundNum)
            ]);

            // 如果是完全退款，更新订单状态
            if ($isFullRefund) {
                new OrderTable()->where([
                    OrderTable::ID => $orderTable->id
                ])->update([
                    OrderTable::STATUS => OrderModel::StatusRefund,
                    OrderTable::REFUND_TIME => time(),
                    OrderTable::REFUND_MSG => $refundMsg ?: '',
                ]);
            } else {
                // 部分退款，更新订单状态为部分退款
                // 注意：这里假设有部分退款状态，如果没有，可能需要创建新的状态
                new OrderTable()->where([
                    OrderTable::ID => $orderTable->id
                ])->update([
                    OrderTable::STATUS => OrderModel::StatusPartial_refund,
                    OrderTable::REFUND_TIME => time(),
                    OrderTable::REFUND_MSG => $refundMsg ?: '',
                ]);
            }

            return true;
        });
    }


    /**
     * 申请退款
     * @throws Throwable
     */
    #[Router(errorTitle: '统计用户退货数量')]
    public function countRefundNum(OrderRefundProto $request): OrderRefundProto
    {
        $sn = $request->getSn();
        $skuId = $request->getSkuId();

        // 参数验证
        if (empty($sn)) {
            throw new AppException('请输入订单号');
        }

        if (empty($skuId)) {
            throw new AppException('请输入规格ID');
        }


        // sku 已经申请退款 了多少个数量了；
        $dbRefundNum = new OrderRefundTable()->where([
            [OrderRefundTable::SN, '=', $sn],
            [OrderRefundTable::SKU_ID, '=', $skuId],
            [OrderRefundTable::HANDLE_REFUND_STATUS, '<>', 2],
        ])->sum(OrderRefundTable::REFUND_NUM);

        $proto = new OrderRefundProto();
        $proto->setRefundNum($dbRefundNum);

        return $proto;
    }


    /**
     * 商家查询退货列表
     * @throws Throwable
     */
    #[Router(errorTitle: '获取商家退货列表失败')]
    public function businessLists(OrderRefundProto $request): OrderRefundListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $status = $request->getHandleRefundStatus() ?: 0;

        $businessId = $request->getBusinessId();
        if (empty($businessId)) {
            throw new AppException('请登录商家');
        }

        $where = [
            [OrderRefundTable::BUSINESS_ID, '=', $businessId],
        ];

        if ($status === 0) {
            $where[] = [OrderRefundTable::HANDLE_REFUND_STATUS, '=', 0];
        } elseif ($status === 1) {
            $where[] = [OrderRefundTable::HANDLE_REFUND_STATUS, 'in', [1, 2]];
        }

        $protoLists = $this->_getRefunds($where, $page, $size);

        $ret = new OrderRefundListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * 用户查询退货列表
     * @throws Throwable
     */
    #[Router(errorTitle: '获取用户退货列表失败')]
    public function userLists(OrderRefundProto $request): OrderRefundListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $userId = $request->getUserId();
        $status = $request->getHandleRefundStatus(); // 前台传递 -1 查询全部
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $where[] = [OrderRefundTable::USER_ID, '=', $userId];
        if ($status === 0) {
            // 待处理
            $where[] = [OrderRefundTable::HANDLE_REFUND_STATUS, '=', $status];
        } elseif ($status === 1) {
            // 已经处理
            $where[] = [OrderRefundTable::HANDLE_REFUND_STATUS, '<>', 0];
        }

        $protoLists = $this->_getRefunds($where, $page, $size, true);

        $ret = new OrderRefundListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * 商家同意退款
     * @throws Throwable
     */
    #[Router(errorTitle: '商家同意退款失败')]
    public function approveRefund(OrderRefundProto $request): Success
    {
        $refundId = $request->getId();
        $businessId = $request->getBusinessId();
        $approveMsg = $request->getRefundMsg();

        // 处理退款状态更新
        $result = $this->handleRefundStatusUpdate(
            $refundId,
            $businessId,
            1,
            $approveMsg ?: '商家已同意退款',
            false
        );

        $msg = new Success();
        $msg->setSuccess($result);
        return $msg;
    }

    /**
     * 商家驳回退款
     * @throws Throwable
     */
    #[Router(errorTitle: '商家驳回退款失败')]
    public function rejectRefund(OrderRefundProto $request): Success
    {
        $refundId = $request->getId();
        $businessId = $request->getBusinessId();
        $rejectMsg = $request->getRefundMsg();

        // 处理退款状态更新
        $result = $this->handleRefundStatusUpdate(
            $refundId,
            $businessId,
            2,
            $rejectMsg,
            true
        );

        $msg = new Success();
        $msg->setSuccess($result);
        return $msg;
    }

    /**
     * 处理退款状态更新的通用方法
     * @param int $refundId 退款ID
     * @param int $businessId 商家ID
     * @param int $status 要更新的状态（1=同意，2=驳回）
     * @param string $message 状态更新消息
     * @param bool $requireMessage 是否必须提供消息
     * @return bool 更新结果
     * @throws Throwable
     */
    private function handleRefundStatusUpdate(
        int    $refundId,
        int    $businessId,
        int    $status,
        string $message,
        bool   $requireMessage
    ): bool
    {
        // 参数验证
        if (empty($refundId)) {
            throw new AppException('请输入退款ID');
        }

        if (empty($businessId)) {
            throw new AppException('请登录商家账号');
        }

        if ($requireMessage && empty($message)) {
            throw new AppException('请输入驳回理由');
        }


        // 查询退款信息
        $refundTable = new OrderRefundTable()
            ->where([
                OrderRefundTable::PRI_KEY => $refundId,
                OrderRefundTable::BUSINESS_ID => $businessId,
            ])
            ->selectOne();

        if (empty($refundTable) || $refundTable->businessId != $businessId) {
            throw new AppException("未找到退款信息或您无权操作此退款");
        }

        // 检查退款状态
        if ($refundTable->handleRefundStatus != 0) {
            throw new AppException("该退款申请已处理，请勿重复操作");
        }

        $orderTotalPrice = new OrderTable()->where([
            OrderTable::SN => $refundTable->sn,
        ])->selectField(OrderTable::PRICE);

        $saveStatus = $status;
        if ($status === 1) {
            $params = array(
                'out_trade_no' => $refundTable->sn,
                'out_refund_no' => 'R' . $refundTable->sn,
                'amount' => array(
                    // 退款金额
                    'refund' => intval($refundTable->refundPrice * $refundTable->refundNum * 100),
                    // 订单总金额
                    'total' => intval($orderTotalPrice * 100),
                    'currency' => 'CNY',
                ),
                '_action' => 'mini', // 小程序退款
            );


            Pay::config(PayModel::getConfig());
            // 注意返回类型为 Response，具体见详细文档
            $response = Pay::wechat()->refund($params);
            $response = $response->toArray();


            if ($response['status'] == 'SUCCESS') {
                // 更新退款记录状态
                $saveStatus = 1;
            } elseif ($response['status'] == 'PROCESSING') {
                $saveStatus = 3;
                MessageQueue::push([__CLASS__, 'queryWxRefund'], [
                    'out_refund_no' => 'R' . $refundTable->sn,
                    'refundId' => $refundId,
                    'message' => $message,
                ], 3);
            }
        } elseif ($status === 2) {
            // 更新ShopOrderTable中的退货数量
            new ShopOrderTable()->where([
                ShopOrderTable::ID => $refundTable->shorOrderId
            ])->update([
                ShopOrderTable::RETURNED_QUANTITY => Db::incr(ShopOrderTable::RETURNED_QUANTITY, $refundTable->refundNum, '-'),
            ]);
            $this->updateOrderStatusAfterRefund($refundTable->sn);
        }


        new OrderRefundTable()->where([
            OrderRefundTable::PRI_KEY => $refundId
        ])->update([
            OrderRefundTable::HANDLE_REFUND_STATUS => $saveStatus,
            OrderRefundTable::HANDLE_REFUND_MSG => $message,
            OrderRefundTable::HANDLE_REFUND_TIME => date("Y-m-d H:i:s"),
        ]);

        return true;
    }

    /**
     * @throws ServiceNotFoundException
     * @throws ContainerException
     * @throws InvalidParamsException|Throwable
     */
    public function queryWxRefund(array $data): void
    {
        $out_refund_no = $data['out_refund_no'];
        $refundId = $data['refundId'];
        $message = $data['message'];
        Pay::config(PayModel::getConfig());
        $order = [
            'out_refund_no' => $out_refund_no,
//            '_action' => 'refund',
            // '_action' => 'refund_jsapi', // 查询 jsapi 退款订单，默认
            // '_action' => 'refund_app', // 查询 App 退款订单
            // '_action' => 'refund_h5', // 查询 H5 退款订单
            '_action' => 'refund_mini', // 查询小程序退款订单
            // '_action' => 'refund_native', // 查询 Native 退款订单
            // '_action' => 'refund_combine', // 查询合单退款订单
        ];

        $response = Pay::wechat()->query($order);
        $response = $response->toArray();

        if ($response['status'] == 'SUCCESS') {
            // 查询退款记录，检查是否已经处理过
            $refundRecord = new OrderRefundTable()->where([
                OrderRefundTable::PRI_KEY => $refundId,
            ])->selectOne();

            // 检查退款记录状态，如果已经退款成功则不再执行
            if ($refundRecord && $refundRecord->handleRefundStatus == 1) {
                // 已经处理过了，直接返回
                return;
            }

            // 在事务中完成两个操作：更新退款记录状态和减少商家冻结余额
            Db::transaction(function () use ($refundId, $message, $refundRecord) {
                // 更新退款记录状态
                $updateResult = new OrderRefundTable()->where([
                    OrderRefundTable::PRI_KEY => $refundId,
                    OrderRefundTable::HANDLE_REFUND_STATUS => $refundRecord->handleRefundStatus,
                ])->update([
                    OrderRefundTable::HANDLE_REFUND_STATUS => 1,
                    OrderRefundTable::HANDLE_REFUND_MSG => $message,
                    OrderRefundTable::HANDLE_REFUND_TIME => date("Y-m-d H:i:s"),
                ]);

                if (!$updateResult) {
                    throw new AppException('更新退款记录状态失败');
                }

                // 减少商家冻结余额
                // 计算需要解冻的金额 = 退款单价 × 退款数量
                $amount = $refundRecord->refundPrice * $refundRecord->refundNum;

                // 获取商家ID
                $businessId = $refundRecord->businessId;

                // 减少商家冻结余额
                $unlockResult = HyCompaniesService::unlockBalance(
                    $businessId,
                    $amount,
                    "订单{$refundRecord->sn}退款解冻金额"
                );

                if (!$unlockResult) {
                    throw new AppException('解冻商家冻结余额失败');
                }
            });
        }
    }


    /**
     * @throws Throwable
     */
    private function _getRefunds(array $where, int $page, int $size, bool $withBusinessInfo = false): array
    {
        $order = [OrderRefundTable::PRI_KEY => "desc"];

        $orderRefundTable = new OrderRefundTable();
        $lists = $orderRefundTable->order($order)->where($where)->page($page, $size)->selectAll();

        if (empty($lists)) {
            return [];
        }

        // 准备批量获取数据
        $productIds = $orderRefundTable->getArrayByField(OrderRefundTable::PRODUCT_ID);
        $skuIds = $orderRefundTable->getArrayByField(OrderRefundTable::SKU_ID);


        // 批量获取关联数据
        $products = [];
        if ($productIds) {
            $products = new HyCompaniesServiceTable()->where([
                [HyCompaniesServiceTable::ID, 'in', $productIds],
            ])->formatId2Array(HyCompaniesServiceTable::ID);
        }


        $skus = [];
        if ($skuIds) {
            $skus = new HyProductsSkuTable()->where([
                [HyProductsSkuTable::ID, 'in', $skuIds]
            ])->formatId2Array(HyProductsSkuTable::ID);
        }

        if ($withBusinessInfo) {
            $businessIds = $orderRefundTable->getArrayByField(OrderRefundTable::BUSINESS_ID);
            $businesses = [];
            if ($businessIds) {
                $businesses = new HyCompaniesTable()->where([
                    [HyCompaniesTable::ID, 'in', $businessIds]
                ])->formatId2Array(HyCompaniesTable::ID);
            }
        }

        $orderLogisticsService = new OrderLogisticsService();
        $protoLists = [];
        foreach ($lists as $table) {
            $proto = OrderRefundModel::formatItem($table);

            // 填充产品信息
            if (isset($products[$table->productId])) {

                $companiesService = $products[$table->productId];
                $orderLogisticsService->sanitizeServiceTable($companiesService);
                $productProto = HyCompaniesServiceModel::formatItem($companiesService);
                $proto->setCompaniesService($productProto); // Assuming setter is setService for HyCompaniesServiceProto
            }

            // 填充SKU信息
            if (isset($skus[$table->skuId])) {
                $sku = $skus[$table->skuId];
                $orderLogisticsService->sanitizeSkuTable($sku);
                $skuProto = HyProductsSkuModel::formatItem($sku);
                $proto->setSku($skuProto); // Assuming setSku
            }

            // 填充商家信息 (for user lists)
            if ($withBusinessInfo && isset($businesses[$table->businessId])) {
                $businessProto = HyCompaniesModel::formatItem($businesses[$table->businessId]);
                $proto->setCompanies($businessProto); // Assuming setCompanies for HyCompaniesProto
            }

            $protoLists[] = $proto;
        }

        return $protoLists;
    }


    /**
     * 退款成功后更新订单状态
     * @throws Throwable
     */
    private function updateOrderStatusAfterRefund(string $sn): void
    {
        $shopOrders = new ShopOrderTable()->where([
            ShopOrderTable::SN => $sn
        ])->selectAll();

        if (empty($shopOrders)) {
            // Or maybe log an error. If there are no shop orders, something is wrong.
            return;
        }

        $totalNum = 0;
        $quantityShipped = 0;
        $returnedQuantity = 0;

        foreach ($shopOrders as $item) {
            $totalNum += $item->num;
            $quantityShipped += $item->quantityShipped;
            $returnedQuantity += $item->returnedQuantity;
        }

        $orderTable = new OrderTable()->where([OrderTable::SN => $sn]);

        // 1. 如果订单商品总数 === 退货总数，说明整单已退，状态为"退款完成"
        if ($totalNum === $returnedQuantity) {
            $orderTable->update([
                OrderTable::STATUS => OrderModel::StatusRefund_finish,
                OrderTable::REFUND_HANDLE_TIME => time(),
            ]);
            return;
        }

        // 2. 如果已发货数 === 0，说明是未发货，状态为"待发货"
        if ($quantityShipped === 0) {
            $orderTable->update([
                OrderTable::STATUS => OrderModel::StatusWait_delivery
            ]);
            return;
        }

        // 3. 如果已发货数 > 0
        // 有效订单数量 = 商品总数 - 退货总数
        $effectiveNum = $totalNum - $returnedQuantity;

        // 如果已发货数 < 有效订单数，说明是"部分发货"
        if ($quantityShipped < $effectiveNum) {
            $orderTable->update([
                OrderTable::STATUS => OrderModel::StatusPartial_shipment
            ]);
        } else { // 如果已发货数 >= 有效订单数，说明是"待收货"
            $orderTable->update([
                OrderTable::STATUS => OrderModel::StatusWait_receive
            ]);
        }
    }


    /**
     * 订单统计
     * @throws Throwable
     */
    #[Router(errorTitle: '获取退款订单统计失败')]
    public function statistics(OrderRefundProto $request): OrderRefundProto
    {
        $userId = $request->getUserId();

        if (empty($userId)) {
            throw new AppException("请登录");
        }


        // 统计订单数量
        $count = new OrderRefundTable()
            ->where([
                [OrderRefundTable::USER_ID, '=', $userId],
                [OrderRefundTable::HANDLE_REFUND_STATUS, '=', 0],
            ])
            ->count();

        $response = new OrderRefundProto();
        $response->setExtInt($count); // 使用ExtInt字段返回统计数量
        $response->setUserId($userId);

        return $response;
    }


    /**
     * @throws Throwable
     * @throws AppException
     */
    #[Router(errorTitle: '获取商家售后信息识别')]
    public function countsCompanies(HyCompaniesServiceProto $request): OrderRefundProto
    {
        $userId = $request->getUserId();

        $companiesId = new HyCompaniesTable()->where([
            HyCompaniesTable::USER_ID => $userId,
            HyCompaniesTable::TYPE => 1,
        ])->selectField(HyCompaniesTable::ID);
        if (empty($companiesId)) {
            throw new AppException('参数错位');
        }

        // 售出数量
        $saleNum = new ShopOrderTable()->where([
            ShopOrderTable:: BUSINESS_ID => $companiesId,
        ])->sum(ShopOrderTable::NUM);

        // 仅退款数量
        $onlyRefundNum = new OrderRefundTable()->where([
            [OrderRefundTable::BUSINESS_ID, '=', $companiesId],
            [OrderRefundTable::REFUND_ONLY, '=', 1],
        ])->sum(OrderRefundTable::REFUND_NUM);


        // 退款退货数量
        $refundNum = new OrderRefundTable()->where([
            [OrderRefundTable::BUSINESS_ID, '=', $companiesId],
            [OrderRefundTable::REFUND_ONLY, '=', 0],
        ])->sum(OrderRefundTable::REFUND_NUM);

        // 退款金额
        $refundPrice = new OrderRefundTable()->where([
            [OrderRefundTable::BUSINESS_ID, '=', $companiesId],
        ])->setDebugSql()->sum(OrderRefundTable::REFUND_NUM, OrderRefundTable::REFUND_PRICE);


        // 退款笔数
        $refundCount = new OrderRefundTable()->where([
            [OrderRefundTable::BUSINESS_ID, '=', $companiesId],
        ])->count(OrderRefundTable::ID);


        $msg = new OrderRefundProto();
        $msg->setExtStr(json_encode([
            'saleNum' => $saleNum,
            'onlyRefundNum' => $onlyRefundNum,
            'refundNum' => $refundNum,
            'refundPrice' => $refundPrice,
            'refundCount' => $refundCount,
        ]));

        return $msg;

    }

}