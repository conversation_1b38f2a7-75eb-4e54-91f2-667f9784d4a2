<?php

namespace App\Controller\Api\Pc;


use <PERSON>wlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Generate\Tables\Datas\PcConfigTable;
use Generate\Models\Datas\PcConfigModel;
use Protobuf\Datas\PcConfig\PcConfigProto;
use Protobuf\Datas\PcConfig\PcConfigListsProto;
use Throwable;


#[Router(method: 'POST')]
class PcConfig extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(PcConfigProto $request): PcConfigListsProto
    {
        $ids = $request->getPosIds();

        $idArr = [];
        foreach ($ids as $id) {
            $idArr[] = $id;
        }

        if (empty($idArr)) {
            throw new AppException('参数错误');
        }


        $where = [
            [PcConfigTable::CONFIG_POS_ID, 'in', $idArr]
        ];
        $order = [PcConfigTable::ID => "desc"];
        $lists = new PcConfigTable()->order($order)->where($where)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = PcConfigModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new PcConfigListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

}