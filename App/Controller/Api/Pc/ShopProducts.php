<?php

namespace App\Controller\Api\Pc;


use Generate\Tables\Datas\BrandTable;
use Generate\Tables\Datas\ShopCategoriesTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Generate\Models\Datas\ShopProductsModel;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Table\JoinEnum;
use Protobuf\Datas\ShopProducts\ShopProductsProto;
use Throwable;


#[Router(method: 'POST')]
class ShopProducts extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(ShopProductsProto $request): ShopProductsProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new ShopProductsTable()
            ->join(ShopCategoriesTable::TABLE_NAME, ShopCategoriesTable::ID, ShopProductsTable::CATEGORY_ID, JoinEnum::LEFT)
            ->join(BrandTable::TABLE_NAME, BrandTable::ID, ShopProductsTable::BRAND_ID, JoinEnum::LEFT)
            ->field([
                ShopProductsTable::FIELD_ALL,
                ShopCategoriesTable::NAME,
                BrandTable::NAME,
            ])
            ->where([
                ShopProductsTable::ID => $id,
            ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }
        $table->deliveryTime = 0;
        $table->hotSort = 0;
        $proto = ShopProductsModel::formatItem($table);
        $proto->setBrandName($table->getByField(BrandTable::NAME, ''));
        $proto->setCategoryName($table->getByField(ShopCategoriesTable::NAME, ''));
        return $proto;
    }

}