<?php
namespace App\Controller\Api\Pc;


use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\PcFeedbackModel;
use Protobuf\Datas\PcFeedback\PcFeedbackProto;
use Throwable;



#[Router(method: 'POST')]
class PcFeedback extends AbstractController{

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '留言失败')]
    public function save(PcFeedbackProto $request): Success
    {
        $table = PcFeedbackModel::request($request);


        if(empty($table->contacts)){
            throw new AppException('请输入您的称呼');
        }
        if(empty($table->email) && empty($table->phone)){
            throw new AppException('请输入电话或者邮箱');
        }
        if(empty($table->content)){
            throw new AppException('请输入信息');
        }
        $table->time = time();

        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

}