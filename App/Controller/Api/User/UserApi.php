<?php

namespace App\Controller\Api\User;

use App\Model\UserModel;
use App\Service\SmsService;
use Exception;
use Generate\Tables\Datas\UserTable;
use Protobuf\Datas\User\UserProto;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Success;
use Throwable;

#[Router(method: "POST")]
class User<PERSON><PERSON> extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取用户信息失败')]
    public function info(UserProto $request): UserProto
    {
        $userId = $request->getId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }

        $table = (new UserTable)->where([
            [UserTable::ID, '=', $userId],
        ])->selectOne();

        if (empty($table)) {
            throw new AppException("用户不存在");
        }

        return UserModel::formatUser($table);
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '登录失败')]
    public function phoneLoginReg(UserProto $request): UserProto
    {
        $phone = $request->getPhone();
        if (empty($phone)) {
            throw new AppException("请输入手机号码");
        }

        $code = $request->getCode();
        if (empty($code)) {
            throw new AppException("请输入验证码");
        }

        SmsService::check($phone, $code);


        $id = (new UserTable)->where([
            [UserTable::PHONE, '=', $phone],
        ])->selectField(UserTable::ID);
        $isNewUser = false;
        if (empty($id)) {
            $id = (new UserTable)->insert([
                UserTable::PHONE => $phone,
                UserTable::REG_TIME => time(),
            ]);
            $isNewUser = true;
        }

        $message = new UserProto();
        $message->setId($id);
        $message->setIsNewUser($isNewUser);
        $message->setPhone($phone);

        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '验证失败')]
    public function findPwdPhoneCheck(UserProto $request): UserProto
    {
        $phone = $request->getPhone();
        if (empty($phone)) {
            throw new AppException("请输入手机号码");
        }

        $code = $request->getCode();
        if (empty($code)) {
            throw new AppException("请输入验证码");
        }

        SmsService::check($phone, $code);


        $user = (new UserTable)->where([
            [UserTable::PHONE, '=', $phone],
        ])->selectOne();

        $message = new UserProto();
        $message->setId($user->id);
        $message->setPhone($phone);

        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '注册失败')]
    public function pwdReg(UserProto $request): UserProto
    {
        $username = $request->getUsername();
        if (empty($username)) {
            throw new AppException("请输入账号");
        }
        $pwd = $this->checkPwdParam($request);

        $find = new UserTable()->where([
            UserTable::USERNAME => $username
        ])->selectOne();

        if ($find) {
            throw new AppException("账号已存在");
        }

        $id = (new UserTable)->insert([
            UserTable::USERNAME => $username,
            UserTable::PASSWORD => $pwd,
            UserTable::REG_TIME => time(),
        ]);

        $message = new UserProto();
        $message->setId($id);

        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '设置密码失败')]
    public function setPwd(UserProto $request): Success
    {
        $userId = $request->getId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }
        $pwd = $this->checkPwdParam($request);

        $id = (new UserTable)->where([
            [UserTable::ID, '=', $userId],
        ])->selectField(UserTable::ID);

        if (empty($id)) {
            throw new AppException("用户不存在");
        }

        (new UserTable)->where([
            [UserTable::ID, '=', $userId],
        ])->update([
            UserTable::PASSWORD => $pwd,
            UserTable::REG_TIME => time(),
        ]);

        $message = new Success();
        $message->setSuccess(true);

        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '登录失败')]
    public function pwdLogin(UserProto $request): UserProto
    {
        $username = $request->getUsername();
        if (empty($username)) {
            throw new AppException("请输入密码");
        }
        $pwd = $request->getPassword();
        if (empty($pwd)) {
            throw new AppException("请输入密码");
        }

        $table = (new UserTable)->where([
            [UserTable::USERNAME, '=', $username],
        ])->selectOne();

        if (empty($table)) {
            throw new AppException("用户名或者密码错误");
        }

        if (password_verify($pwd, $table->password) === false) {
            throw new AppException("用户名或者密码错误!");
        }

        return UserModel::formatUser($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '设置地址失败')]
    public function saveAddr(UserProto $request): Success
    {
        $userId = $request->getId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }
        $addr = $request->getAddr();
        if (empty($addr)) {
            throw new AppException("请输入地址");
        }

        UserModel::findByUserId($userId);

        (new UserTable)->where([
            [UserTable::ID, '=', $userId],
        ])->update([
            UserTable::ADDR => $addr,
        ]);

        $success = new Success();
        $success->setSuccess(true);
        return $success;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存个人资料失败')]
    public function saveInfo(UserProto $request): Success
    {

        $userId = $request->getId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }

        UserModel::findByUserId($userId);

        $save = [];
        if ($email = $request->getEmail()) {
            $save[UserTable::EMAIL] = $email;
        }
        if ($head = $request->getHead()) {
            $save[UserTable::HEAD] = $head;
        }
        if ($nickname = $request->getNickname()) {
            $save[UserTable::NICKNAME] = $nickname;
        }
        if ($info = $request->getInfo()) {
            $save[UserTable::INFO] = $info;
        }
        if ($birthday = $request->getBirthday()) {
            $save[UserTable::BIRTHDAY] = $birthday;
        }
        if ($gender = $request->getGender()) {
            $save[UserTable::GENDER] = $gender;
        }

        if ($phone = $request->getPhone()) {
            $code = $request->getCode();
            if (empty($code)) {
                throw new AppException("请输入验证码");
            }
            SmsService::check($phone, $code);
            $save[UserTable::PHONE] = $phone;
        }


        if ($save) {
            (new UserTable)->where([
                [UserTable::ID, '=', $userId],
            ])->update($save);
        }

        $msg = new Success();
        $msg->setSuccess(true);
        return $msg;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '修改密码失败')]
    public function changePwd(UserProto $request): Success
    {
        $phone = $request->getPhone();
        if (empty($phone)) {
            throw new AppException("请输入手机号码");
        }
        $pwd = $this->checkPwdParam($request);

        $code = $request->getCode();
        if (empty($code)) {
            throw new AppException("请输入验证码");
        }

        SmsService::check($phone, $code);


        $userTable = (new UserTable)->where([
            [UserTable::PHONE, '=', $phone],
        ])->selectOne();
        if (empty($userTable)) {
            throw new AppException("用户不存在");
        }

        if ($userTable->password == $pwd) {
            throw new AppException("不能与原密码相同");
        }

        (new UserTable)->where([
            [UserTable::ID, '=', $userTable->id]
        ])->update([
            UserTable::PASSWORD => $pwd,
        ]);

        $message = new Success();
        $message->setSuccess(true);

        return $message;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '注销账号失败')]
    public function cancel(UserProto $request): Success
    {
        $userId = $request->getId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }


        $user = new UserTable()->where([
            [UserTable::ID, '=', $userId]
        ])->selectOne();
        if (empty($user)) {
            throw new AppException("用户不存在");
        }

        if ($user->phone) {
            $code = $request->getCode();
            if (empty($code)) {
                throw new AppException("请输入验证码");
            }
            SmsService::check($user->phone, $code);
        }


        (new UserTable)->where([
            [UserTable::ID, '=', $userId],
        ])->update([
            UserTable::PHONE => $user->phone . ' - cancel',
            UserTable::USERNAME => $user->username . ' - cancel',
            UserTable::OPENID => $user->openid . ' - cancel',
            UserTable::IS_CANCEL => 1,
        ]);

        $message = new Success();
        $message->setSuccess(true);

        return $message;

    }

    /**
     * @throws Exception
     */
    private function checkPwdParam(UserProto $request): string
    {

        $pwd = $request->getPassword();
        if (empty($pwd)) {
            throw new AppException("请输入密码");
        }

        $pwd2 = $request->getPassword2();
        if (empty($pwd2)) {
            throw new AppException("请输入确认密码");
        }
        if ($pwd != $pwd2) {
            throw new AppException("两次密码不一致");
        }

        return password_hash($pwd, PASSWORD_DEFAULT);
    }


}