<?php

namespace App\Controller\Api\User;


use App\Service\WxService;
use Generate\Tables\Datas\HongBaoTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Generate\Models\Datas\UserHongBaoModel;
use Generate\Tables\Datas\UserHongBaoTable;
use Protobuf\Datas\UserHongBao\UserHongBaoProto;
use Swlib\Utils\Ip;
use Throwable;
use Swlib\Connect\PoolRedis;
use Redis;


/*
* 新用户红包
*/

#[Router(method: 'POST')]
class UserHongBaoApi extends AbstractController
{

    const int REPEATED_LOTTERY_DRAW = -1; // 已经抽奖了。不可以继续抽奖了
    const int PLEASE_LOG_IN = -2; // 需要重新登录


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存新用户红包失败')]
    public function save(UserHongBaoProto $request): UserHongBaoProto
    {
        $table = UserHongBaoModel::request($request);

        if (empty($table->userId)) {
            throw new AppException('请输入userId');
        }

        // 获取用户IP
        $ip = Ip::get();

        // 检查IP请求限制
        PoolRedis::call(function (Redis $redis) use ($ip) {
            $ipKey = "hongbao:ip:$ip";
            $ipCount = $redis->incr($ipKey);
            if ($ipCount === 1) {
                $redis->expire($ipKey, 3600); // 1小时过期
            }
            if ($ipCount > 10) { // 每小时最多10次请求
                throw new AppException('请求过于频繁，请稍后再试');
            }
        });

        // 检查用户请求限制
        PoolRedis::call(function (Redis $redis) use ($table) {
            $userKey = "hongbao:user2:$table->userId";
            $userCount = $redis->incr($userKey);
            if ($userCount === 1) {
                $redis->expire($userKey, 3600); // 1小时过期
            }
            if ($userCount > 5) { // 每小时最多5次请求
                throw new AppException('请求过于频繁，请稍后再试');
            }
        });

        // 获取用户并发锁
        $lockKey = "hongbao:lock:$table->userId";
        $lockValue = uniqid();
        $locked = PoolRedis::call(function (Redis $redis) use ($lockKey, $lockValue) {
            return $redis->set($lockKey, $lockValue, ['NX', 'EX' => 10]); // 10秒锁过期
        });

        if (!$locked) {
            throw new AppException('系统繁忙，请稍后再试');
        }

        try {
            $proto = new UserHongBaoProto();
            $userTable = new UserTable()->where([
                [UserTable::ID, '=', $table->userId],
            ])->selectOne();
            if (empty($userTable)) {
                $proto->setId(self::PLEASE_LOG_IN);
                return $proto;
            }
            if (empty($userTable->openid)) {
                // 以前使用手机号码登录的，没有获取到 openid 需要用户重新登录
                $proto->setId(self::PLEASE_LOG_IN);
                return $proto;
            }

            if ($userTable->regTime < strtotime('today')) {
                // 老用户不可领取红包
                $proto->setId(self::REPEATED_LOTTERY_DRAW);
                return $proto;
            }

            $find = new UserHongBaoTable()->field([
                UserHongBaoTable::ID
            ])->where([
                UserHongBaoTable::USER_ID => $table->userId,
            ])->selectOne();


            if ($find) {
                // 已经抽奖了。不可以继续抽奖了
                $proto->setId(self::REPEATED_LOTTERY_DRAW);
                return $proto;
            }

            $lists = new HongBaoTable()->where([
                [HongBaoTable::RATIO, '>', 0]
            ])->selectAll();


            // 红包抽奖
            list($randomId, $randomPrice) = $this->randomHongBao($lists);


            $proto->setId($randomId);
            $table->price = $randomPrice;
            $table->time = time();

            $sn = "newuser" . $table->userId;
            $table->sn = $sn;
            $r = WxService::transfer($userTable->openid, $table->price * 100, $sn);
            if (isset($r['state']) && $r['state'] === 'WAIT_USER_CONFIRM') {
                $proto->setExtStr($r['package_info']);
            } else {
                if (isset($r['code']) && $r['code'] === 'FAIL') {
                    throw new AppException($r['message']);
                }
                throw new AppException('红包发送失败');
            }
            $table->insert();
            return $proto;
        } finally {
            // 释放锁
            PoolRedis::call(function (Redis $redis) use ($lockKey, $lockValue) {
                $redis->del($lockKey);
            });
        }
    }

    private function randomHongBao(array $hongBao): array
    {
        $total = 0;
        $temp = [];
        foreach ($hongBao as $item) {
            $total += $item->ratio;
            $temp[] = [
                'ratio' => $item->ratio,
                'name' => $item->name,
                'id' => $item->id,
                'price' => $item->price
            ];
        }

        // 计算权重
        foreach ($temp as $k => $item) {
            $temp[$k]['weight'] = $item['ratio'] / $total * 10000;
        }

        // 权重升序排列
        usort($temp, function ($a, $b) {
            return $a['weight'] <=> $b['weight'];
        });


        $random = mt_rand(1, 10000);

        // 循环找，找不到就是最后以后
        // 最后一个就是最权重最大的红包
        $index = 0;
        foreach ($temp as $k => $item) {
            $index = $k;
            if ($random <= $item['weight']) {
                break;
            }
        }

        // 最终抽中的红包
        return [$temp[$index]['id'], $temp[$index]['price']];
    }

}