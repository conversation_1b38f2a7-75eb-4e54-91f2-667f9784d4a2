<?php

namespace App\Controller\Api\User;

use App\Model\SpecialityModel;
use App\Model\UserSpecialityModel;
use Exception;
use Generate\Tables\Datas\SpecialityTable;
use Generate\Tables\Datas\UserSpecialityTable;
use Protobuf\Datas\User\UserProto;
use Swlib\Controller\AbstractController;
use Swlib\Router\Router;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Protobuf\Speciality\SpecialityItem;
use Protobuf\Speciality\SpecialityLists;
use Protobuf\Speciality\SpecialityType;
use Protobuf\Speciality\UserSpecialityLists;
use Throwable;

/**
 * 用户特长
 */
#[Router(method: 'POST')]
class SpecialityApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(cache: 3600, errorTitle: '获取特长列表失败')]
    public function lists(Request $request): SpecialityLists
    {
        $typeId = $request->getId();
        $page = $request->getPage() ?: 1;

        $ret = [];
        /** @var SpecialityTable $table */
        foreach (UserSpecialityModel::lists(typeId: $typeId, page: $page) as $table) {

            $item = new SpecialityItem();
            $item->setId($table->id);
            $item->setName($table->name);

            if ($typeId) {
                $typeMsg = new SpecialityType();
                $typeMsg->setId($typeId);
                $typeMsg->setName(SpecialityModel::TYPES[$typeId]);
                $item->setType($typeMsg);
            }

            $ret[] = $item;
        }

        $message = new SpecialityLists();
        $message->setLists($ret);
        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '用户添加特长失败')]
    public function userAdd(UserSpecialityLists $request): Success
    {
        $saveData = [];

        foreach ($request->getLists() as $list) {
            $specialityId = $list->getId();
            $userId = $list->getUserId();

            $saveData[] = [
                UserSpecialityTable::USER_ID => $userId,
                UserSpecialityTable::SPECIALITY_ID => $specialityId,
            ];
        }

        $res = UserSpecialityModel::userAdd($saveData);
        $msg = new Success();
        $msg->setSuccess($res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取用户特长列表失败')]
    public function getByUserId(UserProto $request): SpecialityLists
    {
        $userId = $request->getId();
        if (empty($userId)) {
            throw new Exception('请登录');
        }
        $message = new SpecialityLists();

        $specialityIds = (new UserSpecialityTable)->where([
            [UserSpecialityTable::USER_ID, '=', $userId]
        ])->getArrayByField(UserSpecialityTable::SPECIALITY_ID);

        if (empty($specialityIds)) {
            return $message;
        }

        $speciality = (new SpecialityTable)->where([
            [SpecialityTable::ID, 'in', $specialityIds]
        ])->formatId2Array(SpecialityTable::ID);


        $specialityMessage = [];
        foreach ($speciality as $list) {
            $item = new SpecialityItem();
            $item->setId($list->id);
            $item->setName($list->name);
            $specialityMessage[] = $item;
        }

        $message = new SpecialityLists();
        $message->setLists($specialityMessage);
        $message->setTotal(count($speciality));
        return $message;


    }

}