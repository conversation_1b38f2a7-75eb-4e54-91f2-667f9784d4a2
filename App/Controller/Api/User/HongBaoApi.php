<?php
namespace App\Controller\Api\User;


use <PERSON>wlib\Controller\AbstractController;
use Swlib\Router\Router;
use Generate\Models\Datas\HongBaoModel;
use Generate\Tables\Datas\HongBaoTable;
use Protobuf\Datas\HongBao\HongBaoProto;
use Protobuf\Datas\HongBao\HongBaoListsProto;
use Throwable;


/*
* 新用户红包
*/
#[Router(method: 'POST')]
class HongBaoApi extends AbstractController{
                


    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '获取新用户红包列表数据失败')]
    public function lists(HongBaoProto $request): HongBaoListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        $order = [HongBaoTable::PRI_KEY=>"desc"];
        $hongBaoTable = new HongBaoTable();
        $lists = $hongBaoTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = HongBaoModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new HongBaoListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

}