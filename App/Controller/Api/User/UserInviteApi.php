<?php

namespace App\Controller\Api\User;


use Generate\Models\Datas\UserModel;
use Generate\Tables\Datas\UserTable;
use Swlib\Controller\AbstractController;
use Swlib\Router\Router;
use Generate\Models\Datas\UserInviteModel;
use Generate\Tables\Datas\UserInviteTable;
use Protobuf\Datas\UserInvite\UserInviteProto;
use Protobuf\Datas\UserInvite\UserInviteListsProto;
use Throwable;


/*
* 用户邀请记录
*/

#[Router(method: 'POST')]
class UserInvite<PERSON>pi extends AbstractController
{
    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取用户邀请记录列表数据失败')]
    public function lists(UserInviteProto $request): UserInviteListsProto
    {
        $userId = $request->getUserId();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [
            [UserInviteTable::USER_ID, '=', $userId],
        ];
        $order = [UserInviteTable::PRI_KEY => "desc"];
        $userInviteTable = new UserInviteTable();
        $lists = $userInviteTable->order($order)->where($where)->page($page, $size)->selectAll();


        $userIds = $userInviteTable->getArrayByField(UserInviteTable::USER_ID);
        $targetUserIds = $userInviteTable->getArrayByField(UserInviteTable::TARGET_USER_ID);

        $userIds = array_unique(array_merge($userIds, $targetUserIds));
        $users = new UserTable()->field([
            UserTable::FIELD_ALL
        ])->where([
            [UserTable::ID, 'in', $userIds]
        ])->formatId2Array(UserTable::ID);


        $protoLists = [];
        foreach ($lists as $table) {
            $proto = UserInviteModel::formatItem($table);


            if (array_key_exists($table->userId, $users)) {
                $proto->setUser(UserModel::formatItem($users[$table->userId]));
            }

            if (array_key_exists($table->targetUserId, $users)) {
                $proto->setTargetUser(UserModel::formatItem($users[$table->targetUserId]));
            }

            $proto->setTimeStr(date('Y-m-d H:i:s', $table->time));
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }


        // 获取今日邀请数量
        $todayInviteCount = new UserInviteTable()->where([
            [UserInviteTable::USER_ID, '=', $userId],
            [UserInviteTable::TIME, '>=', strtotime(date('Y-m-d 00:00:00'))],
        ])->count();

        // 获取总邀请数量
        $totalInviteCount = new UserInviteTable()->where([
            [UserInviteTable::USER_ID, '=', $userId]
        ])->count();

        $ret = new UserInviteListsProto();
        $ret->setLists($protoLists);
        $ret->setTodayInvite($todayInviteCount);
        $ret->setTotalInvite($totalInviteCount);
        return $ret;
    }


}