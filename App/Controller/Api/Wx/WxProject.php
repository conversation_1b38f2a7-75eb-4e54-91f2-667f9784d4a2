<?php

namespace App\Controller\Api\Wx;


use <PERSON>wl<PERSON>\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\WxProjectModel;
use Generate\Tables\Datas\WxProjectTable;
use Protobuf\Datas\WxProject\WxProjectProto;
use Protobuf\Datas\WxProject\WxProjectListsProto;
use Throwable;


#[Router(method: 'POST')]
class WxProject extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(WxProjectProto $request): Success
    {
        $table = WxProjectModel::request($request);

        if (empty($table->name)) {
            throw new AppException('请输入name');
        }
        $table->isEnable = true;
        $table->displaySorting= 0;


        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(WxProjectProto $request): WxProjectListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        $order = [WxProjectTable::ID => "desc"];
        $wxProjectTable = new WxProjectTable();
        $lists = $wxProjectTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = WxProjectModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new WxProjectListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(WxProjectProto $request): WxProjectProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new WxProjectTable()->where([
            WxProjectTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return WxProjectModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(WxProjectProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new WxProjectTable()->where([
            WxProjectTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}