<?php

namespace App\Controller\Api\Wx;

use Generate\Models\Datas\WxEngineerModel;
use Generate\Tables\Datas\CitysTable;
use Generate\Tables\Datas\WxEngineerTable;
use Generate\Tables\Datas\WxFieldTable;
use Generate\Tables\Datas\WxOrderTable;
use Generate\Tables\Datas\WxProjectTable;
use Generate\Tables\Datas\WxServiceTable;
use Protobuf\Datas\WxOrder\WxOrderListsProto;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Success;

use Protobuf\Datas\WxEngineer\WxEngineerListsProto;
use Protobuf\Datas\WxEngineer\WxEngineerProto;
use Protobuf\Datas\WxEngineer\WxEngineerStatusEnum;
use Protobuf\Datas\WxEngineer\WxEngineerTypeEnum;
use Protobuf\Datas\WxField\WxFieldListsProto;
use Protobuf\Datas\WxField\WxFieldProto;
use Protobuf\Datas\WxOrder\WxOrderProto;
use Protobuf\Datas\WxOrder\WxOrderTypeEnum;
use Protobuf\Datas\WxProject\WxProjectListsProto;
use Protobuf\Datas\WxProject\WxProjectProto;
use Protobuf\Datas\WxService\WxServiceListsProto;
use Protobuf\Datas\WxService\WxServiceProto;
use Protobuf\Datas\WxService\WxServiceStatusEnum;
use Throwable;


#[Router(method: "POST")]
class WxApi extends AbstractController
{

    /**
     * @throws Throwable
     * @throws AppException
     */
    #[Router(errorTitle: "工程师入驻失败")]
    public function engineerJoin(WxEngineerProto $request): Success
    {
        $userId = $request->getUserId();
        $name = $request->getName();
        $phone = $request->getPhone();

        $type = $request->getType();
        if ($type) {
            try {
                $type = WxEngineerTypeEnum::name($type);
                $type = WxEngineerTypeEnum::value($type);
            } catch (Throwable) {
                throw new AppException("工程师类型错误");
            }
        } else {
            $type = WxEngineerModel::TypeAll;
        }


        $area = [];
        foreach ($request->getArea() as $value) {
            $area[] = $value;
        }

        $field = [];
        foreach ($request->getField() as $value) {
            $field[] = $value;
        }
        $cardFront = $request->getCardFront();
        $cardBack = $request->getCardBack();
        $certificate = $request->getCertificate();

        if (empty($name)) {
            throw new AppException("请输入姓名");
        }
        if (empty($phone)) {
            throw new AppException("请输入手机号");
        }
        if (empty($area)) {
            throw new AppException("请选择可受理区域");
        }
        if (empty($field)) {
            throw new AppException("请选择擅长领域");
        }
        if (empty($cardFront)) {
            throw new AppException("请上传身份证正面");
        }
        if (empty($cardBack)) {
            throw new AppException("请上传身份证反面");
        }

        $find = new WxEngineerTable()->where([
            WxEngineerTable::USER_ID => $userId,
            WxEngineerTable::TYPE => $type,
        ])->selectOne();

        if (empty($find)) {
            new WxEngineerTable()->insert([
                WxEngineerTable::USER_ID => $userId,
                WxEngineerTable::TYPE => $type,
                WxEngineerTable::NAME => $name,
                WxEngineerTable::PHONE => $phone,
                WxEngineerTable::AREA => json_encode($area),
                WxEngineerTable::FIELD => json_encode($field),
                WxEngineerTable::CARD_FRONT => $cardFront,
                WxEngineerTable::CARD_BACK => $cardBack,
                WxEngineerTable::CERTIFICATE => $certificate,
            ]);
        } else {
            new WxEngineerTable()->where([
                WxEngineerTable::ID => $find->id,
            ])->update([
                WxEngineerTable::TYPE => $type,
                WxEngineerTable::NAME => $name,
                WxEngineerTable::PHONE => $phone,
                WxEngineerTable::AREA => json_encode($area),
                WxEngineerTable::FIELD => json_encode($field),
                WxEngineerTable::CARD_FRONT => $cardFront,
                WxEngineerTable::CARD_BACK => $cardBack,
                WxEngineerTable::CERTIFICATE => $certificate,
                WxEngineerTable::STATUS => WxEngineerModel::StatusPending,
            ]);
        }


        $success = new Success();
        $success->setSuccess(true);
        return $success;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function engineerDelete(WxEngineerProto $request): Success
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }

        $res = new WxEngineerTable()->where([
            WxEngineerTable::USER_ID => $userId,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     * @throws AppException
     */
    #[Router(errorTitle: "查看工程师详情失败")]
    public function engineerDetail(WxEngineerProto $request): WxEngineerProto
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }
        $type = $request->getType();

        $where = [];
        $where[] = [WxEngineerTable::USER_ID, '=', $userId];
        if (!empty($type)) {
            try {
                $type = WxEngineerTypeEnum::name($type);
                $type = WxEngineerTypeEnum::value($type);
            } catch (Throwable) {
                throw new AppException("工程师类型错误");
            }
            $where[] = [WxEngineerTable::TYPE, '=', $type];
        }


        $table = new WxEngineerTable()->where($where)->selectOne();
        if (empty($table)) {
            $proto = new WxEngineerProto();
            $proto->setId(0);
            return $proto;
        }

        $city2Name = [];
        $areaIds = $table->area;
        if (!empty($areaIds)) {
            $city2Name = new CitysTable()->where([
                [CitysTable::ID, 'in', $areaIds]
            ])->formatId2Name(CitysTable::ID, CitysTable::NAME);
        }

        $field2Name = [];
        $fieldIds = $table->field;
        if (!empty($fieldIds)) {
            $field2Name = new WxFieldTable()->where([
                [WxFieldTable::ID, 'in', $fieldIds]
            ])->formatId2Name(WxFieldTable::ID, WxFieldTable::NAME);
        }


        return $this->_formatItem($table, $city2Name, $field2Name);

    }

    /**
     * @throws Throwable
     */
    private function _formatItem(WxEngineerTable $table, array $city2Name, array $field2Name): WxEngineerProto
    {
        $area = [];
        $areaStr = [];
        if ($table->area) {
            foreach ($table->area as $value) {
                $area[] = $value;
                $areaStr[] = $city2Name[$value] ?? '';
            }
        }

        $field = [];
        $fieldStr = [];
        if ($table->field) {
            foreach ($table->field as $value) {
                $field[] = $value;
                $fieldStr[] = $field2Name[$value] ?? '';
            }
        }


        $proto = new WxEngineerProto();
        $proto->setId($table->id);
        $proto->setUserId($table->userId);
        $proto->setName($table->name);
        $proto->setPhone($table->phone);
        $proto->setArea($area);
        $proto->setType(WxEngineerTypeEnum::value($table->type));
        $proto->setAreaStr($areaStr);
        $proto->setField($field);
        $proto->setFieldStr($fieldStr);
        $proto->setCardFront($table->cardFront);
        $proto->setCardBack($table->cardBack);
        $proto->setCertificate($table->certificate);
        $proto->setTimeStr(date('Y-m-d H:i:s', $table->time));
        $proto->setStatus(WxEngineerStatusEnum::value($table->status ?: 'UNDEFINED_STATUS'));
        return $proto;
    }

    /**
     * @throws Throwable
     * @throws AppException
     */
    #[Router(errorTitle: "工程师搜索失败")]
    public function engineerSearch(WxEngineerProto $request): WxEngineerListsProto
    {
        $type = $request->getType();
        $field = $request->getField();
        $area = $request->getArea();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        if (!empty($type)) {
            try {
                $type = WxEngineerTypeEnum::name($type);
                $type = WxEngineerTypeEnum::value($type);
            } catch (Throwable) {
                throw new AppException("工程类型错误");
            }
            $where[] = [WxEngineerTable::TYPE, 'in', [$type,'all']];
        }


        // 审核通过了才显示
        $where[] = [WxEngineerTable::STATUS, '=', WxEngineerStatusEnum::APPROVED];


        if (!empty($area)) {
            $queryAreaIds = [];
            foreach ($area as $item) {
                $queryAreaIds[] = $item;
            }
            $where[] = [WxEngineerTable::AREA, 'json_contains', $queryAreaIds];
        }

        if (!empty($field)) {
            $queryFieldIds = [];
            foreach ($field as $item) {
                $queryFieldIds[] = $item;
            }
            $where[] = [WxEngineerTable::FIELD, 'json_contains', $queryFieldIds];
        }

        $table = new WxEngineerTable();
        $all = $table->page($page, $size)->where($where)->selectAll();

        $areaIds = [];
        foreach ($table->getArrayByField(WxEngineerTable::AREA) as $value) {
            if (empty($value)) continue;
            foreach (json_decode($value, true) as $id) {
                $areaIds[] = $id;
            }
        }


        $city2Name = new CitysTable()->where([
            [CitysTable::ID, 'in', $areaIds]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);


        $fieldIds = [];
        foreach ($table->getArrayByField(WxEngineerTable::FIELD) as $value) {
            if (empty($value)) continue;
            foreach (json_decode($value, true) as $id) {
                $fieldIds[] = $id;
            }
        }

        $field2Name = new WxFieldTable()->where([
            [WxFieldTable::ID, 'in', $fieldIds]
        ])->formatId2Name(WxFieldTable::ID, WxFieldTable::NAME);


        $lists = [];
        foreach ($all as $list) {
            $lists[] = $this->_formatItem($list, $city2Name, $field2Name);
        }


        $ret = new WxEngineerListsProto();
        $ret->setLists($lists);
        return $ret;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: "获取擅长领域失败")]
    public function getFields(): WxFieldListsProto
    {
        $nodes = [];
        foreach (new WxFieldTable()->order([
            WxFieldTable::DISPLAY_SORTING => 'asc'
        ])->selectAll() as $item) {
            $itemMsg = new WxFieldProto();
            $itemMsg->setId($item->id);
            $itemMsg->setName($item->name);
            $nodes[] = $itemMsg;
        }


        $msg = new WxFieldListsProto();
        $msg->setLists($nodes);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: "获取项目失败")]
    public function getProject(): WxProjectListsProto
    {
        $nodes = [];
        foreach (new WxProjectTable()->order([
            WxProjectTable::DISPLAY_SORTING => 'asc'
        ])->selectAll() as $item) {
            $itemMsg = new WxProjectProto();
            $itemMsg->setId($item->id);
            $itemMsg->setName($item->name);
            $nodes[] = $itemMsg;
        }


        $msg = new WxProjectListsProto();
        $msg->setLists($nodes);
        return $msg;
    }


    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(errorTitle: "获取我的订单失败")]
    public function getOrder(WxOrderProto $request): WxOrderListsProto
    {
        $engineerId = $request->getEngineerId();
        $type = $request->getType();
        if (empty($engineerId)) {
            throw new AppException("工程师ID不能为空");
        }


        if (empty($type)) {
            throw new AppException("请选择工程师类型");
        }
        try {
            $type = WxOrderTypeEnum::value($type);
        } catch (Throwable) {
            throw new AppException("工程类型错误");
        }


        $all = new WxOrderTable()
            ->join(WxEngineerTable::TABLE_NAME, WxEngineerTable::ID, WxOrderTable::ENGINEER_ID)
            ->join(WxServiceTable::TABLE_NAME, WxServiceTable::ID, WxOrderTable::SERVICE_ID)
            ->where([
                [WxOrderTable::ENGINEER_ID, '=', $engineerId],
                [WxOrderTable::TYPE, '=', $type],
            ])
            ->field([
                WxOrderTable::ID,
                WxOrderTable::TIME,
                WxOrderTable::ENGINEER_ID,
                WxEngineerTable::NAME,
                WxEngineerTable::PHONE,
                WxServiceTable::MEN_ZHEN,
                WxServiceTable::AREA,
                WxServiceTable::ADDR,
                WxServiceTable::NAME,
                WxServiceTable::PHONE,
                WxServiceTable::DESC,
                WxServiceTable::MANUFACTURER,
            ])->selectAll();

        $cityIds = [];
        foreach ($all as $item) {
            $cityIds[] = $item->getByField(WxServiceTable::AREA);
        }

        $cityId2Name = new CitysTable()->where([
            [CitysTable::ID, 'in', $cityIds]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);


        $nodes = [];
        foreach ($all as $item) {

            $itemMsg = new WxOrderProto();
            $itemMsg->setId($item->id);
            $itemMsg->setTimeStr(date('Y-m-d H:i:s', $item->time));

            $engineer = new WxEngineerProto();
            $engineer->setId($item->engineerId);
            $engineer->setName($item->getByField(WxEngineerTable::NAME));
            $engineer->setPhone($item->getByField(WxEngineerTable::PHONE));
            $itemMsg->setEngineer($engineer);

            $service = new WxServiceProto();
            $service->setMenZhen($item->getByField(WxServiceTable::MEN_ZHEN));
            $service->setAreaStr($cityId2Name[$item->getByField(WxServiceTable::AREA)] ?? "");
            $service->setAddr($item->getByField(WxServiceTable::ADDR));
            $service->setName($item->getByField(WxServiceTable::NAME));
            $service->setPhone($item->getByField(WxServiceTable::PHONE));
            $service->setDesc($item->getByField(WxServiceTable::DESC));
            $service->setManufacturer($item->getByField(WxServiceTable::MANUFACTURER));
            $itemMsg->setService($service);

            $nodes[] = $itemMsg;

        }

        $ret = new WxOrderListsProto();
        $ret->setLists($nodes);

        return $ret;

    }

    /**
     * @throws Throwable
     * @throws AppException
     */
    #[Router(errorTitle: '发布服务失败')]
    public function serviceAdd(WxServiceProto $request): Success
    {
        $menZhen = $request->getMenZhen();
        $areaId = $request->getArea();
        $addr = $request->getAddr();
        $name = $request->getName();
        $phone = $request->getPhone();
        $desc = $request->getDesc();
        $manufacturer = $request->getManufacturer();
        $type = $request->getType();
        $pics = $request->getPics();
        $projectIds = $request->getProjectIds();
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException("请登录");
        }

        if (empty($type)) {
            throw new AppException("请选择服务类型");
        }
        try {
            $type = WxEngineerTypeEnum::name($type);
            $type = WxEngineerTypeEnum::value($type);
        } catch (Throwable) {
            throw new AppException("服务类型错误");
        }

        $data = [
            WxServiceTable::STATUS => WxServiceStatusEnum::WAIT,
            WxServiceTable::TIME => time(),
            WxServiceTable::USER_ID => $userId,
            WxServiceTable::TYPE => $type,
        ];

        if ($menZhen) {
            $data[WxServiceTable::MEN_ZHEN] = $menZhen;
        }
        if ($projectIds) {
            $saveIds = [];
            foreach ($projectIds as $id) {
                $saveIds[] = $id;
            }
            $data[WxServiceTable::PROJECT_IDS] = json_encode($saveIds);
        }
        if ($areaId) {
            $data[WxServiceTable::AREA] = $areaId;
        }
        if ($addr) {
            $data[WxServiceTable::ADDR] = $addr;
        }
        if ($name) {
            $data[WxServiceTable::NAME] = $name;
        }
        if ($phone) {
            $data[WxServiceTable::PHONE] = $phone;
        }
        if ($desc) {
            $data[WxServiceTable::DESC] = $desc;
        }
        if ($manufacturer) {
            $data[WxServiceTable::MANUFACTURER] = $manufacturer;
        }
        if ($pics) {
            $data[WxServiceTable::PICS] = $pics;
        }

        new WxServiceTable()->insert($data);

        $msg = new Success();
        $msg->setSuccess(true);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取服务列表失败')]
    public function serviceLists(WxServiceProto $request): WxServiceListsProto
    {
        $areaId = $request->getArea();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $type = $request->getType();
        $projectIds = $request->getProjectIds();
        $usrId = $request->getUserId();

        if (empty($type)) {
            throw new AppException("请选择服务类型");
        }
        try {
            $type = WxEngineerTypeEnum::name($type);
            $type = WxEngineerTypeEnum::value($type);
        } catch (Throwable) {
            throw new AppException("服务类型错误");
        }

        $where = [];
        $where[] = [WxServiceTable::TYPE, '=', $type];
        if ($areaId) {
            $where[] = [WxServiceTable::AREA, '=', $areaId];
        }

        if ($usrId) {
            $where[] = [WxServiceTable::USER_ID, '=', $usrId];
        }

        if ($projectIds) {
            $saveIds = [];
            foreach ($projectIds as $id) {
                $saveIds[] = $id;
            }
            $where[] = [WxServiceTable::PROJECT_IDS, 'json_contains', $saveIds];
        }


        $table = new WxServiceTable();
        $all = $table->page($page, $size)->order([
            WxServiceTable::ID => 'desc'
        ])->where($where)->selectAll();
        $cityIds = $table->getArrayByField(WxServiceTable::AREA);
        $projectIds = [];
        foreach ($table->getArrayByField(WxServiceTable::PROJECT_IDS) as $row) {
            if ($row) {
                $arr = json_decode($row, true);
                foreach ($arr as $id) {
                    $projectIds[] = $id;
                }
            }
        }


        $cityId2Name = new CitysTable()->where([
            [CitysTable::ID, 'in', $cityIds]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);

        $project2Name = new WxProjectTable()->where([
            [WxProjectTable::ID, 'in', $projectIds]
        ])->formatId2Name(WxProjectTable::ID, WxProjectTable::NAME);


        $nodes = [];
        foreach ($all as $item) {
            $itemMsg = new WxServiceProto();
            $itemMsg->setId($item->id);
            $itemMsg->setMenZhen($item->getByField(WxServiceTable::MEN_ZHEN, ''));

            $projectNames = [];
            if ($ids = $item->projectIds) {
                foreach ($ids as $id) {
                    $projectNames[] = $project2Name[$id] ?? '';
                }
            }
            $itemMsg->setProjectIdsStr($projectNames);


            $itemMsg->setArea((int)$item->area);
            $itemMsg->setAreaStr($cityId2Name[$item->area] ?? '');
            $itemMsg->setAddr($item->getByField(WxServiceTable::ADDR, ''));
            $itemMsg->setName($item->getByField(WxServiceTable::NAME));
            $itemMsg->setPhone($item->getByField(WxServiceTable::PHONE, ''));
            $itemMsg->setDesc($item->getByField(WxServiceTable::DESC, ''));
            $itemMsg->setManufacturer($item->getByField(WxServiceTable::MANUFACTURER, ''));
            $itemMsg->setPics($item->getByField(WxServiceTable::PICS, ''));
            $itemMsg->setStatus(WxServiceStatusEnum::value($item->getByField(WxServiceTable::STATUS)));
            $itemMsg->setEngineerId($item->getByField(WxServiceTable::ENGINEER_ID, 0));
            $itemMsg->setTimeStr(date('Y-m-d H:i:s', $item->time));
            $itemMsg->setUserId($item->userId ?: 0);
            $nodes[] = $itemMsg;
        }

        $ret = new WxServiceListsProto();
        $ret->setLists($nodes);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除维修服务失败')]
    public function serviceDelete(WxServiceProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new WxServiceTable()->where([
            WxServiceTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


}