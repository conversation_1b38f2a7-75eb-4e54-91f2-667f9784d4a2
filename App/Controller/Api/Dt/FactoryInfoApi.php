<?php
namespace App\Controller\Api\Dt;


use <PERSON><PERSON>ib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\FactoryInfoModel;
use Generate\Tables\Datas\FactoryInfoTable;
use Protobuf\Datas\FactoryInfo\FactoryInfoProto;
use Protobuf\Datas\FactoryInfo\FactoryInfoListsProto;
use Throwable;


/*
* 加工厂信息表
*/
#[Router(method: 'POST')]
class FactoryInfoApi extends AbstractController{
                

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '保存加工厂信息表失败')]
    public function save(FactoryInfoProto $request): Success
    {
        $table = FactoryInfoModel::request($request);

        if (empty($table->id)){
            throw new AppException('请输入id');
        }
        if (empty($table->userId)){
            throw new AppException('请输入用户ID');
        }
        if (empty($table->name)){
            throw new AppException('请输入品牌名称');
        }
        if (empty($table->logo)){
            throw new AppException('请输入品牌logo图片地址');
        }
        if (empty($table->contact)){
            throw new AppException('请输入联系人');
        }
        if (empty($table->phone)){
            throw new AppException('请输入联系电话');
        }
        if (empty($table->area)){
            throw new AppException('请输入承接区域');
        }
        if (empty($table->description)){
            throw new AppException('请输入产品介绍');
        }
        if (empty($table->images)){
            throw new AppException('请输入产品图片，多个图片URL用逗号分隔');
        }
        if (empty($table->status)){
            throw new AppException('请输入状态：0-下架 1-上架');
        }
        if (empty($table->createTime)){
            throw new AppException('请输入创建时间');
        }
        if (empty($table->updateTime)){
            throw new AppException('请输入更新时间');
        }

        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '获取加工厂信息表列表数据失败')]
    public function lists(FactoryInfoProto $request): FactoryInfoListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        $order = [FactoryInfoTable::PRI_KEY=>"desc"];
        $factoryInfoTable = new FactoryInfoTable();
        $lists = $factoryInfoTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = FactoryInfoModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new FactoryInfoListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '查看加工厂信息表详情失败')]
    public function detail(FactoryInfoProto $request): FactoryInfoProto
    {
        $id = $request->getId();
        if(empty($id)){
            throw new AppException("缺少参数");
        }

        $table = new FactoryInfoTable()->where([
            FactoryInfoTable::ID=>$id,
        ])->selectOne();
        if(empty($table)){
            throw new AppException("参数错误");
        }

        return FactoryInfoModel::formatItem($table);
    }

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '删除加工厂信息表失败')]
    public function delete(FactoryInfoProto $request): Success
    {
        $id = $request->getId();
        if(empty($id)){
            throw new AppException("参数错误");
        }

        $res = new FactoryInfoTable()->where([
            FactoryInfoTable::ID=>$id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}