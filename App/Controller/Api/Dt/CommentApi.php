<?php

namespace App\Controller\Api\Dt;


use Generate\Models\Datas\UserModel;
use Generate\Tables\Datas\UserTable;
use Swlib\Controller\AbstractController;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\CommentModel;
use Generate\Tables\Datas\CommentTable;
use Protobuf\Datas\Comment\CommentProto;
use Protobuf\Datas\Comment\CommentListsProto;
use Throwable;


/*
* 
*/

#[Router(method: 'POST')]
class CommentApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存失败')]
    public function save(CommentProto $request): Success
    {
        $table = CommentModel::request($request);

        if (empty($table->comment)) {
            throw new AppException('请输入评论具体内容');
        }

        if (empty($table->userId)) {
            throw new AppException('请输入userId');
        }

        if (empty($table->productId)) {
            throw new AppException('请输入商品ID');
        }
        if (empty($table->productTable)) {
            $table->productTable = 'hy_companies_service';
        }

        if (!$table->descNum) {
            $table->descNum = 0;
        }

        Event::emit('count.companiesService.commentNum', [
            'productId' => $table->productId,
        ]);
        $table->time = time();

        $res = $table->save();
        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(CommentProto $request): CommentListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $productId = $request->getProductId();
        $productTable = $request->getProductTable();

        if (empty($productTable) || empty($productId)) {
            throw new AppException('参数错误');
        }

        $where = [
            CommentTable::PRODUCT_TABLE => $productTable,
            CommentTable::PRODUCT_ID => $productId,
        ];
        $order = [CommentTable::PRI_KEY => "desc"];
        $commentTable = new CommentTable();
        $lists = $commentTable->order($order)->where($where)->page($page, $size)->selectAll();
        $total = new CommentTable()->where($where)->count();
        $userIds = $commentTable->getArrayByField(CommentTable::USER_ID);

        $users = new UserTable()->where([
            [UserTable::ID, 'in', $userIds],
        ])->field([
            UserTable::FIELD_ALL,
        ])->formatId2Array(UserTable::ID);


        $protoLists = [];
        foreach ($lists as $table) {
            if (empty($table->anonymous)) {
                $table->anonymous = 0;
            }
            if (empty($table->compositeNum)) {
                $table->compositeNum = 0;
            }
            if (empty($table->logisticsNum)) {
                $table->logisticsNum = 0;
            }
            if (empty($table->customerServiceNum)) {
                $table->customerServiceNum = 0;
            }
            if (empty($table->commentTag)) {
                $table->commentTag = 1;
            }

            $proto = CommentModel::formatItem($table);

            if (isset($users[$table->userId])) {
                $proto->setUser(UserModel::formatItem($users[$table->userId]));
            }

            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }


        $ret = new CommentListsProto();
        $ret->setLists($protoLists);
        $ret->setTotal($total);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(CommentProto $request): CommentProto
    {
        $id = $request->getId();
        $userId = $request->getUserId();
        if (empty($id) || empty($userId)) {
            throw new AppException("缺少参数");
        }

        $table = new CommentTable()->where([
            CommentTable::ID => $id,
            CommentTable::USER_ID => $userId,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return CommentModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除失败')]
    public function delete(CommentProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new CommentTable()->where([
            CommentTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}