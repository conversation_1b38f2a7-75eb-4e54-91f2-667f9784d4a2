<?php

namespace App\Controller\Api\Dt;


use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\DrugDisplaysModel;
use Generate\Tables\Datas\DrugDisplaysTable;
use Protobuf\Datas\DrugDisplays\DrugDisplaysProto;
use Protobuf\Datas\DrugDisplays\DrugDisplaysListsProto;
use Throwable;


/*
* 药品展示表
*/

#[Router(method: 'POST')]
class DrugDisplaysApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存药品展示表失败')]
    public function save(DrugDisplaysProto $request): Success
    {
        $table = DrugDisplaysModel::request($request);


        if (empty($table->hyCompaniesId)) {
            throw new AppException('请输入厂商ID');
        }
        if (empty($table->name)) {
            throw new AppException('请输入产品名称');
        }

        $table->status = 1;
        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取药品展示表列表数据失败')]
    public function lists(DrugDisplaysProto $request): DrugDisplaysListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $hyCompaniesId = $request->getHyCompaniesId();
        $name = $request->getName();

        if (empty($hyCompaniesId)) {
            throw new AppException("缺少参数");
        }


        $where = [];

        $where[] = [DrugDisplaysTable::HY_COMPANIES_ID, '=', $hyCompaniesId];
        if ($name) {
            $where[] = [DrugDisplaysTable::NAME, 'like', "%$name%"];
        }

        $order = [DrugDisplaysTable::PRI_KEY => "desc"];
        $drugDisplaysTable = new DrugDisplaysTable();
        $lists = $drugDisplaysTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = DrugDisplaysModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new DrugDisplaysListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除药品展示表失败')]
    public function delete(DrugDisplaysProto $request): Success
    {
        $name = $request->getName();
        $hyCompaniesId = $request->getHyCompaniesId();
        if (empty($name) || empty($hyCompaniesId)) {
            throw new AppException("参数错误");
        }

        $res = new DrugDisplaysTable()->where([
            DrugDisplaysTable::NAME => $name,
            DrugDisplaysTable::HY_COMPANIES_ID => $hyCompaniesId,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}