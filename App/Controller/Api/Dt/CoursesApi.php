<?php

namespace App\Controller\Api\Dt;


use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\CoursesModel;
use Generate\Tables\Datas\CoursesTable;
use Protobuf\Datas\Courses\CoursesProto;
use Protobuf\Datas\Courses\CoursesListsProto;
use Throwable;


/*
* 课程表
*/

#[Router(method: 'POST')]
class CoursesApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存课程表失败')]
    public function save(CoursesProto $request): CoursesProto
    {
        $table = CoursesModel::request($request);

        if (empty($table->name)) {
            throw new AppException('请输入课程名称');
        }
        if (empty($table->userId)) {
            throw new AppException('请输入用户ID');
        }
        if (empty($table->category)) {
            throw new AppException('请输入课程分类');
        }
        if (empty($table->format)) {
            throw new AppException('请输入课程形式');
        }
        if (empty($table->startTime)) {
            throw new AppException('请输入开课时间');
        }
        if (empty($table->location)) {
            throw new AppException('请输入开课地址');
        }
        if (empty($table->price)) {
            throw new AppException('请输入课程价格');
        }
        if (empty($table->description)) {
            throw new AppException('请输入课程介绍');
        }
        if (empty($table->teacherDescription)) {
            throw new AppException('请输入讲师介绍');
        }
        if (empty($table->courseImages)) {
            throw new AppException('请输入课程图片URL，逗号分割存储');
        }

        if (empty($table->lecturer)) {
            throw new AppException('请输入讲师名称');
        }

        $table->status = 0;
        if ($table->id) {
            $table->status = 0;
        }

        $table->save();

        $msg = new CoursesProto();
        $msg->setId($table->id);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发布服务失败')]
    public function publish(CoursesProto $request): Success
    {
        $status = $request->getStatus() ?: 0;
        $id = $request->getId();

        if (empty($id)) {
            throw new AppException('请选择要发布的服务');
        }

        $res = new CoursesTable()->where([
            [CoursesTable::ID, '=', $id]
        ])->update([
            CoursesTable::STATUS => $status
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取课程表列表数据失败')]
    public function lists(CoursesProto $request): CoursesListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $name = $request->getName();
        $category = $request->getCategory();
        $format = $request->getFormat();
        $location = $request->getLocation();
        $lecturer = $request->getLecturer();
        $userId = $request->getUserId();


        $where = [];


        if ($category) {
            $where[] = [CoursesTable::CATEGORY, "=", $category];
        }
        if ($format) {
            $where[] = [CoursesTable::FORMAT, "=", $format];
        }

        if ($location) {
            $where[] = [CoursesTable::LOCATION, "=", $location];
        }

        if ($lecturer) {
            $where[] = [CoursesTable::LECTURER, "like", "%$lecturer%"];
        }

        if ($name) {
            $where[] = [CoursesTable::NAME, "like", "%$name%"];
        }
        if ($userId) {
            $where[] = [CoursesTable::USER_ID, "=", $userId];
        }

        $order = [CoursesTable::PRI_KEY => "desc"];
        $coursesTable = new CoursesTable();
        $lists = $coursesTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = CoursesModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new CoursesListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看课程表详情失败')]
    public function detail(CoursesProto $request): CoursesProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new CoursesTable()->where([
            CoursesTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return CoursesModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除课程表失败')]
    public function delete(CoursesProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new CoursesTable()->where([
            CoursesTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发布课程表失败')]
    public function pub(CoursesProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new CoursesTable()->where([
            CoursesTable::ID => $id,
        ])->update([
            CoursesTable::STATUS => 1,
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}