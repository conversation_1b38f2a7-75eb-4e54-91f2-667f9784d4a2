<?php

namespace App\Controller\Api\Dt;


use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\FavoritesModel;
use Generate\Tables\Datas\FavoritesTable;
use Protobuf\Datas\Favorites\FavoritesProto;
use Protobuf\Datas\Favorites\FavoritesListsProto;
use ReflectionClass;
use Swlib\Table\Db;
use Throwable;


/*
* 收藏表
*/

#[Router(method: 'POST')]
class FavoritesApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存收藏表失败')]
    public function save(FavoritesProto $request): Success
    {
        $table = FavoritesModel::request($request);

        if (empty($table->userId)) {
            throw new AppException('请输入用户ID');
        }
        if (empty($table->itemId)) {
            throw new AppException('请输入收藏项目ID');
        }
        if (empty($table->type)) {
            throw new AppException('请输入收藏的表格名称');
        }


        // 使用反射获取表格类并查询数据
        try {
            // 将下划线分隔的表名转换为驼峰式类名
            $className = '';
            $parts = explode('_', $table->type);
            foreach ($parts as $part) {
                $className .= ucfirst($part);
            }
            $className .= 'Table';

            // 构建完整的类名
            $fullClassName = "Generate\\Tables\\Datas\\$className";
            // 使用反射检查类是否存在
            if (class_exists($fullClassName)) {
                // 获取主键常量
                $reflectionClass = new ReflectionClass($fullClassName);
                $priKeyConstant = $reflectionClass->getConstants()['PRI_KEY'];

                // 实例化表格并查询数据
                $targetTable = new $fullClassName();
                $targetRecord = $targetTable->where([
                    $priKeyConstant => $table->itemId
                ])->selectOne();

                if ($targetRecord) {
                    // 填充收藏项目的相关信息
                    if (isset($targetRecord->name) && $targetRecord->name) {
                        $table->name = $targetRecord->name;
                    } elseif (isset($targetRecord->title) && $targetRecord->title) {
                        $table->name = $targetRecord->title;
                    } elseif (isset($targetRecord->productName) && $targetRecord->productName) {
                        $table->name = $targetRecord->productName;
                    } elseif (isset($targetRecord->title) && $targetRecord->title) {
                        $table->name = $targetRecord->title;
                    }

                    if (isset($targetRecord->description) && $targetRecord->description) {
                        $table->desc = $targetRecord->description;
                    } elseif (isset($targetRecord->desc) && $targetRecord->desc) {
                        $table->desc = $targetRecord->desc;
                    } elseif (isset($targetRecord->content) && $targetRecord->content) {
                        $table->desc = mb_substr(strip_tags($targetRecord->content), 0, 100);
                    }

                    if (isset($targetRecord->images) && $targetRecord->images) {
                        $arr = explode(',', $targetRecord->images);
                        $table->image = $arr[0];
                    } elseif (isset($targetRecord->image) && $targetRecord->image) {
                        $table->image = $targetRecord->image;
                    } elseif (isset($targetRecord->cover) && $targetRecord->cover) {
                        $table->image = $targetRecord->cover;
                    }


                    if ($table->type == 'zp_resume') {
                        $table->typeName = '简历';
                    } elseif ($table->type == 'zp_jobs') {
                        $table->typeName = '职位';
                    } elseif ($table->type == 'wx_engineer') {
                        $table->typeName = '工程师';
                    } elseif ($table->type == 'hy_companies_service') {
                        $table->typeName = HyCompaniesServiceModel::TypeTextMaps[$targetRecord->type];
                        new HyCompaniesServiceTable()->where([
                            HyCompaniesServiceTable::ID => $table->itemId
                        ])->update([
                            HyCompaniesServiceTable::FAVORITE_COUNT => Db::incr(HyCompaniesServiceTable::FAVORITE_COUNT),
                        ]);
                    } elseif ($table->type == 'wx_service') {
                        $table->typeName = '抢单';
                    }

                }
            }
        } catch (Throwable) {
            // 异常处理，继续保存基本信息
        }

        $res = $table->save([
            FavoritesTable::USER_ID => $table->userId,
            FavoritesTable::ITEM_ID => $table->itemId,
            FavoritesTable::TYPE => $table->type,
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取收藏表列表数据失败')]
    public function lists(FavoritesProto $request): FavoritesListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $userId = $request->getUserId();

        $where = [
            FavoritesTable::USER_ID => $userId,
        ];
        $order = [FavoritesTable::PRI_KEY => "desc"];
        $favoritesTable = new FavoritesTable();
        $lists = $favoritesTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = FavoritesModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new FavoritesListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看收藏表详情失败')]
    public function detail(FavoritesProto $request): FavoritesProto
    {
        $type = $request->getType();
        $userId = $request->getUserId();
        $id = $request->getItemId();


        $table = new FavoritesTable()->where([
            FavoritesTable::ITEM_ID => $id,
            FavoritesTable::USER_ID => $userId,
            FavoritesTable::TYPE => $type,
        ])->selectOne();
        if (empty($table)) {
            return new FavoritesProto();
        }

        return FavoritesModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除收藏表失败')]
    public function delete(FavoritesProto $request): Success
    {
        $type = $request->getType();
        $userId = $request->getUserId();
        $id = $request->getItemId();


        $find = new FavoritesTable()->where([
            FavoritesTable::ITEM_ID => $id,
            FavoritesTable::USER_ID => $userId,
            FavoritesTable::TYPE => $type,
        ])->selectOne();


        if ($find->type === 'hy_companies_service') {
            new HyCompaniesServiceTable()->where([
                HyCompaniesServiceTable::ID => $find->itemId
            ])->update([
                HyCompaniesServiceTable::FAVORITE_COUNT => Db::incr(HyCompaniesServiceTable::FAVORITE_COUNT, 1, '-'),
            ]);
        }


        $res = new FavoritesTable()->where([
            FavoritesTable::ITEM_ID => $id,
            FavoritesTable::USER_ID => $userId,
            FavoritesTable::TYPE => $type,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}