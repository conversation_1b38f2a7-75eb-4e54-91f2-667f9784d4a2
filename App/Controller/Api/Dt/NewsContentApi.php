<?php

namespace App\Controller\Api\Dt;


use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\NewsContentModel;
use Generate\Tables\Datas\NewsContentTable;
use Protobuf\Datas\NewsContent\NewsContentProto;
use Protobuf\Datas\NewsContent\NewsContentListsProto;
use Throwable;


/*
*
*/

#[Router(method: 'POST')]
class NewsContentApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存失败')]
    public function save(NewsContentProto $request): Success
    {
        $table = NewsContentModel::request($request);


        if (empty($table->type)) {
            throw new AppException('请输入内容类型：');
        }
        if (empty($table->content)) {
            throw new AppException('请输入内容');
        }
        if (empty($table->targetId)) {
            throw new AppException('请输入新闻ID');
        }

        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(NewsContentProto $request): NewsContentListsProto
    {

        $targetId = $request->getTargetId();


        $where = [NewsContentTable::TARGET_ID => $targetId];
        $order = [NewsContentTable::PRI_KEY => "asc"];
        $newsContentTable = new NewsContentTable();
        $lists = $newsContentTable->order($order)->where($where)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = NewsContentModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new NewsContentListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(NewsContentProto $request): NewsContentProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new NewsContentTable()->where([
            NewsContentTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return NewsContentModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除失败')]
    public function delete(NewsContentProto $request): Success
    {
        $id = $request->getId();
        $targetId = $request->getTargetId();

        $res = false;

        if (!empty($id)) {
            $res = new NewsContentTable()->where([
                NewsContentTable::ID => $id,
            ])->delete();
        } elseif (!empty($targetId)) {
            $res = new NewsContentTable()->where([
                NewsContentTable::TARGET_ID => $targetId,
            ])->delete();
        }


        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}