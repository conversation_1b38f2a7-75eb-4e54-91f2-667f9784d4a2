<?php

namespace App\Controller\Api\Dt;


use Generate\Tables\Datas\HyCompaniesTable;
use Swlib\Connect\PoolRedis;
use <PERSON>wlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\CustomProductsModel;
use Generate\Tables\Datas\CustomProductsTable;
use Protobuf\Datas\CustomProducts\CustomProductsProto;
use Protobuf\Datas\CustomProducts\CustomProductsListsProto;
use Throwable;


/*
* 定制产品表
*/

#[Router(method: 'POST')]
class CustomProductsApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存定制产品表失败')]
    public function save(CustomProductsProto $request): Success
    {
        $table = CustomProductsModel::request($request);


        if (empty($table->hyCompaniesId)) {
            throw new AppException('请输入货源厂家ID');
        }
        if (empty($table->productName)) {
            throw new AppException('请输入产品名称');
        }

        $table->status = 1;

        $res = $table->save();


        // 更新为定制厂家
        PoolRedis::getSet("setCustomCompanies$table->hyCompaniesId", function () use ($table) {
            new HycompaniesTable()->where([
                HycompaniesTable::ID => $table->hyCompaniesId,
            ])->update([
                HycompaniesTable::IS_CUSTOM => 1,
            ]);
        }, 86400);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取定制产品表列表数据失败')]
    public function lists(CustomProductsProto $request): CustomProductsListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $hyCompaniesId = $request->getHyCompaniesId();

        $where = [];
        if ($hyCompaniesId) {
            $where[] = [CustomProductsTable::HY_COMPANIES_ID, "=", $hyCompaniesId];
        }

        $order = [CustomProductsTable::PRI_KEY => "desc"];
        $customProductsTable = new CustomProductsTable();
        $lists = $customProductsTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = CustomProductsModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new CustomProductsListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看定制产品表详情失败')]
    public function detail(CustomProductsProto $request): CustomProductsProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new CustomProductsTable()->where([
            CustomProductsTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return CustomProductsModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除定制产品表失败')]
    public function delete(CustomProductsProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new CustomProductsTable()->where([
            CustomProductsTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除定制产品表失败')]
    public function deleteByName(CustomProductsProto $request): Success
    {
        $hyCompaniesId = $request->getHyCompaniesId();
        $productName = $request->getProductName();
        if (empty($hyCompaniesId) || empty($productName)) {
            throw new AppException("参数错误");
        }

        $res = new CustomProductsTable()->where([
            CustomProductsTable::HY_COMPANIES_ID => $hyCompaniesId,
            CustomProductsTable::PRODUCT_NAME => $productName,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}