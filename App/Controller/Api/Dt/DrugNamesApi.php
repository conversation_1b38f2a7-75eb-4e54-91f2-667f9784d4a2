<?php
namespace App\Controller\Api\Dt;


use <PERSON>wlib\Controller\AbstractController;
use Swlib\Router\Router;
use Generate\Models\Datas\DrugNamesModel;
use Generate\Tables\Datas\DrugNamesTable;
use Protobuf\Datas\DrugNames\DrugNamesProto;
use Protobuf\Datas\DrugNames\DrugNamesListsProto;
use Throwable;


/*
* 药品名称
*/
#[Router(method: 'POST')]
class DrugNamesApi extends AbstractController{
                



    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '获取药品名称列表数据失败')]
    public function lists(DrugNamesProto $request): DrugNamesListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        $order = [DrugNamesTable::PRI_KEY=>"desc"];
        $drugNamesTable = new DrugNamesTable();
        $lists = $drugNamesTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = DrugNamesModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new DrugNamesListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }
}