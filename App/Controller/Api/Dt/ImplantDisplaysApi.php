<?php
namespace App\Controller\Api\Dt;


use <PERSON><PERSON><PERSON>\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\ImplantDisplaysModel;
use Generate\Tables\Datas\ImplantDisplaysTable;
use Protobuf\Datas\ImplantDisplays\ImplantDisplaysProto;
use Protobuf\Datas\ImplantDisplays\ImplantDisplaysListsProto;
use Throwable;


/*
* 种植体展示表
*/
#[Router(method: 'POST')]
class ImplantDisplaysApi extends AbstractController{
                

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '保存种植体展示表失败')]
    public function save(ImplantDisplaysProto $request): Success
    {
        $table = ImplantDisplaysModel::request($request);

        if (empty($table->id)){
            throw new AppException('请输入id');
        }
        if (empty($table->userId)){
            throw new AppException('请输入用户ID');
        }
        if (empty($table->brandName)){
            throw new AppException('请输入品牌名称');
        }
        if (empty($table->contactPerson)){
            throw new AppException('请输入联系人');
        }
        if (empty($table->contactPhone)){
            throw new AppException('请输入联系电话');
        }
        if (empty($table->serviceArea)){
            throw new AppException('请输入承接区域');
        }
        if (empty($table->description)){
            throw new AppException('请输入产品介绍');
        }
        if (empty($table->productImages)){
            throw new AppException('请输入产品图片URLs，JSON数组格式');
        }
        if (empty($table->status)){
            throw new AppException('请输入状态：0-已下架，1-展示中');
        }
        if (empty($table->createdAt)){
            throw new AppException('请输入创建时间');
        }
        if (empty($table->updatedAt)){
            throw new AppException('请输入更新时间');
        }

        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '获取种植体展示表列表数据失败')]
    public function lists(ImplantDisplaysProto $request): ImplantDisplaysListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        $order = [ImplantDisplaysTable::PRI_KEY=>"desc"];
        $implantDisplaysTable = new ImplantDisplaysTable();
        $lists = $implantDisplaysTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = ImplantDisplaysModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new ImplantDisplaysListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '查看种植体展示表详情失败')]
    public function detail(ImplantDisplaysProto $request): ImplantDisplaysProto
    {
        $id = $request->getId();
        if(empty($id)){
            throw new AppException("缺少参数");
        }

        $table = new ImplantDisplaysTable()->where([
            ImplantDisplaysTable::ID=>$id,
        ])->selectOne();
        if(empty($table)){
            throw new AppException("参数错误");
        }

        return ImplantDisplaysModel::formatItem($table);
    }

    /**
    * @throws Throwable
    */
    #[Router(errorTitle: '删除种植体展示表失败')]
    public function delete(ImplantDisplaysProto $request): Success
    {
        $id = $request->getId();
        if(empty($id)){
            throw new AppException("参数错误");
        }

        $res = new ImplantDisplaysTable()->where([
            ImplantDisplaysTable::ID=>$id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}