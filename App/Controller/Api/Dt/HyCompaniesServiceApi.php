<?php

namespace App\Controller\Api\Dt;


use App\Service\ExportProductService;
use App\Service\HyCompaniesServiceService;
use Generate\Models\Datas\HyCompaniesModel;
use Generate\Models\Datas\HyProductsSkuModel;
use Generate\Tables\Datas\CitysTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Protobuf\Datas\HyCompanies\HyCompaniesProto;
use Protobuf\Datas\HyCompaniesService\HyCompaniesServiceTypeEnum;
use Swlib\Controller\AbstractController;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Protobuf\Datas\HyCompaniesService\HyCompaniesServiceProto;
use Protobuf\Datas\HyCompaniesService\HyCompaniesServiceListsProto;
use Swlib\Table\Db;
use Swlib\Utils\Func;
use Swlib\Utils\Server;
use Throwable;


/*
* 厂家大厅表,厂家提供那些服务
*/

#[Router(method: 'POST')]
class HyCompaniesServiceApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存厂家服务失败')]
    public function save(HyCompaniesServiceProto $request): HyCompaniesServiceProto
    {
        $table = HyCompaniesServiceModel::request($request);

        HyCompaniesServiceService::verify($table);

        // 如果是编辑 则将发布状态改为未发布
//        if ($table->id) {
//            $table->isPub = 0;
//        }

        $table->save();

        $msg = new HyCompaniesServiceProto();
        $msg->setId($table->id);

        Server::task([__CLASS__, 'countSaleNum'], ['companiesId' => $table->companiesId]);

        return $msg;
    }


    /**
     * 统计在售商品数量
     * @param $data
     * @return void
     * @throws Throwable
     */
    public function countSaleNum($data): void
    {
        $id = $data['companiesId'];
        $count = new HyCompaniesServiceTable()->where([
            HyCompaniesServiceTable::COMPANIES_ID => $id,
            HyCompaniesServiceTable::TYPE => HyCompaniesServiceModel::TypeChan_pin,
            HyCompaniesServiceTable::IS_SALE => 1,
        ])->count();

        new HyCompaniesTable()->where([
            HyCompaniesTable::ID => $id
        ])->update([
            HyCompaniesTable::SALE_NUM => $count
        ]);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发布服务失败')]
    public function publish(HyCompaniesServiceProto $request): Success
    {
        $publish = $request->getIsPub() ?: 0;
        $id = $request->getId();

        if (empty($id)) {
            throw new AppException('请选择要发布的服务');
        }

        $res = new HyCompaniesServiceTable()->where([
            [HyCompaniesServiceTable::ID, '=', $id]
        ])->update([
            HyCompaniesServiceTable::IS_PUB => $publish
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取厂家服务列表数据失败')]
    public function lists(HyCompaniesServiceProto $request): HyCompaniesServiceListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $type = $request->getType(); // 默认查询全部
        $excludeTypes = $request->getExcludeTypes(); // 需要排除的类型
        $extStr = $request->getExtStr(); // 模糊关键字查询
        $serviceArea = $request->getServiceArea();
        $userId = $request->getUserId();
        $productCategory = $request->getProductCategory();
        $productType = $request->getProductType();
        $servicePerson = $request->getServicePerson();
        $companiesId = $request->getCompaniesId();
        $companiesType = $request->getCompaniesType();// 查询企业类型

        if (empty($companiesType)) {
            $companiesType = 1;
        }


        $where = [];

        if ($type) {
            $where[] = [HyCompaniesServiceTable::TYPE, "=", $type];
        }
        if ($excludeTypes) {
            $arr = explode(',', $excludeTypes);
            if ($arr) {
                $excludeTypesArr = [];
                foreach ($arr as $item) {
                    $excludeTypesArr[] = HyCompaniesServiceTypeEnum::name($item);
                }
                $where[] = [HyCompaniesServiceTable::TYPE, "not in", $excludeTypesArr];
            }
        }

        if ($userId) {
            $where[] = [HyCompaniesServiceTable::USER_ID, "=", $userId];
        } else {
            $where[] = [HyCompaniesServiceTable::IS_PUB, '=', 1];
        }

        if ($productCategory) {
            $where[] = [HyCompaniesServiceTable::PRODUCT_CATEGORY, "=", $productCategory];
        }
        if ($productType) {
            $where[] = [HyCompaniesServiceTable::PRODUCT_TYPE, "=", $productType];
        }
        if ($servicePerson) {
            $where[] = [HyCompaniesServiceTable::SERVICE_PERSON, "like", "%$servicePerson%"];
        }

        if ($userId) {
            $order = [HyCompaniesServiceTable::UPDATED_AT => "ASC"];
        } else {
            $order = [HyCompaniesServiceTable::CREATED_AT => "asc"];
        }

        if ($companiesId) {
            $where[] = [HyCompaniesServiceTable::COMPANIES_ID, "=", $companiesId];
        }


        $query = new HyCompaniesServiceTable()
            ->field([
                HyCompaniesServiceTable::FIELD_ALL,
                HyCompaniesTable::ID,
                HyCompaniesTable::NAME,
                HyCompaniesTable::LOGO,
                HyCompaniesTable::PHONE,
                HyCompaniesTable::CONTACT_PERSON,
                HyCompaniesTable::TYPE,
            ])
            ->order($order)
            ->join(HyCompaniesTable::TABLE_NAME, HyCompaniesTable::ID, HyCompaniesServiceTable::COMPANIES_ID)
            ->where($where)
            ->addWhere(HyCompaniesTable::TYPE, $companiesType)
            ->page($page, $size);


        if ($extStr) {
            switch ($type) {
                case HyCompaniesServiceModel::TypeChan_pin:
                    $query->addWhereRaw(
                        " and (hy_companies.name like ? or hy_companies_service.product_name like ? or hy_companies_service.service_desc like ?)",
                        ["%$extStr%", "%$extStr%", "%$extStr%", "%$extStr%"]
                    );

                    break;
                default:
                    $query->addWhereRaw(
                        " and (hy_companies_service.description like ? or hy_companies.name like ? or hy_companies_service.product_name like ? or hy_companies_service.service_desc like ?)",
                        ["%$extStr%", "%$extStr%", "%$extStr%", "%$extStr%"]
                    );

                    break;
            }

        }


        if ($serviceArea) {
            // 获取城市
            $serviceAreaArr = array_filter(explode(",", $serviceArea));
            $serviceAreaArr = array_unique($serviceAreaArr);
            $conditions = [];
            foreach ($serviceAreaArr as $ignored) {
                if ($ignored) {
                    $conditions[] = " FIND_IN_SET(?, hy_companies_service.service_area) ";
                }
            }
            if ($conditions) {
                $whereClause = implode(' OR ', $conditions);
                $query->addWhereRaw(" and($whereClause)", $serviceAreaArr);
            }
        }


        $lists = $query->selectAll();

        $cityIdArr = [];
        $ids = [];
        foreach ($lists as $table) {
            if ($table->serviceArea) {
                $tempArr = explode(",", $table->serviceArea);
                $cityIdArr = array_merge($cityIdArr, $tempArr);
            }
            $ids[] = $table->id;
        }


        $cityTables = new CitysTable()->field([
            CitysTable::FIELD_ALL,
        ])->where([
            [CitysTable::ID, 'in', array_unique($cityIdArr)]
        ])->formatId2Array(CitysTable::ID);


        $skus = [];
        if ($type === HyCompaniesServiceTypeEnum::CHAN_PIN) {
            $skuTables = new HyProductsSkuTable()->where([
                [HyProductsSkuTable::PRODUCT_ID, 'in', $ids]
            ])->selectAll();
            foreach ($skuTables as $skuTable) {
                $productId = $skuTable->productId;
                if (!isset($skus[$productId])) {
                    $skus[$productId] = [];
                }
                if (empty($skuTable->price)) {
                    $skuTable->price = 0;
                }
                $skus[$productId][] = HyProductsSkuModel::formatItem($skuTable);
            }
        }


        $protoLists = [];
        foreach ($lists as $table) {
            $proto = HyCompaniesServiceModel::formatItem($table);
            // 其他自定义字段格式化
            if ($table->companiesId) {
                $companiesProto = new HyCompaniesProto();
                $companiesProto->setId($table->getByField(HyCompaniesTable::ID));
                $companiesProto->setName($table->getByField(HyCompaniesTable::NAME, ''));
                $companiesProto->setLogo($table->getByField(HyCompaniesTable::LOGO, ''));
                $companiesProto->setContactPerson($table->getByField(HyCompaniesTable::CONTACT_PERSON, ''));
                $companiesProto->setPhone($table->getByField(HyCompaniesTable::PHONE, ''));
                $companiesProto->setType($table->getByField(HyCompaniesTable::TYPE));
                $proto->setCompanies($companiesProto);
            }
            if ($table->serviceArea) {
                $cityIds = explode(",", $table->serviceArea);

                $cityNames = [];
                foreach ($cityIds as $cityId) {
                    if (isset($cityTables[$cityId])) {
                        $cityNames[] = $cityTables[$cityId]->name;
                    }
                }
                $proto->setServiceArea(implode(",", $cityNames));
                $proto->setServiceAreaIds($table->serviceArea);
            }


            if (isset($skus[$table->id])) {
                $proto->setSkus($skus[$table->id]);
            }


            if ($table->type === HyCompaniesServiceModel::TypeXin_wen && $table->commentNum === 0) {
                Event::emit('count.companiesService.commentNum', [
                    'productId' => $table->id,
                ]);
            }


            $protoLists[] = $proto;
        }

        $ret = new HyCompaniesServiceListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(HyCompaniesServiceProto $request): HyCompaniesServiceProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new HyCompaniesServiceTable()->where([
            HyCompaniesServiceTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }
        // 浏览器+1 ；
        new HyCompaniesServiceTable()->where([
            HyCompaniesServiceTable::ID => $id,
        ])->update([
            HyCompaniesServiceTable::VIEWS => Db::incr(HyCompaniesServiceTable::VIEWS),
        ]);

        $companiesTable = new HyCompaniesTable()->where([
            HyCompaniesTable::ID => $table->companiesId,
        ])->selectOne();


        $cityIdArr = [];
        if ($table->serviceArea) {
            $tempArr = explode(",", $table->serviceArea);
            $cityIdArr = array_merge($cityIdArr, $tempArr);
        }


        $cityTables = new CitysTable()->field([
            CitysTable::FIELD_ALL,
        ])->where([
            [CitysTable::ID, 'in', array_unique($cityIdArr)]
        ])->formatId2Array(CitysTable::ID);


        $proto = HyCompaniesServiceModel::formatItem($table);
        $proto->setCompanies(HyCompaniesModel::formatItem($companiesTable));


        if ($table->serviceArea) {
            $cityIds = explode(",", $table->serviceArea);

            $cityNames = [];
            foreach ($cityIds as $cityId) {
                if (isset($cityTables[$cityId])) {
                    $cityNames[] = $cityTables[$cityId]->name;
                }
            }
            $proto->setServiceAreaIds($table->serviceArea);
            $proto->setServiceArea(implode(",", $cityNames));
        }

        $skuTables = new HyProductsSkuTable()->where([
            [HyProductsSkuTable::PRODUCT_ID, '=', $table->id]
        ])->selectAll();
        $skuProto = [];
        foreach ($skuTables as $skuTable) {
            $skuProto[] = HyProductsSkuModel::formatItem($skuTable);
        }
        $proto->setSkus($skuProto);

        // 异步增加曝光量
        Server::task([__CLASS__, 'addSkuView'], [
            'id' => $id
        ]);

        return $proto;
    }

    /**
     * 增加曝光
     * @param array $data
     * @return void
     * @throws Throwable
     */
    public static function addSkuView(array $data): void
    {
        $id = $data['id'];

        new HyProductsSkuTable()->where([
            [HyProductsSkuTable::PRODUCT_ID, '=', $id]
        ])->update([
            HyProductsSkuTable::VIEW => Db::incr(HyProductsSkuTable::VIEW)
        ]);

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除厂家大厅表,厂家提供那些服务失败')]
    public function delete(HyCompaniesServiceProto $request): Success
    {
        $id = $request->getId();
        $userId = $request->getUserId();
        if (empty($id) || empty($userId)) {
            throw new AppException("参数错误");
        }

        $res = new HyCompaniesServiceTable()->where([
            HyCompaniesServiceTable::ID => $id,
            HyCompaniesServiceTable::USER_ID => $userId,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(errorTitle: "执行导出任务失败")]
    public function export(HyCompaniesServiceProto $request): HyCompaniesServiceProto
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('参数错误');
        }

        $companiesId = new HyCompaniesTable()->where([
            HyCompaniesTable::USER_ID => $userId,
            HyCompaniesTable::TYPE => 1,
        ])->selectField(HyCompaniesTable::ID);
        if (empty($companiesId)) {
            throw new AppException('参数错误');
        }

        $saveDir = PUBLIC_DIR . 'export/';
        if (!is_dir($saveDir)) {
            mkdir($saveDir, 0777, true);
        }
        $fileName = '产品列表' . date('YmdHis') . '.xlsx';
        $filePath = $saveDir . $fileName;
        $taskId = ExportProductService::export([
            'filePath' => $filePath,
            'companiesId' => $companiesId
        ]);

        $msg = new HyCompaniesServiceProto();
        $msg->setProductName($taskId);
        $msg->setExtStr(Func::getHost() . '/' . str_replace(PUBLIC_DIR, '', $filePath));
        return $msg;
    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(errorTitle: "获取导出进度失败")]
    public function getExportProgress(HyCompaniesServiceProto $request): HyCompaniesServiceProto
    {
        $taskId = $request->getProductName();
        if (empty($taskId)) {
            throw new AppException('任务ID不能为空');
        }

        $progress = ExportProductService::getProgress($taskId);
        if ($progress === null) {
            throw new AppException('任务不存在或已过期');
        }

        $msg = new HyCompaniesServiceProto();
        // -1 表示执行成功了
        $msg->setExtInt($progress >= 100 ? -1 : $progress);
        return $msg;
    }

}