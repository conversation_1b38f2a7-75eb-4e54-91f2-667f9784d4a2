<?php

namespace App\Controller\Api\Dt;


use App\Model\HyCompaniesModel;
use Generate\Tables\Datas\HyCompaniesTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\ClinicFranchisesModel;
use Generate\Tables\Datas\ClinicFranchisesTable;
use Generate\Tables\Datas\CitysTable;
use Protobuf\Datas\ClinicFranchises\ClinicFranchisesProto;
use Protobuf\Datas\ClinicFranchises\ClinicFranchisesListsProto;
use Throwable;


/*
* 门诊加盟表
*/

#[Router(method: 'POST')]
class ClinicFranchisesApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存门诊加盟表失败')]
    public function save(ClinicFranchisesProto $request): ClinicFranchisesProto
    {
        $table = ClinicFranchisesModel::request($request);

        if (empty($table->hyCompaniesId)) {
            throw new AppException('请输入门诊ID');
        }
        if (empty($table->userId)) {
            throw new AppException('请输入用户ID');
        }
        if (empty($table->clinicName)) {
            throw new AppException('请输入门诊名称');
        }
        if (empty($table->contactPerson)) {
            throw new AppException('请输入联系人');
        }
        if (empty($table->contactPhone)) {
            throw new AppException('请输入联系电话');
        }
        if (empty($table->targetArea)) {
            throw new AppException('请输入区域要求');
        }
        if (empty($table->description)) {
            throw new AppException('请输入门诊介绍');
        }
        $table->status = 1;
        $table->isPub = 0;

        $table->save();

        HyCompaniesModel::updateContactPerson($table->hyCompaniesId, $table->contactPerson);

        $proto = ClinicFranchisesModel::formatItem($table);
        $proto->setId($table->id);
        return $proto;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取门诊加盟表列表数据失败')]
    public function lists(ClinicFranchisesProto $request): ClinicFranchisesListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $queryCityIds = $request->getCityIds();
        $userId = $request->getUserId();
        $clinicName = $request->getClinicName();

        $where = [];

        if ($queryCityIds) {
            $queryCityIdsArr = [];
            foreach ($queryCityIds as $cityId) {
                $queryCityIdsArr[] = $cityId;
            }
            $where[] = [ClinicFranchisesTable::TARGET_AREA, 'in', $queryCityIdsArr];
        }

        if($userId){
            $where[] = [ClinicFranchisesTable::USER_ID, '=', $userId];
        }
        if($clinicName){
            $where[] = [ClinicFranchisesTable::CLINIC_NAME, 'like', "%$clinicName%"];
        }

        $order = [ClinicFranchisesTable::PRI_KEY => "desc"];
        $clinicFranchisesTable = new ClinicFranchisesTable();
        $lists = $clinicFranchisesTable->order($order)->where($where)->page($page, $size)->selectAll();

        // 收集所有的城市ID
        $allCityIds = [];
        $hyCompaniesIds = [];
        foreach ($lists as $table) {
            $hyCompaniesIds[] = $table->hyCompaniesId;
            if (!empty($table->targetArea)) {
                $cityIds = explode(',', $table->targetArea);
                foreach ($cityIds as $cityId) {
                    if (!empty($cityId)) {
                        $allCityIds[] = (int)$cityId;
                    }
                }
            }
        }

        $hyCompaniesArr = new HyCompaniesTable()->field([
            HyCompaniesTable::FIELD_ALL,
        ])->where([
            [HyCompaniesTable::ID, 'in', array_unique($hyCompaniesIds)]
        ])->formatId2Array(HyCompaniesTable::ID);

        // 查询城市信息
        $cityArr = [];
        if (!empty($allCityIds)) {
            $cityArr = new CitysTable()->where([
                [CitysTable::ID, 'in', array_unique($allCityIds)]
            ])->formatId2Name(CitysTable::ID, CitysTable::NAME);
        }

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = ClinicFranchisesModel::formatItem($table);

            // 设置城市信息
            if (!empty($table->targetArea)) {
                $cityIds = explode(',', $table->targetArea);
                $cityNames = [];
                $cityIdsArray = [];

                foreach ($cityIds as $cityId) {
                    if (!empty($cityId)) {
                        $cityIdsArray[] = (int)$cityId;
                        if (isset($cityArr[(int)$cityId])) {
                            $cityNames[] = $cityArr[(int)$cityId];
                        }
                    }
                }

                $proto->setCityIds($cityIdsArray);
                $proto->setCityName($cityNames);
            }

            if (isset($hyCompaniesArr[$table->hyCompaniesId])) {
                $proto->setCompanies(\Generate\Models\Datas\HyCompaniesModel::formatItem($hyCompaniesArr[$table->hyCompaniesId]));
            }

            $protoLists[] = $proto;
        }

        $ret = new ClinicFranchisesListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发布')]
    public function pub(ClinicFranchisesProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new ClinicFranchisesTable()->where([
            ClinicFranchisesTable::ID => $id,
        ])->update([
            ClinicFranchisesTable::IS_PUB => 1,
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看门诊加盟表详情失败')]
    public function detail(ClinicFranchisesProto $request): ClinicFranchisesProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new ClinicFranchisesTable()->where([
            ClinicFranchisesTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        $proto = ClinicFranchisesModel::formatItem($table);

        // 设置城市信息
        if (!empty($table->targetArea)) {
            $cityIds = explode(',', $table->targetArea);
            $cityIdsArray = [];

            foreach ($cityIds as $cityId) {
                if (!empty($cityId)) {
                    $cityIdsArray[] = (int)$cityId;
                }
            }

            // 查询城市信息
            if (!empty($cityIdsArray)) {
                $cityArr = new CitysTable()->where([
                    [CitysTable::ID, 'in', $cityIdsArray]
                ])->formatId2Name(CitysTable::ID, CitysTable::NAME);

                $cityNames = [];
                foreach ($cityIdsArray as $cityId) {
                    if (isset($cityArr[$cityId])) {
                        $cityNames[] = $cityArr[$cityId];
                    }
                }

                $proto->setCityIds($cityIdsArray);
                $proto->setCityName($cityNames);
            }
        }

        return $proto;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除门诊加盟表失败')]
    public function delete(ClinicFranchisesProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new ClinicFranchisesTable()->where([
            ClinicFranchisesTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}