<?php

namespace App\Controller\Api\Dt;


use Generate\Tables\Datas\CitysTable;
use <PERSON><PERSON>ib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\ClinicTransfersModel;
use Generate\Tables\Datas\ClinicTransfersTable;
use Protobuf\Datas\ClinicTransfers\ClinicTransfersProto;
use Protobuf\Datas\ClinicTransfers\ClinicTransfersListsProto;
use Throwable;


/*
* 门诊转让表
*/

#[Router(method: 'POST')]
class ClinicTransfersApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存门诊转让表失败')]
    public function save(ClinicTransfersProto $request): ClinicTransfersProto
    {
        $table = ClinicTransfersModel::request($request);


        if (empty($table->userId)) {
            throw new AppException('请输入用户ID');
        }
//        if (empty($table->clinicType)) {
//            throw new AppException('请输入门诊类型');
//        }
        if (empty($table->clinicAddress)) {
            throw new AppException('请输入门诊地址');
        }
        if (empty($table->contactPerson)) {
            throw new AppException('请输入联系人');
        }
        if (empty($table->contactPhone)) {
            throw new AppException('请输入联系电话');
        }
        if (empty($table->area)) {
            throw new AppException('请输入门诊面积(平方米)');
        }
        if (empty($table->chairCount)) {
            throw new AppException('请输入椅位数量');
        }
        if (empty($table->description)) {
            throw new AppException('请输入门诊介绍');
        }
        if (empty($table->clinicImages)) {
            throw new AppException('请输入门诊图片URLs');
        }
        if (empty($table->status)) {
            throw new AppException('请输入状态：0-已转让，1-转让中');
        }
        $table->isPub = 0;

        $table->save();


        return ClinicTransfersModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取门诊转让表列表数据失败')]
    public function lists(ClinicTransfersProto $request): ClinicTransfersListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $clinicAddress = $request->getCityIds();
        // 面积
        $areaStr = $request->getQueryAreaStr();
        // 椅位数量
        $countStr = $request->getQueryCountStr();
        $userId = $request->getUserId();
        $description = $request->getDescription();

        $clinicAddressArr = [];
        if ($clinicAddress) {
            foreach ($clinicAddress as $id) {
                $clinicAddressArr[] = $id;
            }
        }


        $where = [];
        $where[] = [ClinicTransfersTable::IS_PUB, '=', 1];
        if ($clinicAddress) {
            $where[] = [ClinicTransfersTable::CLINIC_ADDRESS, 'in', $clinicAddressArr];
        }
        if($userId){
            $where[] = [ClinicTransfersTable::USER_ID, '=', $userId];
        }
        if($description){
            $where[] = [ClinicTransfersTable::DESCRIPTION, 'like', "%$description%"];
        }

        if ($areaStr && $t1 = $this->addRangeCondition(ClinicTransfersTable::AREA, $areaStr)) {
            $where[] = $t1;
        }

        if ($countStr && $t2 = $this->addRangeCondition(ClinicTransfersTable::CHAIR_COUNT, $countStr)) {
            $where[] = $t2;
        }

        $order = [ClinicTransfersTable::PRI_KEY => "desc"];
        $clinicTransfersTable = new ClinicTransfersTable();
        $lists = $clinicTransfersTable->order($order)->where($where)->page($page, $size)->selectAll();


        $cityIds = [];
        foreach ($lists as $table) {
            $cityIds[] = $table->clinicAddress;
        }

        $cityArr = new CitysTable()->where([
            [CitysTable::ID, 'in', $cityIds]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);


        $protoLists = [];
        foreach ($lists as $table) {
            $proto = ClinicTransfersModel::formatItem($table);
            // 其他自定义字段格式化

            if (isset($cityArr[$table->clinicAddress])) {
                $proto->setCityName($cityArr[$table->clinicAddress]);
            }

            $protoLists[] = $proto;
        }

        $ret = new ClinicTransfersListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看门诊转让表详情失败')]
    public function detail(ClinicTransfersProto $request): ClinicTransfersProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new ClinicTransfersTable()->where([
            ClinicTransfersTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        $city = new CitysTable()->where([
            [CitysTable::ID, '=', $table->clinicAddress]
        ])->selectOne();

        $proto = ClinicTransfersModel::formatItem($table);
        $proto->setCityName($city->name);
        return $proto;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发布')]
    public function pub(ClinicTransfersProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new ClinicTransfersTable()->where([
            ClinicTransfersTable::ID => $id,
        ])->update([
            ClinicTransfersTable::IS_PUB => 1,
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除门诊转让表失败')]
    public function delete(ClinicTransfersProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new ClinicTransfersTable()->where([
            ClinicTransfersTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * 添加范围条件
     * 如果传入一个数字，使用大于等于查询
     * 如果传入两个数字（用逗号分隔），使用between查询
     *
     * @param string $field 字段名
     * @param string $valueStr 值字符串，格式：单个数字或两个数字用逗号分隔
     * @return array 更新后的查询条件数组
     */
    private function addRangeCondition(string $field, string $valueStr): array
    {
        $valueArr = explode(",", $valueStr);
        if (count($valueArr) == 2) {
            $valueArr[0] = (int)$valueArr[0];
            $valueArr[1] = (int)$valueArr[1];
            return [$field, 'between', $valueArr];
        } elseif (count($valueArr) == 1) {
            $value = (int)$valueArr[0];
            return [$field, '>=', $value];
        }
        return [];
    }
}