<?php

namespace App\Controller\Api\Jobs;

use Generate\Models\Datas\ZpJobsCollectModel;
use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Generate\Tables\Datas\ZpJobsCollectTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpResumeTable;
use Swlib\Controller\AbstractController;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Success;
use Protobuf\Datas\ZpJobs\ZpJobsListsProto;
use Protobuf\Datas\ZpJobs\ZpJobsProto;
use Protobuf\Datas\ZpJobsCollect\ZpJobsCollectProto;
use Protobuf\Datas\ZpJobsCollect\ZpJobsCollectTypeEnum;
use Protobuf\Datas\ZpResume\ZpResumeListsProto;
use Protobuf\Datas\ZpResume\ZpResumeProto;
use Throwable;

#[Router(method: 'POST')]
class CollectApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '职位收藏失败')]
    public function add(ZpJobsCollectProto $request): Success
    {
        $userId = $request->getUserId();
        $jobsId = $request->getJobsId();
        $type = $request->getType();
        if (!$type) {
            throw new AppException('请选择收藏类型');
        }
        $type = ZpJobsCollectTypeEnum::name($type);
        if (!isset(ZpJobsCollectModel::TypeTextMaps[strtolower($type)])) {
            throw new AppException('收藏类型错误');
        }
        $type = ZpJobsCollectTypeEnum::value($type);

        if (!$userId) {
            throw new AppException('请登录');
        }
        if (!$jobsId) {
            throw new AppException('请选择职位');
        }


        $collect = new ZpJobsCollectTable()->where([
            [ZpJobsCollectTable::JOBS_ID, '=', $jobsId],
            [ZpJobsCollectTable::USER_ID, '=', $userId],
            [ZpJobsCollectTable::TYPE, '=', $type],
        ])->selectOne();

        if (!$collect) {
            new ZpJobsCollectTable()->insert([
                ZpJobsCollectTable::JOBS_ID => $jobsId,
                ZpJobsCollectTable::USER_ID => $userId,
                ZpJobsCollectTable::TYPE => $type,
                ZpJobsCollectTable::TIME => time(),
            ]);
        }


        if ($type === ZpJobsCollectTypeEnum::RESUME) {
            $companiesName = new ZpCompaniesTable()->where([
                ZpCompaniesTable::USER_ID => $userId,
            ])->selectField(ZpCompaniesTable::NAME);
            $content = "$companiesName 收藏了您的简历";
            $targetType = 'zp_resume';
            $agentId = new ZpResumeTable()->where([
                ZpResumeTable::ID => $jobsId,
            ])->selectField(ZpResumeTable::USER_ID);
        } else {
            $nickname = new UserTable()->where([
                UserTable::ID => $userId,
            ])->selectField(UserTable::NICKNAME);
            $content = "$nickname 收藏了您的职位";
            $targetType = 'zp_jobs';
            $agentId = new ZpJobsTable()->where([
                ZpJobsTable::ID => $jobsId,
            ])->selectField(ZpJobsTable::USER_ID);
        }

        Event::emit('MessageSendEvent', [
            'agentId' => $agentId,
            'targetType' => $targetType,
            'targetId' => $jobsId,
            'userId' => $userId,
            'content' => $content,
            'type' => 'text',
        ]);

        $success = new Success();
        $success->setSuccess(true);

        return $success;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '取消职位收藏失败')]
    public function cancel(ZpJobsCollectProto $request): Success
    {
        $userId = $request->getUserId();
        $jobsId = $request->getJobsId() ?: $request->getId();
        if (!$userId) {
            throw new AppException('请登录');
        }
        if (!$jobsId) {
            throw new AppException('请选择职位');
        }


        (new ZpJobsCollectTable)->where([
            [ZpJobsCollectTable::JOBS_ID, '=', $jobsId],
            [ZpJobsCollectTable::USER_ID, '=', $userId],
        ])->delete();


        $success = new Success();
        $success->setSuccess(true);

        return $success;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取是否收藏失败')]
    public function exists(ZpJobsCollectProto $request): Success
    {
        $jobId = $request->getJobsId();
        $userId = $request->getUserId();
        if (!$userId) {
            throw new AppException('请登录');
        }
        $type = $request->getType();
        if (!$type) {
            throw new AppException('请选择收藏类型');
        }
        $type = ZpJobsCollectTypeEnum::name($type);

        if (!isset(ZpJobsCollectModel::TypeTextMaps[strtolower($type)])) {
            throw new AppException('收藏类型错误');
        }

        $type = ZpJobsCollectTypeEnum::value($type);

        if (!$jobId) {
            throw new AppException('请选择职位');
        }

        $find = (new ZpJobsCollectTable)->where([
            [ZpJobsCollectTable::JOBS_ID, '=', $jobId],
            [ZpJobsCollectTable::USER_ID, '=', $userId],
            [ZpJobsCollectTable::TYPE, '=', $type],
        ])->selectOne();


        $success = new Success();
        $success->setSuccess((bool)$find);

        return $success;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '职位收藏列表失败')]
    public function jobLists(ZpJobsCollectProto $request): ZpJobsListsProto
    {
        $userId = $request->getUserId();
        $page = $request->getQueryPageNo();
        $size = $request->getQueryPageSize();

        $jobsId = new ZpJobsCollectTable()->page($page, $size)->where([
            [ZpJobsCollectTable::USER_ID, '=', $userId],
            [ZpJobsCollectTable::TYPE, '=', ZpJobsCollectModel::TypeJob],
        ])->getArrayByField(ZpJobsCollectTable::JOBS_ID);

        $nodes = [];
        if ($jobsId) {

            $lists = new ZpJobsTable()->join(ZpCompaniesTable::TABLE_NAME, ZpJobsTable::COMPANY_ID, ZpCompaniesTable::ID)
                ->order([
                    ZpJobsTable::ID => 'desc'
                ])
                ->field([
                    ZpJobsTable::FIELD_ALL,
                    ZpCompaniesTable::NAME
                ])
                ->where([
                    [ZpJobsTable::ID, 'in', $jobsId]
                ])->selectAll();


            foreach ($lists as $i) {
                $proto = new ZpJobsProto();
                $proto->setId($i->id);
                $proto->setTitle($i->title);
                $proto->setSalaryRange($i->salaryRange);
                $proto->setCompanyIdStr($i->getByField(ZpCompaniesTable::NAME));
                $proto->setCompanyId($i->companyId);
                $nodes[] = $proto;
            }

        }

        $ret = new ZpJobsListsProto();
        $ret->setLists($nodes);
        return $ret;

    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '简历收藏列表失败')]
    public function resumeLists(ZpJobsCollectProto $request): ZpResumeListsProto
    {
        $userId = $request->getUserId();
        $page = $request->getQueryPageNo();
        $size = $request->getQueryPageSize();

        $jobsId = new ZpJobsCollectTable()->page($page, $size)->where([
            [ZpJobsCollectTable::USER_ID, '=', $userId],
            [ZpJobsCollectTable::TYPE, '=', ZpJobsCollectModel::TypeResume],
        ])->getArrayByField(ZpJobsCollectTable::JOBS_ID);


        $nodes = [];
        if ($jobsId) {
            $lists = new ZpResumeTable()->order([
                ZpResumeTable::ID => 'desc'
            ])->where([
                [ZpResumeTable::ID, 'in', $jobsId]
            ])->selectAll();


            foreach ($lists as $list) {
                $proto = new ZpResumeProto();
                $proto->setId($list->id);
                $proto->setJobIntention($list->jobIntention);
                $proto->setContactsName($list->contactsName);
                $proto->setSalaryRange($list->salaryRange);
                $proto->setContactsPhone($list->contactsPhone);
                $nodes[] = $proto;
            }
        }

        $ret = new ZpResumeListsProto();
        $ret->setLists($nodes);
        return $ret;
    }

}