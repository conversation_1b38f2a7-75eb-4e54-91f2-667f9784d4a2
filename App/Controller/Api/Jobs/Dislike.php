<?php

namespace App\Controller\Api\Jobs;


use Generate\Tables\Datas\DislikeTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpResumeTable;
use <PERSON><PERSON>ib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Protobuf\Datas\Dislike\DislikeProto;
use Protobuf\Datas\Dislike\DislikeListsProto;
use Protobuf\Datas\ZpJobs\ZpJobsListsProto;
use Protobuf\Datas\ZpJobs\ZpJobsProto;
use Protobuf\Datas\ZpResume\ZpResumeListsProto;
use Protobuf\Datas\ZpResume\ZpResumeProto;
use Throwable;


#[Router(method: 'POST')]
class Dislike extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(DislikeProto $request): Success
    {
        $data = $this->_request($request);
        $pk = DislikeTable::PRI_KEY;
        if (isset($data[$pk]) && $data[$pk]) {
            $res = new DislikeTable()->where([
                $pk => $data[$pk]
            ])->update($data);
        } else {
            $res = new DislikeTable()->insert($data);
        }

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(DislikeProto $request): DislikeListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];

        $lists = new DislikeTable()->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $protoLists[] = $this->_formatItem($table);
        }

        $ret = new DislikeListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function showDetail(DislikeProto $request): DislikeProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new DislikeTable()->where([
            DislikeTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return $this->_formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(DislikeProto $request): Success
    {
        $targetId = $request->getTargetId();
        $userId = $request->getUserId();
        if (empty($targetId)) {
            throw new AppException("参数错误");
        }

        $res = new DislikeTable()->where([
            DislikeTable::TARGET_ID => $targetId,
            DislikeTable::USER_ID => $userId,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '职位收藏列表失败')]
    public function jobLists(DislikeProto $request): ZpJobsListsProto
    {
        $userId = $request->getUserId();
        $page = $request->getQueryPageNo();
        $size = $request->getQueryPageSize();

        $jobsId = new DislikeTable()->page($page, $size)->where([
            [DislikeTable::USER_ID, '=', $userId],
            [DislikeTable::TYPE, '=', 1],
        ])->getArrayByField(DislikeTable::TARGET_ID);

        $nodes = [];
        if ($jobsId) {
            $lists = new ZpJobsTable()->join(ZpCompaniesTable::TABLE_NAME, ZpJobsTable::COMPANY_ID, ZpCompaniesTable::ID)
                ->field([
                    ZpJobsTable::FIELD_ALL,
                    ZpCompaniesTable::NAME
                ])
                ->order([
                    ZpJobsTable::ID => 'desc'
                ])
                ->where([
                    [ZpJobsTable::ID, 'in', $jobsId]
                ])->selectAll();


            foreach ($lists as $i) {
                $proto = new ZpJobsProto();
                $proto->setId($i->id);
                $proto->setSalaryRange($i->salaryRange);
                $proto->setTitle($i->title);
                $proto->setCompanyIdStr($i->getByField(ZpCompaniesTable::NAME));
                $proto->setCompanyId($i->companyId);
                $nodes[] = $proto;
            }

        }

        $ret = new ZpJobsListsProto();
        $ret->setLists($nodes);
        return $ret;

    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '简历收藏列表失败')]
    public function resumeLists(DislikeProto $request): ZpResumeListsProto
    {
        $userId = $request->getUserId();
        $page = $request->getQueryPageNo();
        $size = $request->getQueryPageSize();

        $jobsId = new DislikeTable()->page($page, $size)->where([
            [DislikeTable::USER_ID, '=', $userId],
            [DislikeTable::TYPE, '=', 2],
        ])->getArrayByField(DislikeTable::TARGET_ID);



        $nodes = [];
        if ($jobsId) {
            $lists = new ZpResumeTable()
                ->where([
                    [ZpResumeTable::ID, 'in', $jobsId]
                ])
                ->order([
                    ZpResumeTable::ID => 'desc'
                ])->selectAll();


            foreach ($lists as $list) {
                $proto = new ZpResumeProto();
                $proto->setJobIntention($list->jobIntention);
                $proto->setId($list->id);
                $proto->setContactsName($list->contactsName);
                $proto->setSalaryRange($list->salaryRange);
                $proto->setContactsPhone($list->contactsPhone);
                $nodes[] = $proto;
            }
        }

        $ret = new ZpResumeListsProto();
        $ret->setLists($nodes);
        return $ret;
    }


    /**
     * @throws Throwable
     */
    private function _request(DislikeProto $request): array
    {
        $targetId = $request->getTargetId();
        $type = $request->getType();
        $userId = $request->getUserId();

        if (empty($targetId)) {
            throw new AppException('参数错误');
        }
        if (empty($type)) {
            throw new AppException('参数错误');
        }
        if (empty($userId)) {
            throw new AppException('参数错误');
        }
        return [
            DislikeTable::TARGET_ID => $targetId,
            DislikeTable::TYPE => $type,
            DislikeTable::USER_ID => $userId,
        ];
    }

    /**
     * @throws Throwable
     */
    private function _formatItem(DislikeTable $table): DislikeProto
    {
        $proto = new DislikeProto();
        $proto->setId($table->id);
        $proto->setTargetId($table->targetId);
        $proto->setType($table->type);
        $proto->setUserId($table->userId);
        return $proto;
    }
}