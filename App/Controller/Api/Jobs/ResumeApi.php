<?php

namespace App\Controller\Api\Jobs;

use Generate\Models\Datas\ZpResumeModel;
use Generate\Tables\Datas\CitysTable;
use Generate\Tables\Datas\DislikeTable;
use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpResumeTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Success;
use Protobuf\Datas\ZpResume\ZpResumeListsProto;
use Protobuf\Datas\ZpResume\ZpResumeProto;
use Throwable;


/**
 * 用户简历
 */
#[Router(method: 'POST')]
class ResumeApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '用户填写职位求职失败')]
    public function add(ZpResumeProto $request): Success
    {
        $userId = $request->getUserId();
        $name = $request->getContactsName();
        $phone = $request->getContactsPhone();
        $area = $request->getArea();
        $qualification = $request->getQualification();
        $jobIntention = $request->getJobIntention();
        $intendedRegions = $request->getIntendedRegions();
        $fastTime = $request->getFastTime();
        $title = $request->getTitle();
        $desc = $request->getDesc();
        $jlUrl = $request->getUrl();
        $jlName = $request->getName();
        $jlSize = $request->getSize();
        $salaryRange = $request->getSalaryRange();
        $id = $request->getId();
        $gender = $request->getGender();
        $age = $request->getAge();

        if (empty($userId)) {
            throw new AppException('请登录');
        }

        if (empty($name)) {
            throw new AppException('请填写联系人姓名');
        }
        if (empty($phone)) {
            throw new AppException('请填写联系人手机号');
        }
//        if (empty($area)) {
//            throw new AppException('请选择地区');
//        }
//        if (empty($qualification)) {
//            throw new AppException('请选择学历');
//        }
        if (empty($jobIntention)) {
            throw new AppException('请选择求职意向');
        }
        if (empty($intendedRegions)) {
            throw new AppException('请选择意向地区');
        }
        if (empty($fastTime)) {
            throw new AppException('请选择最快到岗时间');
        }
//        if (empty($title)) {
//            throw new AppException('请填写职称');
//        }
        if (empty($desc)) {
            throw new AppException('请填写个人简介');
        }
//        if (empty($jlUrl)) {
//            throw new AppException('请上传简历');
//        }

        $intendedRegionsArr = [];
        foreach ($intendedRegions as $intendedRegionsId) {
            $intendedRegionsArr[] = $intendedRegionsId;
        }


        $data = [
            ZpResumeTable::USER_ID => $userId,
            ZpResumeTable::CONTACTS_NAME => $name,
            ZpResumeTable::CONTACTS_PHONE => $phone,
            ZpResumeTable::AREA => $area,
            ZpResumeTable::QUALIFICATION => $qualification,
            ZpResumeTable::JOB_INTENTION => $jobIntention,
            ZpResumeTable::INTENDED_REGIONS => json_encode($intendedRegionsArr),
            ZpResumeTable::FAST_TIME => $fastTime,
            ZpResumeTable::TITLE => $title,
            ZpResumeTable::DESC => $desc,
            ZpResumeTable::URL => $jlUrl,
            ZpResumeTable::NAME => $jlName,
            ZpResumeTable::SIZE => $jlSize,
            ZpResumeTable::GENDER => $gender,
            ZpResumeTable::AGE => $age,
            ZpResumeTable::SALARY_RANGE => $salaryRange,
            ZpResumeTable::TIME => time(),
        ];

        $find = new ZpResumeTable()->where([
            ZpResumeTable::ID => $id,
            ZpResumeTable::USER_ID => $userId,
        ])->selectOne();

        if ($find) {
            $ret = new ZpResumeTable()->where([
                ZpResumeTable::ID => $id,
                ZpResumeTable::USER_ID => $userId,
            ])->update($data);
        } else {
            $ret = new ZpResumeTable()->insert($data);
        }


        $msg = new Success();
        $msg->setSuccess((bool)$ret);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取求职用户列表失败')]
    public function search(ZpResumeProto $request): ZpResumeListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $userId = $request->getUserId();
        $intendedRegions = $request->getIntendedRegions();
        $name = $request->getJobIntention();
        $dislikeUserId = $request->getDislikeUserId();
        $loginUserId = $request->getLoginUserId();

        $where = [];

        if ($userId) {
            $where[] = [ZpResumeTable::USER_ID, '=', $userId];
        } else if ($dislikeUserId) {
            if ($loginUserId) {
                $where[] = [ZpResumeTable::USER_ID, '<>', $loginUserId];
            }
            // 不查询不喜欢
            $jobIds = new DislikeTable()->where([
                DislikeTable::USER_ID => $dislikeUserId,
                DislikeTable::TYPE => 2,
            ])->getArrayByField(DislikeTable::TARGET_ID);
            $where[] = [ZpResumeTable::ID, 'not in', $jobIds];

            if ($intendedRegions) {
                $ids = [];
                foreach ($intendedRegions as $intendedRegionId) {
                    $ids[] = $intendedRegionId;
                }
                $where[] = [ZpResumeTable::INTENDED_REGIONS, 'json_contains', $ids];
            }


            if ($name) {
                $where[] = [ZpResumeTable::JOB_INTENTION, 'like', "%$name%"];
            }
        }


        $zpResumeTable = new ZpResumeTable();
        $lists = $zpResumeTable->order([
            ZpResumeTable::ID => 'desc'
        ])->where($where)->page($page, $size)->selectAll();
        $userIds = $zpResumeTable->getArrayByField(ZpResumeTable::USER_ID);
        $intendedRegions = $zpResumeTable->getArrayByField(ZpResumeTable::INTENDED_REGIONS);
        $areaIds = [];
        foreach ($intendedRegions as $area) {
            if ($area) {
                $tempArr = json_decode($area, true);
                array_push($areaIds, ...$tempArr);
            }

        }
        $areaIds = array_unique($areaIds);
        $cityId2Name = new CitysTable()->where([
            [CitysTable::ID, 'in', $areaIds]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);


        $users = new UserTable()->field([
            UserTable::ID,
            UserTable::HEAD,
            UserTable::LAST_ACTIVE_TIME,
        ])->where([
            [UserTable::ID, 'in', $userIds]
        ])->formatId2Array(UserTable::ID);


        $nodes = [];
        foreach ($lists as $list) {
            $proto = new ZpResumeProto();

            if ($list->userId) {
                $proto->setContactsHead($users[$list->userId]->head ?: '');
                $proto->setLastActiveTime($users[$list->userId]->lastActiveTime ?: 0);
            }

            if ($area = $list->intendedRegions) {
                $proto->setIntendedRegionsStr(array_filter(array_map(function ($item) use ($cityId2Name) {
                    return $cityId2Name[$item] ?? null;
                }, $area)));
            }


            $proto->setId($list->id);
            $proto->setJobIntention($list->jobIntention);
            $proto->setContactsName($list->contactsName);
            $proto->setContactsPhone($list->contactsPhone);
            $proto->setSalaryRange($list->salaryRange);
            $proto->setDesc($list->desc);
            $proto->setTitle($list->title);
            $proto->setTimeStr(date('Y-m-d', $list->time));
            $nodes[] = $proto;
        }


        $ret = new ZpResumeListsProto();
        $ret->setLists($nodes);
        return $ret;

    }

    /**
     * @throws Throwable
     * @throws AppException
     */
    #[Router(errorTitle: '获取求职用户详情失败')]
    public function detail(ZpResumeProto $request): ZpResumeProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException('请选择用户');
        }

        $detail = new ZpResumeTable()->where([ZpResumeTable::ID => $id])->selectOne();
        if (empty($detail)) {
            throw new AppException('用户不存在');
        }

        $areaId = $detail->area;
        $intendedRegionsId = $detail->intendedRegions;

        $cityIds = [$areaId];
        foreach ($intendedRegionsId as $id) {
            $cityIds[] = $id;
        }
        $cityId2name = new CitysTable()->where([
            [CitysTable::ID, 'in', $cityIds]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);


        $intendedRegionsNameArr = [];
        foreach ($intendedRegionsId as $id) {
            $intendedRegionsNameArr[] = $cityId2name[$id] ?? '';
        }

        $proto = new ZpResumeProto();
        $proto->setId($detail->id);
        $proto->setUserId($detail->userId);
        $proto->setUrl($detail->url);
        $proto->setName($detail->name);
        $proto->setSize($detail->size);
        $proto->setTimeStr(date('Y-m-d H:i:s', $detail->time));
        $proto->setContactsName($detail->contactsName);
        $proto->setContactsPhone($detail->contactsPhone);
        $proto->setArea($areaId);
        $proto->setAreaStr($cityId2name[$areaId] ?? '');
        $proto->setQualification($detail->qualification);
        $proto->setJobIntention($detail->jobIntention);
        $proto->setIntendedRegions($intendedRegionsId);
        $proto->setIntendedRegionsStr($intendedRegionsNameArr);
        $proto->setFastTime($detail->fastTime);
        $proto->setTitle($detail->title);
        $proto->setDesc($detail->desc);
        $proto->setGender($detail->gender);
        $proto->setAge($detail->age ?: 0);
        $proto->setSalaryRange($detail->salaryRange);


        return $proto;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除简历失败')]
    public function delete(ZpResumeProto $request): Success
    {
        $id = $request->getId();
        $userId = $request->getUserId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }
        if (empty($userId)) {
            throw new AppException("请登录");
        }

        $res = new ZpResumeTable()->where([
            ZpResumeTable::ID => $id,
            ZpResumeTable::USER_ID => $userId,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取我的简历列表数据失败')]
    public function myLists(ZpResumeProto $request): ZpResumeListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $where = [
            ZpResumeTable::USER_ID => $userId,
        ];
        $order = [ZpResumeTable::ID => "desc"];
        $lists = new ZpResumeTable()->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            if (empty($table->age)) {
                $table->age = 0;
            }
            $proto = ZpResumeModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new ZpResumeListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

}