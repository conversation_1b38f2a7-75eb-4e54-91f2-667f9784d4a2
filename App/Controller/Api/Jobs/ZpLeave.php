<?php

namespace App\Controller\Api\Jobs;


use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpResumeTable;
use Swlib\Controller\AbstractController;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\ZpLeaveModel;
use Generate\Tables\Datas\ZpLeaveTable;
use Protobuf\Datas\ZpLeave\ZpLeaveProto;
use Protobuf\Datas\ZpLeave\ZpLeaveListsProto;
use Protobuf\Datas\ZpLeave\ZpLeaveStatusEnum;
use Throwable;


#[Router(method: 'POST')]
class ZpLeave extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存数据失败')]
    public function save(ZpLeaveProto $request): Success
    {
        $table = ZpLeaveModel::request($request);

        if (empty($table->sendUserId)) {
            throw new AppException('请输入发送者');
        }
        if (empty($table->toUserId)) {
            throw new AppException('请输入接收者');
        }
        if (empty($table->content)) {
            throw new AppException('请输入内容');
        }
        if (empty($table->targetId)) {
            throw new AppException('请输入目标ID');
        }
        if (empty($table->targetType)) {
            throw new AppException('请输入留言类型');
        }

        Event::emit('LeaveAddEvent', [
            'send_user_id' => $table->sendUserId,
            'to_user_id' => $table->toUserId,
            'content' => $table->content,
            'target_id' => $table->targetId,
            'target_type' => $table->targetType,
            'msg_type' => $table->msgType,
        ]);

        $msg = new Success();
        $msg->setSuccess(true);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(ZpLeaveProto $request): ZpLeaveListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $toUserId = $request->getToUserId();
        $status = $request->getStatus();
        $msgType = $request->getMsgType();
        if (empty($toUserId)) {
            throw new AppException('请输入接收者');
        }


        $where = [
            ZpLeaveTable::TO_USER_ID => $toUserId,
            ZpLeaveTable::MSG_TYPE => $msgType,
        ];

        // 只查询未读
        if ($status === ZpLeaveStatusEnum::UNREAD) {
            $where[ZpLeaveTable::STATUS] = ZpLeaveModel::StatusUnread;
        }

        $order = [ZpLeaveTable::ID => "desc"];
        $zpLeaveTable = new ZpLeaveTable();
        $lists = $zpLeaveTable->setDebugSql()->order($order)->where($where)->page($page, $size)->selectAll();


        $companiesUserIds = []; //根据用户ID 查找公司
        $userIds = [];
        $jobIds = []; // 招聘职位ID
        $resumeIds = []; // 简历ID
        foreach ($lists as $table) {
            if ($table->targetType === ZpLeaveModel::TargetTypeJob) {
                // 招聘职位发布的留言
                $jobIds[] = $table->targetId;
                $companiesUserIds[] = $table->sendUserId;
            } else {
                // 简历发布留言
                $resumeIds[] = $table->targetId;
            }
            $userIds[] = $table->sendUserId;
        }

        $companiesUserId2Name = new ZpCompaniesTable()->where([
            [ZpCompaniesTable::USER_ID, 'in', $companiesUserIds],
        ])->formatId2Name(ZpCompaniesTable::USER_ID, ZpCompaniesTable::NAME);

        $userId2Array = new UserTable()->field([
            UserTable::USERNAME,
            UserTable::HEAD,
        ])->where([
            UserTable::ID => $userIds,
        ])->formatId2Array(UserTable::ID);

        $jobId2Name = new ZpJobsTable()->where([
            [ZpJobsTable::ID, 'in', $jobIds],
        ])->formatId2Name(ZpJobsTable::ID, ZpJobsTable::TITLE);

        $resumeId2Name = new ZpResumeTable()->where([
            [ZpResumeTable::ID, 'in', $resumeIds],
        ])->formatId2Name(ZpResumeTable::ID, ZpResumeTable::JOB_INTENTION);

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = ZpLeaveModel::formatItem($table);
            // 其他自定义字段格式化

            $proto->setTimeStr(date('Y-m-d H:i:s', $table->time));

            if ($table->targetType === ZpLeaveModel::TargetTypeJob) {
                // 招聘职位发布的留言
                $proto->setSendTitle($companiesUserId2Name[$table->sendUserId] ?: '');
                $proto->setTargetTitle($jobId2Name[$table->targetId] ?: '');
            } else {
                // 简历发布留言
                $proto->setSendTitle($userId2Array[$table->sendUserId]->getByField(UserTable::USERNAME, ''));
                $proto->setTargetTitle($resumeId2Name[$table->targetId] ?? '');
            }
            $proto->setAvatar($userId2Array[$table->sendUserId]->getByField(UserTable::HEAD, ''));

            $protoLists[] = $proto;
        }

        $unReadCount = new ZpLeaveTable()->where([
            ZpLeaveTable::TO_USER_ID => $toUserId,
            ZpLeaveTable::STATUS => ZpLeaveModel::StatusUnread,
        ])->count();

        $zpUnReadCount = new ZpLeaveTable()->where([
            ZpLeaveTable::TO_USER_ID => $toUserId,
            ZpLeaveTable::STATUS => ZpLeaveModel::StatusUnread,
            ZpLeaveTable::MSG_TYPE => ZpLeaveModel::MsgTypeZp,
        ])->count();

        $jjUnReadCount = new ZpLeaveTable()->where([
            ZpLeaveTable::TO_USER_ID => $toUserId,
            ZpLeaveTable::STATUS => ZpLeaveModel::StatusUnread,
            ZpLeaveTable::MSG_TYPE => ZpLeaveModel::MsgTypeJj,
        ])->count();


        $xtUnReadCount = new ZpLeaveTable()->where([
            ZpLeaveTable::TO_USER_ID => $toUserId,
            ZpLeaveTable::STATUS => ZpLeaveModel::StatusUnread,
            ZpLeaveTable::MSG_TYPE => ZpLeaveModel::MsgTypeXt,
        ])->count();

        $ret = new ZpLeaveListsProto();
        $ret->setLists($protoLists);
        $ret->setUnreadCount($unReadCount);
        $ret->setZpUnreadCount($zpUnReadCount);
        $ret->setJjUnreadCount($jjUnReadCount);
        $ret->setXtUnreadCount($xtUnReadCount);
        return $ret;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '统计未读失败')]
    public function countUnRead(ZpLeaveProto $request): ZpLeaveListsProto
    {
        $toUserId = $request->getToUserId();
        $unReadCount = new ZpLeaveTable()->where([
            ZpLeaveTable::TO_USER_ID => $toUserId,
            ZpLeaveTable::STATUS => ZpLeaveModel::StatusUnread,
        ])->count();

        $proto = new ZpLeaveListsProto();
        $proto->setUnreadCount($unReadCount);
        return $proto;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '设置已读失败')]
    public function setRead(ZpLeaveProto $request): Success
    {
        $table = ZpLeaveModel::request($request);

        if (empty($table->id)) {
            throw new AppException('请输入id');
        }
        $table->status = ZpLeaveModel::StatusRead;
        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '设置已读失败')]
    public function setAllRead(ZpLeaveProto $request): Success
    {
        $toUserId = $request->getToUserId();

        if (empty($toUserId)) {
            throw new AppException('请输入接收者ID');
        }

        $res = new ZpLeaveTable()->where([
            ZpLeaveTable::TO_USER_ID => $toUserId,
        ])->update([
            ZpLeaveTable::STATUS => ZpLeaveModel::StatusRead
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看详情失败')]
    public function detail(ZpLeaveProto $request): ZpLeaveProto
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("缺少参数");
        }

        $table = new ZpLeaveTable()->where([
            ZpLeaveTable::ID => $id,
        ])->selectOne();
        if (empty($table)) {
            throw new AppException("参数错误");
        }

        return ZpLeaveModel::formatItem($table);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(ZpLeaveProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new ZpLeaveTable()->where([
            ZpLeaveTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }
}