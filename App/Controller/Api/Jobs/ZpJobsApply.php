<?php

namespace App\Controller\Api\Jobs;


use Generate\Tables\Datas\ZpJobsApplyTable;
use <PERSON>wlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Generate\Models\Datas\ZpJobsApplyModel;
use Protobuf\Datas\ZpJobsApply\ZpJobsApplyProto;
use Throwable;


#[Router(method: 'POST')]
class ZpJobsApply extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存备注失败')]
    public function saveNotes(ZpJobsApplyProto $request): Success
    {
        $table = ZpJobsApplyModel::request($request);

        if (empty($table->id)) {
            throw new AppException('请输入id');
        }

        if (empty($table->userNotes) && empty($table->companyNotes)) {
            throw new AppException('请输入备注');
        }

        $res = $table->save();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查询备注失败')]
    public function getNotes(ZpJobsApplyProto $request): ZpJobsApplyProto
    {
        $id = $request->getId();

        $find = new ZpJobsApplyTable()->field([
            ZpJobsApplyTable::USER_NOTES,
            ZpJobsApplyTable::COMPANY_NOTES,
        ])->where([
            ZpJobsApplyTable::ID => $id,
        ])->selectOne();


        $msg = new ZpJobsApplyProto();
        if($find){
            $msg->setUserNotes($find->userNotes ?:'');
            $msg->setCompanyNotes($find->companyNotes?:'');
        }

        return $msg;
    }


}