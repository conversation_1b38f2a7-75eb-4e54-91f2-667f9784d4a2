<?php

namespace App\Controller\Api\Jobs;

use App\Model\JobCollectModel;
use App\Model\JobsApplyModel;
use App\Model\JobsModel;
use App\Model\UserModel;
use App\Model\UserSpecialityModel;
use Generate\Tables\Datas\CitysTable;
use Generate\Tables\Datas\DislikeTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\SpecialityTable;
use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Generate\Tables\Datas\ZpJobIntentionTable;
use Generate\Tables\Datas\ZpJobsApplyTable;
use Generate\Tables\Datas\ZpJobsCollectTable;
use Generate\Tables\Datas\ZpJobsLevelTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpJobsTypeTable;
use Generate\Tables\Datas\ZpJobsUserSeeTable;
use Generate\Tables\Datas\ZpJobTagTable;
use Generate\Tables\Datas\ZpResumeTable;
use Generate\Tables\Datas\ZpTagTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Protobuf\Companies\CompaniesItem;
use Protobuf\Companies\CompaniesLists;
use Protobuf\Jobs\CountByTypeItemMessage;
use Protobuf\Jobs\CountByTypeListsMessage;
use Protobuf\Jobs\Intention;
use Protobuf\Jobs\JobsApplyLists;
use Protobuf\Jobs\JobsCollectItem;
use Protobuf\Jobs\JobsCollectLists;
use Protobuf\Jobs\JobsItem;
use Protobuf\Jobs\JobsLevelItem;
use Protobuf\Jobs\JobsLevelLists;
use Protobuf\Jobs\JobsLists;
use Protobuf\Jobs\JobsListsRequest;
use Protobuf\Jobs\JobsTypeItem;
use Protobuf\Jobs\JobsTypeLists;
use Protobuf\Jobs\JobsUserSeeItemMessage;
use Protobuf\Jobs\JobsUserSeeListsMessage;
use Protobuf\Jobs\ResumeItem;
use Protobuf\Jobs\UserCounts;
use Protobuf\Datas\ZpJobs\ZpJobsListsProto;
use Protobuf\Datas\ZpJobs\ZpJobsProto;
use Protobuf\Datas\ZpJobsApply\ZpJobsApplyProto;
use Throwable;

#[Router(method: 'POST')]
class JobsApi extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位列表失败')]
    public function lists(JobsListsRequest $request): JobsLists
    {
        $keyword = $request->getKeyword();
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $companyId = $request->getCompaniesId();
        $province = $request->getProvince();
        $city = $request->getCity();
        $sortField = $request->getSortField();
        $sortType = $request->getSortType();
        $userId = $request->getUserId();
        $typeId = $request->getTypeId();


        $where = [];
        if ($companyId) {
            // 搜索某个公司的职位
            $where[] = [ZpJobsTable::COMPANY_ID, '=', $companyId];
        }
        if ($typeId) {
            // 搜索某个类型的职位
            $where[] = [ZpJobsTable::TYPE_ID, '=', $typeId];
        }
        if ($keyword) {
            // 根据关键字搜索职位
            $where[] = [ZpJobsTable::TITLE, 'like', "%$keyword%"];
        }
        if ($province) {
            // 省份搜索
            $where[] = [ZpCompaniesTable::PROVINCE, '=', $province];
        }
        if ($city) {
            // 城市搜索
            $where[] = [ZpCompaniesTable::CITY, '=', $city];
        }
        if ($sortField) {
            $sortType = $sortType ?: 'desc';
            if ($sortField == 'created_at') {
                $sortField = ZpJobsTable::CREATED_TIME;
            }
        }

        // 不查询不喜欢
        if ($userId) {
            $jobIds = new DislikeTable()->where([
                DislikeTable::USER_ID => $userId,
                DislikeTable::TYPE => 1,
            ])->getArrayByField(DislikeTable::TARGET_ID);
            $where[] = [ZpJobsTable::ID, 'not in', $jobIds];
        }


        $query = (new ZpJobsTable)->page($page, $size)->where($where);
        if ($province || $city) {
            $query->join(ZpCompaniesTable::TABLE_NAME, ZpJobsTable::COMPANY_ID, ZpCompaniesTable::ID);
        }
        if ($sortField) {
            $query->order([$sortField => $sortType]);
        } else {
            $query->order([ZpJobsTable::ID => 'desc']);
        }

        $ret = JobsModel::getJobInfo(
            jobsTables: $query->selectAll(),
            userId: $userId
        );

        $message = new JobsLists();
        $message->setLists($ret);
        return $message;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位列表失败')]
    public function listsV2(ZpJobsProto $request): ZpJobsListsProto
    {
        $title = $request->getTitle();
        $cityArr = $request->getCitys();
        $page = $request->getQueryPageNo();
        $size = $request->getQueryPageSize();
        $userId = $request->getUserId();
        $dislikeUserId = $request->getDislikeUserId();
        $loginUserId = $request->getLoginUserId();


        $zpJobsTable = new ZpJobsTable();
        $query = $zpJobsTable
            ->join(HyCompaniesTable::TABLE_NAME, ZpJobsTable::COMPANY_ID, HyCompaniesTable::ID)
            ->join(UserTable::TABLE_NAME, ZpJobsTable::USER_ID, UserTable::ID)
            ->order([
                ZpJobsTable::ID => 'desc'
            ])
            ->field([
                ZpJobsTable::FIELD_ALL,
                HyCompaniesTable::NAME,
                UserTable::LAST_ACTIVE_TIME,
            ])
            ->page($page, $size);


        if ($userId) {
            $query->addWhere(ZpJobsTable::USER_ID, $userId);
        } else if ($dislikeUserId) {
            if ($loginUserId) {
                $query->addWhere(ZpJobsTable::USER_ID, $loginUserId, '<>');
            }

            // 不查询不喜欢
            $jobIds = new DislikeTable()->where([
                DislikeTable::USER_ID => $dislikeUserId,
                DislikeTable::TYPE => 1,
            ])->getArrayByField(DislikeTable::TARGET_ID);
            $query->addWhere(ZpJobsTable::ID, $jobIds, 'not in');

            if ($cityArr) {
                $arr = [];
                foreach ($cityArr as $id) {
                    if ($id) {
                        $arr[] = $id;
                    }
                }
                if ($arr) {
                    $query->addWhere(ZpJobsTable::AREA, $arr, 'json_contains');
                }
            }

        }

        if ($title) {
            $query->addWhereRaw(" and (zp_jobs.title like ? or hy_companies.name like ?) ", ["%$title%", "%$title%"]);
        }


        $lists = $query->selectAll();
        $areas = $zpJobsTable->getArrayByField(ZpJobsTable::AREA);
        $areaIds = [];
        foreach ($areas as $area) {
            if ($area) {
                $tempArr = json_decode($area, true);
                array_push($areaIds, ...$tempArr);
            }

        }
        $areaIds = array_unique($areaIds);

        $cityId2Name = new CitysTable()->where([
            [CitysTable::ID, 'in', $areaIds]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);

        $nodes = [];
        foreach ($lists as $i) {
            $proto = new ZpJobsProto();
            $proto->setId($i->id);
            $proto->setTitle($i->title);
            $proto->setDescription($i->description);
            $proto->setAsk($i->ask);
            $proto->setSalaryRange($i->salaryRange);
            $proto->setCreatedTimeStr(date('Y-m-d', $i->createdTime));
            $proto->setCompanyId($i->companyId);
            $proto->setCompanyIdStr($i->getByField(HyCompaniesTable::NAME));
            $proto->setLastActiveTime($i->getByField(UserTable::LAST_ACTIVE_TIME, 0));

            if ($area = $i->area) {
                $proto->setAreaStr(array_filter(array_map(function ($item) use ($cityId2Name) {
                    return $cityId2Name[$item] ?? null;
                }, $area)));
            }

            $nodes[] = $proto;
        }

        $ret = new ZpJobsListsProto();
        $ret->setLists($nodes);
        return $ret;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位详情失败')]
    public function detailV2(ZpJobsProto $request): ZpJobsProto
    {
        $jobId = $request->getId();
        $loginUserId = $request->getLoginUserId();
        if (empty($jobId)) {
            throw new AppException("参数错误");
        }


        $i = new ZpJobsTable()->join(HyCompaniesTable::TABLE_NAME, ZpJobsTable::COMPANY_ID, HyCompaniesTable::ID)
            ->field([
                ZpJobsTable::FIELD_ALL,
                HyCompaniesTable::NAME
            ])
            ->where([
                ZpJobsTable::ID => $jobId
            ])->selectOne();
        if (empty($i)) {
            throw new AppException("参数错误");
        }
        $areaId = $i->area;

        $cityId2name = new CitysTable()->where([
            [CitysTable::ID, '=', $areaId]
        ])->formatId2Name(CitysTable::ID, CitysTable::NAME);

        $areaStr = [];
        foreach ($areaId as $id) {
            $areaStr[] = $cityId2name[$id] ?? '';
        }


        $proto = new ZpJobsProto();
        $proto->setId($i->id);
        $proto->setTitle($i->title);
        $proto->setSalaryRange($i->salaryRange);
        $proto->setCompanyId($i->companyId);
        $proto->setContactsName($i->contactsName);
        $proto->setContactsPhone($i->contactsPhone);
        $proto->setAsk($i->ask);
        $proto->setArea($areaId);
        $proto->setAreaStr($areaStr);
        $proto->setUserId($i->userId);
        $proto->setCompanyIdStr($i->getByField(HyCompaniesTable::NAME));
        $proto->setHasApply($loginUserId && JobsApplyModel::hasApply($jobId, $loginUserId));
        return $proto;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位列表失败')]
    public function skillMatching(JobsListsRequest $request): JobsLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }


        $whereOr = [];
        /**@var SpecialityTable $i */
        foreach (UserSpecialityModel::userLists(userId: $userId) as $i) {
            $name = $i->getByField(SpecialityTable::NAME);
            $whereOr[] = [ZpJobsTable::TITLE, 'like', "%$name%"];
            $whereOr[] = [ZpJobsTable::DESCRIPTION, 'like', "%$name%"];
            $whereOr[] = [ZpJobsTable::ASK, 'like', "%$name%"];
            $whereOr[] = [ZpTagTable::NAME, 'like', "%$name%"];
        }


        $ret = [];
        if ($whereOr) {
            $query = (new ZpJobsTable)->page($page, $size);
            $query->order([ZpJobsTable::ID => 'desc']);
            $query->join(ZpJobTagTable::TABLE_NAME, ZpJobTagTable::JOB_ID, ZpJobsTable::ID);
            $query->join(ZpTagTable::TABLE_NAME, ZpTagTable::ID, ZpJobTagTable::TAG_ID);
            $query->whereOr($whereOr);

            $ret = JobsModel::getJobInfo(
                jobsTables: $query->selectAll(),
                userId: $userId
            );
        }


        $message = new JobsLists();
        $message->setLists($ret);
        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位分类列表失败')]
    public function types(Request $request): JobsTypeLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;

        $ret = [];
        foreach ((new ZpJobsTypeTable)->page($page, $size)->selectAll() as $table) {
            $item = new JobsTypeItem();
            $item->setId($table->id);
            $item->setName($table->name);
            $ret[] = $item;
        }
        $message = new JobsTypeLists();
        $message->setLists($ret);
        return $message;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位级别列表失败')]
    public function levels(Request $request): JobsLevelLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;

        $ret = [];
        foreach ((new ZpJobsLevelTable)->page($page, $size)->selectAll() as $table) {
            $item = new JobsLevelItem();
            $item->setId($table->id);
            $item->setName($table->name);
            $ret[] = $item;
        }
        $message = new JobsLevelLists();
        $message->setLists($ret);
        return $message;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位收藏失败')]
    public function collectLists(Request $request): JobsCollectLists
    {
        $page = $request->getPage() ?: 1;
        $userId = $request->getUserId();
        if (!$userId) {
            throw new AppException('请登录');
        }

        $ret = [];
        $jobsIds = [];
        $userIds = [];

        /**@var ZpJobsCollectTable $table */
        foreach (JobCollectModel::getByUserLists($userId, $page) as $table) {
            $item = new JobsCollectItem();
            $item->setId($table->id);
            $item->setJobsId($table->jobsId);
            $item->setUserId($table->userId);
            $item->setTime(date('Y-m-d H:i:s', $table->time));
            $ret[] = $item;

            $jobsIds[] = $table->jobsId;
            $userIds[] = $table->userId;
        }


        $jobs = JobsModel::getJobList($jobsIds);
        foreach ($jobs as $job) {
            $job->setIsCollected(true);
        }

        $users = [];
        $userTables = new UserTable()->getCacheByIds($userIds, UserTable::ID);
        // 获取用户信息
        foreach ($userTables as $table) {
            $id = $table->id;
            $users[$id] = UserModel::formatUser($table);
        }


        /**@var JobsCollectItem $item */
        foreach ($ret as $item) {
            $user = $users[$item->getUserId()] ?? null;
            $job = $jobs[$item->getJobsId()] ?? null;
            $item->setUser($user);
            $item->setJobs($job);
        }


        $success = new JobsCollectLists();
        $success->setLists($ret);

        return $success;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '申请职位失败')]
    public function apply(ZpJobsApplyProto $request): Success
    {
        $userId = $request->getUserId();
        $jobsId = $request->getJobId();
        $resumeId = $request->getResumeId();
        if (!$userId) {
            throw new AppException('请登录');
        }

        if (!$jobsId) {
            throw new AppException('请选择职位');
        }

        $res = JobsApplyModel::apply($jobsId, $userId, $resumeId);

        $success = new Success();
        $success->setSuccess($res);
        return $success;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位申请列表失败')]
    public function myApply(Request $request): JobsApplyLists
    {
        $userId = $request->getUserId();
        $page = $request->getPage() ?: 1;
        $type = $request->getType() ?: 1; // 1  待面试  2： 已经面试
        $datetime = $request->getDatetime();
        if (!$userId) {
            throw new AppException('请登录');
        }

        return JobsApplyModel::applyList($userId, $page, $type, $datetime);
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取公司职位数量失败')]
    public function jobsCountByCompanies(Request $request): CompaniesLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;

        $table = new ZpCompaniesTable();
        $lists = $table->page($page, $size)->selectAll();

        $ids = $table->getArrayByField(ZpCompaniesTable::ID);

        $jobs = (new ZpJobsTable)
            ->field([
                ZpJobsTable::COMPANY_ID
            ])
            ->where([
                [ZpJobsTable::COMPANY_ID, 'in', $ids]
            ])
            ->selectAll();

        $jobCount = [];
        foreach ($jobs as $job) {
            $cid = $job->companyId;
            if (!array_key_exists($cid, $jobCount)) {
                $jobCount[$cid] = 0;
            }
            $jobCount[$cid]++;
        }


        $ret = [];
        foreach ($lists as $list) {
            $item = new CompaniesItem();
            $item->setId($list->id);
            $item->setName($list->name);
            $item->setLogo($list->logo);

            $count = $jobCount[$list->id] ?? 0;
            $item->setJobsCount($count);

            $ret[] = $item;
        }

        $msg = new CompaniesLists();
        $msg->setLists($ret);

        return $msg;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位详情失败')]
    public function getDetail(Request $request): JobsItem
    {
        $id = $request->getId();
        $userId = $request->getUserId();
        if (empty($id)) {
            throw new AppException('参数错误');
        }

        $job = new ZpJobsTable()->addWhere(ZpJobsTable::ID, $id)->selectOne();
        if (empty($job)) {
            throw new AppException('职位不存在');
        }

        new ZpJobsTable()->addWhere(ZpJobsTable::ID, $id)->update([
            ZpJobsTable::SHOW_NUM => ZpJobsTable::SHOW_NUM . "+1"
        ]);

        $isCollect = false;
        if ($userId) {
            // 写入浏览记录
            $seeId = new ZpJobsUserSeeTable()
                ->addWhere(ZpJobsUserSeeTable::USER_ID, $userId)
                ->addWhere(ZpJobsUserSeeTable::JOB_ID, $id)
                ->selectField(ZpJobsUserSeeTable::ID);
            if (empty($seeId)) {
                new ZpJobsUserSeeTable()->insert([
                    ZpJobsUserSeeTable::USER_ID => $userId,
                    ZpJobsUserSeeTable::JOB_ID => $id,
                    ZpJobsUserSeeTable::FIRST_TIME => time(),
                    ZpJobsUserSeeTable::LAST_TIME => time()
                ]);
            } else {
                new ZpJobsUserSeeTable()->addWhere(ZpJobsUserSeeTable::ID, $seeId)->update([
                    ZpJobsUserSeeTable::LAST_TIME => time(),
                    ZpJobsUserSeeTable::NUM => ZpJobsUserSeeTable::NUM . "+1",
                ]);
            }

            $isCollect = new ZpJobsCollectTable()->where([
                [ZpJobsCollectTable::USER_ID, '=', $userId],
                [ZpJobsCollectTable::JOBS_ID, '=', $id],
            ])->selectField(ZpJobsCollectTable::ID);
            $isCollect = (bool)$isCollect;

        }


        $company = new ZpCompaniesTable()
            ->addWhere(ZpCompaniesTable::ID, $job->companyId)
            ->selectOne();
        $companyMsg = new CompaniesItem();
        $companyMsg->setId($company->id);
        $companyMsg->setLogo($company->logo);
        $companyMsg->setName($company->name);
        $companyMsg->setProvince($company->province);
        $companyMsg->setCity($company->city);


        $msg = JobsModel::formatItem($job);

        if ($job->typeId) {
            $type = new ZpJobsTypeTable()
                ->addWhere(ZpJobsTypeTable::ID, $job->typeId)
                ->selectOne();
            $typeMsg = new JobsTypeItem();
            $typeMsg->setId($type->id);
            $typeMsg->setName($type->name);
            $msg->setType($typeMsg);
        }


        if ($job->levelId) {
            $level = new ZpJobsLevelTable()
                ->addWhere(ZpJobsLevelTable::ID, $job->levelId)
                ->selectOne();
            $levelMsg = new JobsLevelItem();
            $levelMsg->setId($level->id);
            $levelMsg->setName($level->name);
            $msg->setLevel($levelMsg);
        }


        $msg->setCompany($companyMsg);
        $msg->setIsCollected($isCollect);
        $msg->setHasApply(JobsApplyModel::hasApply($id, $userId));


        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '上传附件简历失败')]
    public function addResume(ResumeItem $request): ResumeItem
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }
        $url = $request->getUrl();
        if (!$url) {
            throw new AppException('请上传附件');
        }
        $name = $request->getName();
        if (!$name) {
            throw new AppException('简历名称为空');
        }

        $size = $request->getSize();

        $id = new ZpResumeTable()->insert([
            ZpResumeTable::USER_ID => $userId,
            ZpResumeTable::URL => $url,
            ZpResumeTable::NAME => $name,
            ZpResumeTable::SIZE => $size,
            ZpResumeTable::TIME => time(),
        ]);

        $msg = new ResumeItem();
        $msg->setId($id);
        $msg->setUrl($url);
        $msg->setName($name);
        $msg->setUserId($userId);
        $msg->setSize($size);

        return $msg;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除附件简历失败')]
    public function delResume(ResumeItem $request): Success
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }
        $id = $request->getId();
        new ZpResumeTable()
            ->addWhere(ZpResumeTable::USER_ID, $userId)
            ->addWhere(ZpResumeTable::ID, $id)->delete();
        $msg = new Success();
        $msg->setSuccess(true);
        return $msg;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取职位类型数量失败')]
    public function countByType(Request $request): CountByTypeListsMessage
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 1;

        $countField = ZpJobsTable::getFuncField(ZpJobsTable::ID, 'count');
        $query = new ZpJobsTable()
            ->field([
                ZpJobsTable::TYPE_ID,
                ZpJobsTypeTable::NAME,
                ZpJobsTypeTable::ID,
                $countField
            ])
            ->join(ZpJobsTypeTable::TABLE_NAME, ZpJobsTable::TYPE_ID, ZpJobsTypeTable::ID)
            ->group(ZpJobsTable::TYPE_ID)->order([
                $countField => 'desc'
            ])->page($page, $size);


        $lists = [];
        /**@var ZpJobsTable $table */
        foreach ($query->generator() as $table) {
            $typeMsg = new JobsTypeItem();
            $typeMsg->setName($table->getByField(ZpJobsTypeTable::NAME));
            $typeMsg->setId($table->getByField(ZpJobsTypeTable::ID));


            $item = new CountByTypeItemMessage();
            $item->setCount($table->getByField($countField));
            $item->setType($typeMsg);
            $lists[] = $item;

        }

        $msg = new CountByTypeListsMessage();
        $msg->setLists($lists);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取用户已查看职位列表失败')]
    public function getUserSeeLists(Request $request): JobsUserSeeListsMessage
    {
        $userId = $request->getUserId();
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 1;


        $seeTable = new ZpJobsUserSeeTable();
        $seeAll = $seeTable
            ->addWhere(ZpJobsUserSeeTable::USER_ID, $userId)
            ->page($page, $size)->selectAll();

        $seeIds = $seeTable->getArrayByField(ZpJobsUserSeeTable::JOB_ID);
        $jobs = JobsModel::getJobList($seeIds);

        $lists = [];
        foreach ($seeAll as $table) {
            $job = $jobs[$table->jobId];
            $item = new JobsUserSeeItemMessage();
            $item->setId($table->id);
            $item->setJobId($table->jobId);
            $item->setJobs($job);
            $item->setUserId($table->userId);
            $item->setNum($table->num);
            $item->setFirstTime(date('Y-m-d H:i:s', $table->firstTime));
            $item->setLastTime(date('Y-m-d H:i:s', $table->lastTime));

            $lists[] = $item;
        }

        $msg = new JobsUserSeeListsMessage();
        $msg->setLists($lists);
        return $msg;

    }

    /**
     * 统计用户的申请数，面试数，浏览数
     * @throws Throwable
     */
    #[Router(errorTitle: '统计用户数据失败')]
    public function userCounts(Request $request): UserCounts
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $applyCount = new ZpJobsApplyTable()->addWhere(ZpJobsApplyTable::USER_ID, $userId)->count();
        $interviewCount = new ZpJobsApplyTable()
            ->addWhere(ZpJobsApplyTable::USER_ID, $userId)
            ->addWhere(ZpJobsApplyTable::INTERVIEW_USER_ID, 0, '>')
            ->count();
        $seeCount = new ZpJobsUserSeeTable()->addWhere(ZpJobsUserSeeTable::USER_ID, $userId)->count();


        $message = new UserCounts();
        $message->setApply($applyCount);
        $message->setInterview($interviewCount);
        $message->setSee($seeCount);
        return $message;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '用户意向保存失败')]
    public function userSaveIntention(Intention $request): Success
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }
        $salaryRange = $request->getSalaryRange();
        $type = $request->getType();
        $city = $request->getCity();
        $typeIds = [];
        foreach ($request->getTypeIds() as $typeId) {
            $typeIds[] = $typeId;
        }

        $data = [
            ZpJobIntentionTable::SALARY_RANGE => $salaryRange,
            ZpJobIntentionTable::TYPE => $type,
            ZpJobIntentionTable::CITY => $city,
            ZpJobIntentionTable::TYPE_IDS => json_encode($typeIds, JSON_UNESCAPED_UNICODE)
        ];

        $intention = new ZpJobIntentionTable()->addWhere(ZpJobIntentionTable::USER_ID, $userId)->selectOne();
        if (empty($intention)) {
            $data[ZpJobIntentionTable::USER_ID] = $userId;
            new ZpJobIntentionTable()->insert($data);
        } else {
            new ZpJobIntentionTable()->addWhere(ZpJobIntentionTable::USER_ID, $userId)->update($data);
        }

        $success = new Success();
        $success->setSuccess(true);
        return $success;

    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '用户意向获取失败')]
    public function userGetIntention(Intention $request): Intention
    {
        $userId = $request->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }


        $intention = new ZpJobIntentionTable()->addWhere(ZpJobIntentionTable::USER_ID, $userId)->selectOne();
        $message = new Intention();
        if ($intention) {
            $message->setUserId($intention->userId);
            $message->setId($intention->id);
            $message->setType($intention->type);
            $message->setTypeIds(json_decode($intention->typeIds, true));
            $message->setCity($intention->city);
            $message->setSalaryRange($intention->salaryRange);
        }

        return $message;

    }


    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(errorTitle: "企业发布职位失败")]
    public function addJobByCompanies(ZpJobsProto $request): Success
    {
        $companyId = $request->getCompanyId();
        $phone = $request->getContactsPhone();
        $area = $request->getArea();
        $jobName = $request->getTitle();
        $salaryRange = $request->getSalaryRange();
        $ask = $request->getAsk();
        $userId = $request->getUserId();
        $id = $request->getId();

        if (empty($userId)) {
            throw new AppException("请登录");
        }
        if (empty($companyId)) {
            throw new AppException("请选择企业");
        }
        if (empty($phone)) {
            throw new AppException("请填写联系电话");
        }
        if (empty($area)) {
            throw new AppException("请选择工作地点");
        }
        $areaIds = [];
        foreach ($area as $areaId) {
            $areaIds[] = $areaId;
        }


        if (empty($jobName)) {
            throw new AppException("请填写职位名称");
        }
        if (empty($salaryRange)) {
            throw new AppException("请填写薪资范围");
        }
        if (empty($ask)) {
            throw new AppException("请填写职位要求");
        }

        $company = new HyCompaniesTable()->where([
            HyCompaniesTable::ID => $companyId
        ])->selectOne();
        if (empty($company)) {
            throw new AppException("企业不存在");
        }

        $data = [
            ZpJobsTable::COMPANY_ID => $companyId,
            ZpJobsTable::CONTACTS_NAME => $company->name,
            ZpJobsTable::CONTACTS_PHONE => $phone,
            ZpJobsTable::AREA => json_encode($areaIds),
            ZpJobsTable::TITLE => $jobName,
            ZpJobsTable::SALARY_RANGE => $salaryRange,
            ZpJobsTable::ASK => $ask,
            ZpJobsTable::USER_ID => $userId,
            ZpJobsTable::CREATED_TIME => time(),
        ];

        if ($id > 0) {
            $ret = new ZpJobsTable()->where([
                ZpJobsTable::ID => $id
            ])->update($data);
        } else {
            $ret = new ZpJobsTable()->insert($data);
        }


        $succ = new Success();
        $succ->setSuccess((bool)$ret);
        return $succ;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除数据失败')]
    public function delete(ZpJobsProto $request): Success
    {
        $id = $request->getId();
        $userId = $request->getUserId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new ZpJobsTable()->where([
            ZpJobsTable::ID => $id,
            ZpJobsTable::USER_ID => $userId,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

}