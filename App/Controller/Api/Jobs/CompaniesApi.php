<?php

namespace App\Controller\Api\Jobs;

use App\Model\ZpCompaniesModel;
use Generate\Tables\Datas\ZpCompaniesFocusTable;
use Generate\Tables\Datas\ZpCompaniesTable;
use Generate\Tables\Datas\ZpJobsApplyTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpResumeTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Protobuf\Datas\ZpCompanies\ZpCompaniesProto;
use Protobuf\Datas\ZpCompanies\ZpCompaniesStatusEnum;
use Protobuf\Datas\ZpJobsApply\ZpJobsApplyListsProto;
use Protobuf\Datas\ZpJobsApply\ZpJobsApplyProto;
use Protobuf\Datas\ZpJobsApply\ZpJobsApplyStatusEnum;
use Throwable;


/**
 * 招聘商家控制器
 */
#[Router(method: 'POST')]
class CompaniesApi extends AbstractController
{

    /**
     * 用户查看企业详情
     * @throws Throwable
     */
    #[Router(errorTitle: '企业获取详情失败')]
    public function detail(ZpCompaniesProto $request): ZpCompaniesProto
    {
        $id = $request->getId();
        $userId = $request->getUserId();
        if (empty($id)) {
            throw new AppException('参数错误');
        }


        $find = new ZpCompaniesTable()->addWhere(ZpCompaniesTable::ID, $id)->selectOne();
        if (empty($find)) {
            throw new AppException('商家不存在');
        }

        $focusCount = new ZpCompaniesFocusTable()->addWhere(ZpCompaniesFocusTable::COMPANY_ID, $id)->count();

        $isFocus = false;
        if ($userId) {
            $findFocus = new ZpCompaniesFocusTable()
                ->addWhere(ZpCompaniesFocusTable::COMPANY_ID, $id)
                ->addWhere(ZpCompaniesFocusTable::USER_ID, $userId)
                ->selectField(ZpCompaniesFocusTable::ID);
            $isFocus = (bool)$findFocus;
        }

        return ZpCompaniesModel::formatDetail($find, $focusCount, $isFocus);
    }

    /**
     * 查看自己的企业详情
     * @throws Throwable
     */
    #[Router(errorTitle: '企业获取详情失败')]
    public function detailByCompanies(ZpCompaniesProto $request): ZpCompaniesProto
    {
        $userId = $request->getUserId();
        $id = $request->getId();


        if ($userId) {
            $where = [ZpCompaniesTable::USER_ID => $userId];
        }
        if ($id) {
            $where = [ZpCompaniesTable::ID => $id];
        }

        if (empty($where)) {
            throw new AppException('参数错误');
        }

        $find = new ZpCompaniesTable()->where($where)->selectOne();
        if (empty($find)) {
            $message = new ZpCompaniesProto();
            $message->setId(0);
            return $message;
        }

        $focusCount = new ZpCompaniesFocusTable()->where([
            ZpCompaniesFocusTable::COMPANY_ID => $find->id
        ])->count();

        return ZpCompaniesModel::formatDetail($find, $focusCount, false);
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '关注企业失败')]
    public function followWithInterest(Request $request): Success
    {
        $userId = $request->getUserId();
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException('参数错误');
        }
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $find = new ZpCompaniesFocusTable()->where([
            [ZpCompaniesFocusTable::USER_ID, '=', $userId],
            [ZpCompaniesFocusTable::COMPANY_ID, '=', $id],
        ])->selectOne();

        if (empty($find)) {
            new ZpCompaniesFocusTable()->insert([
                ZpCompaniesFocusTable::USER_ID => $userId,
                ZpCompaniesFocusTable::COMPANY_ID => $id,
            ]);
        }

        $msg = new Success();
        $msg->setSuccess(true);
        return $msg;

    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(errorTitle: '申请入驻企业')]
    public function join(ZpCompaniesProto $request): Success
    {
        $businessLicense = $request->getBusinessLicense();
        $permit = $request->getPermit();
        $contactsName = $request->getContactsName();
        $phone = $request->getContactsPhone();
        $name = $request->getName();
        $userId = $request->getUserId();
        $authType = $request->getAuthType();

        if (empty($userId)) {
            throw new AppException('请登录');
        }

        if (empty($permit) && empty($businessLicense)) {
            throw new AppException('请上传营业执照或者许可证');
        }
//        if (empty($name)) {
//            throw new AppException('请填写门诊名称');
//        }
//        if (empty($contactsName)) {
//            throw new AppException('请填写联系人');
//        }
//        if (empty($phone)) {
//            throw new AppException('请填写联系电话');
//        }

        $find = new ZpCompaniesTable()->where([
            ZpCompaniesTable::USER_ID => $userId
        ])->selectOne();
        $data = [
            ZpCompaniesTable::USER_ID => $userId,
            ZpCompaniesTable::BUSINESS_LICENSE => $businessLicense,
            ZpCompaniesTable::PERMIT => $permit,
            ZpCompaniesTable::CONTACTS_NAME => $contactsName,
            ZpCompaniesTable::NAME => $name,
            ZpCompaniesTable::CONTACTS_PHONE => $phone,
            ZpCompaniesTable::TIME => time(),
            ZpCompaniesTable::STATUS => ZpCompaniesStatusEnum::PENDING,
            ZpCompaniesTable::AUTH_TYPE => $authType,
        ];
        if ($find) {
            $res = new ZpCompaniesTable()->where([
                ZpCompaniesTable::ID => $find->id
            ])->update($data);
        } else {
            $res = new ZpCompaniesTable()->insert($data);
        }

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * 获取企业的职位有那些用户申请了
     * @throws Throwable
     */
    #[Router(errorTitle: '获取企业的职位申请用户失败')]
    public function applyList(ZpCompaniesProto $request): ZpJobsApplyListsProto
    {
        $companyId = $request->getId();
        $page = $request->getQueryPageNo();
        $size = $request->getQueryPageSize();

        $all = new ZpJobsApplyTable()
            ->where([
                ZpJobsApplyTable::COMPANY_ID => $companyId,
            ])
            ->field([
                ZpJobsApplyTable::FIELD_ALL,
                ZpResumeTable::CONTACTS_NAME,
                ZpResumeTable::CONTACTS_PHONE,
                ZpJobsTable::TITLE
            ])
            ->join(ZpResumeTable::TABLE_NAME, ZpResumeTable::ID, ZpJobsApplyTable::RESUME_ID)
            ->join(ZpJobsTable::TABLE_NAME, ZpJobsTable::ID, ZpJobsApplyTable::JOB_ID)
            ->page($page, $size)->selectAll();

        $nodes = [];
        foreach ($all as $table) {
            $proto = new ZpJobsApplyProto();
            $proto->setContactsName($table->getByField(ZpResumeTable::CONTACTS_NAME));
            $proto->setContactsPhone($table->getByField(ZpResumeTable::CONTACTS_PHONE));
            $proto->setJobTitle($table->getByField(ZpJobsTable::TITLE));
            $proto->setUserId($table->userId);
            $proto->setJobId($table->getByField(ZpJobsApplyTable::JOB_ID));
            $proto->setId($table->getByField(ZpJobsApplyTable::ID));
            $proto->setCompanyId($table->getByField(ZpJobsApplyTable::COMPANY_ID));
            $status = $table->status;
            $proto->setStatus(ZpJobsApplyStatusEnum::value($status));
            $proto->setInterviewTimeStr(date($table->getByField(ZpJobsApplyTable::INTERVIEW_TIME)));
            $proto->setFeedback($table->getByField(ZpJobsApplyTable::FEEDBACK, ''));
            $proto->setTimeStr(date('Y-m-d', $table->getByField(ZpJobsApplyTable::TIME)));


            $nodes[] = $proto;
        }
        $ret = new ZpJobsApplyListsProto();
        $ret->setLists($nodes);
        return $ret;

    }

}