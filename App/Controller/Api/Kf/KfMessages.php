<?php

namespace App\Controller\Api\Kf;


use Generate\Tables\Datas\KfSessionsTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Controller\AbstractController;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Generate\Tables\Datas\KfMessagesTable;
use Generate\Models\Datas\KfMessagesModel;
use Swlib\Utils\Server;
use Protobuf\Datas\KfMessages\KfMessagesListsProto;
use Protobuf\Datas\KfMessages\KfMessagesProto;
use Throwable;


#[Router(method: 'POST')]
class KfMessages extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '发送消息失败')]
    public function send(KfMessagesProto $request): KfMessagesProto
    {
        $table = KfMessagesModel::request($request);
        $sessionId = $table->sessionId;
        $agentId = $request->getAgentId();
        $targetType = $request->getTargetType();
        $targetId = $request->getTargetId();

        Event::emit('MessageSendEvent', [
            'sessionId' => $sessionId,
            'agentId' => $agentId,
            'targetType' => $targetType,
            'targetId' => $targetId,
            'userId' => $table->userId,
            'content' => $table->content,
            'type' => $table->type,
        ]);

        $msg = new KfMessagesProto();
        $msg->setSendTimeStr(date("Y-m-d H:i:s", time()));
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(KfMessagesProto $request): KfMessagesListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $sessionId = $request->getSessionId();
        $userId = $request->getUserId();

        if (empty($sessionId)) {
            throw new AppException('请输入会话ID');
        }

        if ($userId) {
            // task 进程设置 未读数量为 0
            Server::task([__CLASS__, 'setUnReadEmpty'], [
                'userId' => $userId,
                'sessionId' => $sessionId,
            ]);
        }


        $where = [
            KfMessagesTable::SESSION_ID => $sessionId,
        ];
        $order = [KfMessagesTable::ID => "desc"];
        $table = new KfMessagesTable();
        $lists = $table->order($order)->where($where)->page($page, $size)->selectAll();

        $userIds = $table->getArrayByField(KfMessagesTable::USER_ID);

        $userTable = new UserTable();
        $userTable->where([
            [UserTable::ID, 'in', $userIds]
        ])->selectAll();
        $userId2Head = $userTable->formatId2Name(UserTable::ID, UserTable::HEAD);
        $userId2Nickname = $userTable->formatId2Name(UserTable::ID, UserTable::NICKNAME);

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = KfMessagesModel::formatItem($table);
            $proto->setSendTimeStr(date("Y-m-d H:i:s", $table->sendTime));
            $proto->setNickname($userId2Nickname[$table->userId] ?? "");
            $proto->setHead($userId2Head[$table->userId] ?? "");
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new KfMessagesListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * 设置未读数量为 0
     * @throws Throwable
     */
    public function setUnReadEmpty(array $data): void
    {
        $sessionId = $data['sessionId'];
        $userId = $data['userId'];

        $find = new KfSessionsTable()->where([
            KfSessionsTable::ID => $sessionId,
        ])->selectOne();
        // 设置未读数量为 0
        $update = [];
        if ($find->userId === $userId) {
            $update[KfSessionsTable::LAST_COUNT] = 0;
        } elseif ($find->agentId === $userId) {
            $update[KfSessionsTable::AGENT_LAST_COUNT] = 0;
        }
        if ($update) {
            new KfSessionsTable()->where([
                KfSessionsTable::ID => $sessionId
            ])->update($update);
        }
    }


}