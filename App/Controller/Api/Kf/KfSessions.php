<?php

namespace App\Controller\Api\Kf;


use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Tables\Datas\BiddingShopTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\KfMessagesTable;
use Generate\Tables\Datas\UserTable;
use Generate\Tables\Datas\WxEngineerTable;
use Generate\Tables\Datas\WxServiceTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpResumeTable;
use ReflectionClass;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Generate\Tables\Datas\KfSessionsTable;
use Generate\Models\Datas\KfSessionsModel;
use Protobuf\Common\Success;
use Protobuf\Datas\KfSessions\KfSessionsListsProto;
use Protobuf\Datas\KfSessions\KfSessionsProto;
use Throwable;


#[Router(method: 'POST')]
class KfSessions extends AbstractController
{

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '创建会话失败')]
    public function create(KfSessionsProto $request): KfSessionsProto
    {
        $table = KfSessionsModel::request($request);
        if (empty($table->userId)) {
            throw new AppException('请登录');
        }

        $table->startTime = time();
        $table->status = KfSessionsModel::StatusOpen;

        $find = new KfSessionsTable()->where([
            KfSessionsTable::USER_ID => $table->userId,
        ])->selectOne();

        // 目前只做单一会话
        if (empty($find)) {
            $id = $table->insert();
        } else {
            $id = $find->id;
        }

        $msg = new KfSessionsProto();
        $msg->setId($id);
        return $msg;
    }


    /**
     * 每个节点创建不同的会话
     * @throws Throwable
     */
    #[Router(errorTitle: '创建会话失败')]
    public function create2(KfSessionsProto $request): KfSessionsProto
    {
        $path = $request->getExtStr();
        $table = KfSessionsModel::request($request);
        if (empty($table->userId)) {
            throw new AppException('请登录');
        }
        if (empty($table->agentId)) {
            throw new AppException('请登录');
        }


        $table->startTime = time();
        $table->lastTime = time();
        $table->status = KfSessionsModel::StatusOpen;

        $userId = $table->userId;
        $agentId = $table->agentId;
        $find = new KfSessionsTable()->addWhereRaw("and (user_id = ? and agent_id = ?) or (user_id = ? and agent_id = ?)", [$userId, $agentId, $agentId, $userId])->selectOne();

        $saveSource = true;
        if (empty($find)) {
            $id = $table->insert();
        } else {

            // 上次的消息来源跟这次的一样，就不写入来源了
            if ($find->targetId === $table->targetId && $find->targetType === $table->targetType) {
                $saveSource = false;
            }


            $table->where([
                KfSessionsTable::ID => $find->id,
            ])->update([
                KfSessionsTable::LAST_TIME => time(),
                KfSessionsTable::TARGET_TYPE => $table->targetType,
                KfSessionsTable::TARGET_ID => $table->targetId,
            ]);
            $id = $find->id;
        }

        $tableName = $table->targetType;
        $targetId = $table->targetId;

        // 使用反射获取表格类并查询数据
        if ($saveSource === true && !empty($tableName) && !empty($targetId)) {
            try {
                // 将下划线分隔的表名转换为驼峰式类名
                $className = '';
                $parts = explode('_', $tableName);
                foreach ($parts as $part) {
                    $className .= ucfirst($part);
                }
                $className .= 'Table';

                // 构建完整的类名
                $fullClassName = "Generate\\Tables\\Datas\\$className";

                // 使用反射检查类是否存在
                if (class_exists($fullClassName)) {

                    // 获取主键常量
                    $reflectionClass = new ReflectionClass($fullClassName);
                    $priKeyConstant = $reflectionClass->getConstants()['PRI_KEY'];

                    // 实例化表格并查询数据
                    $targetTable = new $fullClassName();
                    $targetRecord = $targetTable->where([
                        $priKeyConstant => $targetId
                    ])->selectOne();


                    // 实例化KfMessagesTable
                    $kfMessageTable = new KfMessagesTable();

                    $source = [
                        'name' => '',
                        'path' => $path,
                    ];

                    if (isset($targetRecord->type) && $targetRecord->type) {
                        $source['type'] = HyCompaniesServiceModel::TypeTextMaps[$targetRecord->type] ?? '';
                    }

                    if (isset($targetRecord->name) && $targetRecord->name) {
                        $source['name'] = $targetRecord->name;
                    } elseif (isset($targetRecord->title) && $targetRecord->title) {
                        $source['name'] = $targetRecord->title;
                    } elseif (isset($targetRecord->productName) && $targetRecord->productName) {
                        $source['name'] = $targetRecord->productName;
                    } elseif (isset($targetRecord->companiesId) && $targetRecord->companiesId) {
                        $find = new HyCompaniesTable()->where([
                            HyCompaniesTable::ID => $targetRecord->companiesId
                        ])->selectOne();
                        if ($find) {
                            $source['name'] = $find->name;
                        }
                    }

                    if (isset($targetRecord->description) && $targetRecord->description) {
                        $source['description'] = $targetRecord->description;
                    }
                    if (isset($targetRecord->images) && $targetRecord->images) {
                        $arr = explode(',', $targetRecord->images);
                        $source['images'] = $arr[0];
                    }

                    // 写入新消息记录
                    $kfMessageTable->insert([
                        KfMessagesTable::SESSION_ID => $id,
                        KfMessagesTable::CONTENT => $targetId,
                        KfMessagesTable::SEND_TIME => time(),
                        KfMessagesTable::TYPE => 4,
                        KfMessagesTable::USER_ID => $userId,
                        KfMessagesTable::SOURCE => json_encode($source, JSON_UNESCAPED_UNICODE)
                    ]);
                }
            } catch (Throwable) {
            }
        }

        $msg = new KfSessionsProto();
        $msg->setId($id);
        return $msg;
    }


    /**
     * 统计未读消息数量
     * @throws Throwable
     */
    #[Router(errorTitle: '获取未读消息失败')]
    public function countUnRead(KfSessionsProto $request): KfSessionsProto
    {
        $usrId = $request->getUserId();
        $count = new KfSessionsTable()->where([
            [KfSessionsTable::AGENT_ID, '=', $usrId],
            [KfSessionsTable::USER_ID, '<>', $usrId],
        ])->sum(KfSessionsTable::AGENT_LAST_COUNT);

        $msg = new KfSessionsProto();
        $msg->setLastCount($count);
        return $msg;
    }


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取列表数据失败')]
    public function lists(KfSessionsProto $request): KfSessionsListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $userId = $request->getUserId();

        $where = [];


        $order = [KfSessionsTable::LAST_TIME => "desc"];
        $table = new KfSessionsTable();
        $query = $table->order($order)->where($where)->page($page, $size);
        if ($userId) {
            $query->addWhereRaw('and (kf_sessions.user_id= ? or kf_sessions.agent_id= ?)', [$userId, $userId]);
        }
        $lists = $query->selectAll();

        $agentIds = $table->getArrayByField(KfSessionsTable::AGENT_ID);
        $userIds = $table->getArrayByField(KfSessionsTable::USER_ID);
        $userTable = new UserTable();
        $userTable->where([
            [UserTable::ID, 'in', array_unique(array_merge($agentIds, $userIds))]
        ])->selectAll();
        $userId2Nickname = $userTable->formatId2Name(UserTable::ID, UserTable::NICKNAME);
        $userId2Head = $userTable->formatId2Name(UserTable::ID, UserTable::HEAD);


        $protoLists = [];
        foreach ($lists as $table) {
            $proto = KfSessionsModel::formatItem($table);

            if ($userId === $table->agentId) {
                $proto->setHead($userId2Head[$table->userId] ?? "");
                $proto->setNickname($userId2Nickname[$table->userId] ?? "");
            } else {
                $proto->setHead($userId2Head[$table->agentId] ?? "");
                $proto->setNickname($userId2Nickname[$table->agentId] ?? "");
            }


            $proto->setLastTimeStr(date('Y-m-d H:i:s', $table->lastTime));
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new KfSessionsListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }


    /**
     * @throws Throwable
     * @throws AppException
     */
    #[Router(errorTitle: '设置已读失败')]
    public function setRead(KfSessionsProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new KfSessionsTable()->where([
            KfSessionsTable::ID => $id,
        ])->update([
            KfSessionsTable::LAST_COUNT => 0,
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }


    /**
     * @throws AppException
     * @throws Throwable
     */
    #[Router(errorTitle: '获取每个分类的未读条目数量')]
    public function countByType(KfSessionsProto $request): KfSessionsListsProto
    {
        $usrId = $request->getUserId();
        if (empty($usrId)) {
            throw new AppException("参数错误");
        }

        $counts = new KfSessionsTable()->field([
            KfSessionsTable::TARGET_TYPE,
            KfSessionsTable::USER_ID,
            KfSessionsTable::AGENT_ID,
            KfSessionsTable::LAST_COUNT,
            KfSessionsTable::AGENT_LAST_COUNT,
        ])->where([
            [KfSessionsTable::TARGET_TYPE, 'is not null'],
        ])->addWhereRaw('and (kf_sessions.user_id= ? or kf_sessions.agent_id= ?)', [$usrId, $usrId])->selectAll();


        $arr = [];
        foreach ($counts as $table) {
            if ($table->userId === $usrId) {
                $count = $table->lastCount;
            } else {
                $count = $table->agentLastCount;
            }

            $type = $table->targetType;
            if (!array_key_exists($type, $arr)) {
                $arr[$type] = 0;
            }

            $arr[$type] += $count;
        }


        $nodes = [];
        foreach ($arr as $type => $count) {
            $proto = new KfSessionsProto();
            $proto->setTargetType($type);
            $proto->setLastCount($count);
            $nodes[] = $proto;
        }
        $msg = new KfSessionsListsProto();
        $msg->setLists($nodes);
        return $msg;

    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '关闭会话失败')]
    public function close(KfSessionsProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new KfSessionsTable()->where([
            KfSessionsTable::ID => $id,
        ])->update([
            KfSessionsTable::STATUS => KfSessionsModel::StatusClosed,
        ]);

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

}