<?php

namespace App\Event;

use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Swlib\Event\AbstractEvent;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Throwable;


/**
 * 更新产品的 最小价格
 */
#[Event('update.product.minPrice')]
class UpdateProductMinPriceEvent extends AbstractEvent
{
    /**
     * @throws AppException|Throwable
     */
    public function handle(array $args): void
    {
        $productId = $args['productId'];

        $minPrice = new HyProductsSkuTable()->where([
            [HyProductsSkuTable::PRODUCT_ID, '=', $productId],
        ])->min(HyProductsSkuTable::PRICE);


        new HyCompaniesServiceTable()->where([
            [HyCompaniesServiceTable::ID, '=', $productId],
        ])->update([HyCompaniesServiceTable::PRICE => $minPrice]);

    }

}