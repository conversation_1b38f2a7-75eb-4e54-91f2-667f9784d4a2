<?php

namespace App\Event;


use Generate\Tables\Datas\UserTable;
use Swlib\Event\AbstractEvent;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Swlib\Utils\Server;
use Swoole\Http\Request;
use Throwable;

/**
 * 路由访问事件，访问所有的路由都会触发这个事件
 */
#[Event('HttpRouteEnterEvent')]
class HttpRouteEnterEvent extends AbstractEvent
{

    /**
     * @throws AppException
     */
    public function handle(array $args): void
    {
        /** @var Request $request */
        $request = $args['request'];
        $random = $request->header['random'] ?? '';
        if (empty($random)) {
            return;
        }

        $userId = substr($random, 3);
        if (empty($userId)) {
            return;
        }

        // 到 task 进程去执行
        Server::task([__CLASS__, 'updateUserActiveTime'], [
            'userId' => $userId
        ]);
    }


    /**
     * 异步更新用户的最后活跃时间
     * @throws Throwable
     */
    public function updateUserActiveTime(array $data): void
    {
        $userId = $data['userId'];

        if (empty($userId)) {
            return;
        }
        new UserTable()->where([
            UserTable::ID => $userId
        ])->update([
            UserTable::LAST_ACTIVE_TIME => time()
        ]);
    }
}