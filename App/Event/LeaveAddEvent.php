<?php

namespace App\Event;

use Generate\Models\Datas\ZpLeaveModel;
use Generate\Tables\Datas\ZpLeaveTable;
use Swlib\Event\AbstractEvent;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Throwable;


/**
 * 添加留言事件
 */
#[Event('LeaveAddEvent')]
class LeaveAddEvent extends AbstractEvent
{
    /**
     * @throws AppException|Throwable
     */
    public function handle(array $args): void
    {
        $send_user_id = $args['send_user_id'];
        $to_user_id = $args['to_user_id'];
        $content = $args['content'];
        $target_id = $args['target_id'];
        $target_type = $args['target_type'];
        $msg_type = $args['msg_type'];

        if (!array_key_exists($target_type, ZpLeaveModel::TargetTypeTextMaps)) {
            throw new AppException("target_type 类型错误");
        }

        if (!array_key_exists($msg_type, ZpLeaveModel::MsgTypeTextMaps)) {
            throw new AppException("msg_type 类型错误");
        }


        new ZpLeaveTable()->insert([
            ZpLeaveTable::SEND_USER_ID => $send_user_id,
            ZpLeaveTable::TO_USER_ID => $to_user_id,
            ZpLeaveTable::CONTENT => $content,
            ZpLeaveTable::TARGET_ID => $target_id,
            ZpLeaveTable::TARGET_TYPE => $target_type,
            ZpLeaveTable::MSG_TYPE => $msg_type,
            ZpLeaveTable::TIME => time(),
            ZpLeaveTable::STATUS => ZpLeaveModel::StatusUnread,
        ]);

    }

}