<?php

namespace App\Event;

use Generate\Models\Datas\KfSessionsModel;
use Generate\Tables\Datas\KfMessagesTable;
use Generate\Tables\Datas\KfSessionsTable;
use Swlib\Event\AbstractEvent;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Swlib\Table\Db;
use Swlib\Utils\Server;
use Throwable;


/**
 * 添加发送消息 事件
 */
#[Event('MessageSendEvent')]
class MessageSendEvent extends AbstractEvent
{
    /**
     * @throws AppException|Throwable
     */
    public function handle(array $args): void
    {
        $sessionId = $args['sessionId'] ?? 0;
        $agentId = $args['agentId'];
        $targetType = $args['targetType'];
        $targetId = $args['targetId'];
        $userId = $args['userId'];
        $content = $args['content'];
        $type = $args['type'];
        if (empty($userId)) {
            throw new AppException('请登录');
        }
        if (empty($sessionId) && empty($agentId)) {
            throw new AppException('参数错误');
        }

        if (empty($content)) {
            throw new AppException('请输入内容');
        }

        if (empty($type)) {
            throw new AppException('请输入消息类型');
        }

        if ($agentId && empty($sessionId)) {
            $find = new KfSessionsTable()->where([
                KfSessionsTable::AGENT_ID => $agentId,
                KfSessionsTable::USER_ID => $userId,
            ])->selectOne();
            if ($find) {
                $sessionId = $find->id;
            } else {
                $insert = [
                    KfSessionsTable::USER_ID => $userId,
                    KfSessionsTable::AGENT_ID => $agentId,
                    KfSessionsTable::START_TIME => time(),
                    KfSessionsTable::STATUS => KfSessionsModel::StatusOpen,
                    KfSessionsTable::LAST_TIME => time(),
                    KfSessionsTable::LAST_MSG => '',
                    KfSessionsTable::LAST_COUNT => 0,
                ];
                if ($targetType) {
                    $insert[KfSessionsTable::TARGET_TYPE] = strtolower($targetType);
                    $insert[KfSessionsTable::TARGET_ID] = $targetId;
                }
                $sessionId = new KfSessionsTable()->insert($insert);
            }
        }

        // task 进程 更新会话未读数量
        Server::task([__CLASS__, 'updateUnRead'], [
            'content' => $content,
            'sessionId' => $sessionId,
            'userId' => $userId,
            'targetType' => $targetType,
            'targetId' => $targetId,
        ]);

        new KfMessagesTable()->insert([
            KfMessagesTable::SESSION_ID => $sessionId,
            KfMessagesTable::CONTENT => $content,
            KfMessagesTable::SEND_TIME => time(),
            KfMessagesTable::TYPE => $type,
            KfMessagesTable::USER_ID => $userId,
        ]);


    }


    /**
     * 更新会话未读数量
     * @throws Throwable
     */
    public function updateUnRead(array $data): void
    {
        $content = $data['content'];
        $sessionId = $data['sessionId'];
        $userId = $data['userId'];
        $targetType = $data['targetType'];
        $targetId = $data['targetId'];

        $update = [
            KfSessionsTable::LAST_TIME => time(),
            KfSessionsTable::LAST_MSG => $content,
        ];

        if($targetType){
            $update[KfSessionsTable::TARGET_TYPE] = strtolower($targetType);
            $update[KfSessionsTable::TARGET_ID] = $targetId;
        }

        $find = new KfSessionsTable()->where([
            KfSessionsTable::ID => $sessionId,
        ])->selectOne();
        // 增加对方的 未读数量
        if ($find->userId === $userId) {
            $update[KfSessionsTable::AGENT_LAST_COUNT] = Db::incr(KfSessionsTable::AGENT_LAST_COUNT);
        } elseif ($find->agentId === $userId) {
            $update[KfSessionsTable::LAST_COUNT] = Db::incr(KfSessionsTable::LAST_COUNT);
        }
        new KfSessionsTable()->where([
            KfSessionsTable::ID => $sessionId
        ])->update($update);
    }


}