<?php

namespace App\Event;

use Generate\Tables\Datas\CommentTable;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Swlib\Event\AbstractEvent;
use Swlib\Event\Event;
use Swlib\Exception\AppException;
use Throwable;


/**
 * 更新产品的 最小价格
 */
#[Event('count.companiesService.commentNum')]
class CountHyCompaniesServiceCommentNumEvent extends AbstractEvent
{
    /**
     * @throws AppException|Throwable
     */
    public function handle(array $args): void
    {
        $productId = $args['productId'];

        $total = new CommentTable()->where([
            CommentTable::PRODUCT_TABLE => 'hy_companies_service',
            CommentTable::PRODUCT_ID => $productId,
        ])->count();
        new HyCompaniesServiceTable()->where([
            HyCompaniesServiceTable::ID => $productId
        ])->update([
            HyCompaniesServiceTable::COMMENT_NUM => $total,
        ]);

    }

}