<?php

namespace App\Service;

use Swlib\Connect\PoolRedis;
use Swlib\Exception\AppException;
use Redis;
use Throwable;

class SmsService
{
    /**
     * @throws Throwable
     */
    public static function send(string $phone, bool $isYY = false): void
    {
        PoolRedis::call(function (Redis $redis) use ($phone, $isYY) {

            // IP 发送了多少个手机号码
            $ipLockKey = "sms:$phone:ip:lock";
            if ($redis->scard($ipLockKey) > 5) {
                throw new AppException("发送验证码过多 请稍后再试");
            }

            // 验证码发送频率
            $lockKey = "sms:$phone:lock";
            if ($redis->exists($lockKey)) {
                throw new AppException("发送验证码太快了 请稍后再试");
            }

            // 记录验证码发送次数，一天内达到上限 不再发送
            $numKey = "sms:$phone:num";
            if ($redis->get($numKey) > 10) {
                throw new AppException("发送次数已达上限");
            }

            if (stripos($phone, '13800138') === false) {
                $code = mt_rand(1000, 9999);
                if ($isYY) {
                    AliSmsService::sendYY($phone, $code);
                } else {
                    AliSmsService::send($phone, $code);
                }

            } else {
                $code = 8888;
            }


            // IP 发送了多少个手机号码
            $redis->sAdd($ipLockKey, $phone);
            $redis->expire($lockKey, 86400);

            // 验证码发送频率
            $redis->set($lockKey, 1);
            $redis->expire($lockKey, 60);

            // 验证码10分钟有效
            $codeKey = "sms:$phone:code";
            $redis->set($codeKey, $code);
            $redis->expire($codeKey, 600);

            // 记录验证码发送次数，一天内达到上限 不再发送
            $redis->incr($numKey);
            $redis->expire($numKey, 86400);

            //每次发送新的以后 删除之前的验证结果
            $redis->del("sms:$phone:check");
        });


    }

    /**
     * 比对验证码
     * 5分钟内有效
     * @throws Throwable
     */
    public static function check(int $phone, int $code = 0): void
    {
        PoolRedis::call(function ($redis) use ($phone, $code) {
            $codeKey = "sms:$phone:code";
            $checkKey = "sms:$phone:check";

            if ($redis->exists($checkKey)) {
                return;
            }

            if (!$redis->exists($codeKey)) {
                throw new AppException("验证码已过期");
            }
            if ($redis->get($codeKey) != $code) {
                throw new AppException("验证码错误");
            }

            $redis->set($checkKey, 1);
            $redis->expire($checkKey, 300);
        });

    }

}