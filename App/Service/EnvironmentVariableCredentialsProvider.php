<?php

namespace App\Service;
require_once ROOT_DIR . "sdk/aliyun-oss-php-sdk-2.7.1/autoload.php";

use Generate\ConfigEnum;
use OSS\Credentials\Credentials;
use OSS\Credentials\CredentialsProvider;
use Throwable;


class EnvironmentVariableCredentialsProvider implements CredentialsProvider
{

    /**
     * @return Credentials
     * @throws Throwable
     */
    public function getCredentials(): Credentials
    {
        $ak = ConfigEnum::ALI_ACCESS_KEY_ID;
        $sk = ConfigEnum::ALI_ACCESS_KEY_SECRET;
        return new Credentials($ak, $sk, null);
    }
}