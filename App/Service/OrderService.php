<?php

namespace App\Service;

use App\Model\OrderModel;
use Generate\Models\Datas\HyCompaniesModel;
use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Models\Datas\HyProductsSkuModel;
use Generate\Models\Datas\ShopOrderModel;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyCompaniesServiceTable;  
use Generate\Tables\Datas\HyProductsSkuTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\ShopOrderTable;
use Protobuf\Datas\Order\OrderListsProto;
use Protobuf\Datas\Order\OrderStatusEnum;
use Protobuf\Datas\Order\OrderProto;
use Swlib\Exception\AppException;
use Throwable;

class OrderService
{
    /**
     * 格式化订单列表
     * @param array<OrderTable> $orders
     * @param array $shopOrderProtoLists
     * @return OrderListsProto
     * @throws Throwable
     */
    public function formatLists(array $orders, array $shopOrderProtoLists): OrderListsProto
    {
        // 组装最终返回数据
        $nodes = [];
        foreach ($orders as $orderTable) {
            // 设置退款状态默认值
            if (!$orderTable->refundStatus) {
                $orderTable->refundStatus = 0;
            }

            // 格式化订单基本信息
            $orderProto = \Generate\Models\Datas\OrderModel::formatItem($orderTable);

            $orderProto->setTimeStr(date('Y-m-d H:i:s',$orderTable->time));

            // 设置该订单中属于该商家的商品信息
            $sn = $orderTable->sn;
            if (isset($shopOrderProtoLists[$sn])) {
                $orderProto->setShops($shopOrderProtoLists[$sn]);
            }

            $nodes[] = $orderProto;
        }

        $message = new OrderListsProto();
        $message->setLists($nodes);
        return $message;
    }

    /**
     * 获取并验证订单信息
     * @param OrderProto $request
     * @return OrderTable
     * @throws Throwable
     */
    public function getOrderTable(OrderProto $request): OrderTable
    {
        $orderTable = OrderModel::getOrder($request);
        if ($orderTable->status === \Generate\Models\Datas\OrderModel::StatusWait_pay) {
            throw new AppException('订单未支付');
        }
        if ($orderTable->status === \Generate\Models\Datas\OrderModel::StatusRefund) {
            throw new AppException('订单已经退款中');
        }
        if ($orderTable->status === \Generate\Models\Datas\OrderModel::StatusCancel) {
            throw new AppException('订单已取消');
        }
        if ($orderTable->status === \Generate\Models\Datas\OrderModel::StatusTimeout) {
            throw new AppException('订单已超时');
        }
        if ($orderTable->status === \Generate\Models\Datas\OrderModel::StatusRefund_finish) {
            throw new AppException('订单已经退款完成');
        }
        return $orderTable;
    }

    /**
     * 获取订单商品详情信息
     * @param array $sns 订单编号数组
     * @param int|null $businessId 商家ID，如果传入则只返回该商家的商品
     * @return array
     * @throws Throwable
     */
    public function getOrderShopDetails(array $sns, ?int $businessId = null): array
    {
        if (empty($sns)) {
            return [];
        }

        // 获取商店订单数据
        $shopOrders = $this->getShopOrders($sns, $businessId);
        if (empty($shopOrders)) {
            return [];
        }

        // 获取关联数据
        $companies = $this->getCompaniesData($shopOrders);
        $products = $this->getProductsData($shopOrders);
        $skus = $this->getSkusData($shopOrders);

        // 按订单编号和产品ID分组
        $groupedOrders = $this->groupOrdersByProductId($shopOrders, $skus);

        // 组装最终的订单商品信息
        return $this->assembleOrderShopDetails($groupedOrders, $companies, $products);
    }

    /**
     * 获取商店订单数据
     * @param array $sns
     * @param int|null $businessId
     * @return array
     * @throws Throwable
     */
    private function getShopOrders(array $sns, ?int $businessId = null): array
    {
        $shopOrderTable = new ShopOrderTable();
        $whereConditions = [
            [ShopOrderTable::SN, 'in', $sns]
        ];
        
        // 如果指定了商家ID，则只查询该商家的商品
        if ($businessId !== null) {
            $whereConditions[] = [ShopOrderTable::BUSINESS_ID, '=', $businessId];
        }
        
        return $shopOrderTable->where($whereConditions)->selectAll();
    }

    /**
     * 获取公司数据
     * @param array $shopOrders
     * @return array
     * @throws Throwable
     */
    private function getCompaniesData(array $shopOrders): array
    {
        $companiesIds = [];
        foreach ($shopOrders as $order) {
            $companiesIds[] = $order->businessId;
        }
        $companiesIds = array_unique($companiesIds);

        $companies = [];
        if ($companiesIds) {
            $companies = new HyCompaniesTable()->where([
                [HyCompaniesTable::ID, 'in', $companiesIds]
            ])->field(HyCompaniesTable::FIELD_ALL)->formatId2Array(HyCompaniesTable::ID);
        }

        return $companies;
    }

    /**
     * 获取产品数据
     * @param array $shopOrders
     * @return array
     * @throws Throwable
     */
    private function getProductsData(array $shopOrders): array
    {
        $productIds = [];
        foreach ($shopOrders as $order) {
            $productIds[] = $order->productId;
        }
        $productIds = array_unique($productIds);

        $products = [];
        if (!empty($productIds)) {
            $products = new HyCompaniesServiceTable()->where([
                [HyCompaniesServiceTable::ID, 'IN', $productIds]
            ])->field([
                HyCompaniesServiceTable::FIELD_ALL
            ])->formatId2Array(HyCompaniesServiceTable::ID);
        }

        return $products;
    }

    /**
     * 获取SKU数据
     * @param array $shopOrders
     * @return array
     * @throws Throwable
     */
    private function getSkusData(array $shopOrders): array
    {
        $skuIds = [];
        foreach ($shopOrders as $order) {
            $skuIds[] = $order->skuId;
        }
        $skuIds = array_unique($skuIds);

        $skus = [];
        if (!empty($skuIds)) {
            $skus = new HyProductsSkuTable()->where([
                [HyProductsSkuTable::ID, 'IN', $skuIds]
            ])->field([
                HyProductsSkuTable::FIELD_ALL
            ])->formatId2Array(HyProductsSkuTable::ID);
        }

        return $skus;
    }



    /**
     * 按订单编号和产品ID分组订单
     * @param array $shopOrders
     * @param array $skus
     * @return array
     * @throws Throwable
     */
    private function groupOrdersByProductId(array $shopOrders, array $skus): array
    {
        $groupedOrders = [];

        foreach ($shopOrders as $shopOrderTable) {
            $sn = $shopOrderTable->sn;
            $productId = $shopOrderTable->productId;
            $buyNum = $shopOrderTable->num;
            $buyPrice = $shopOrderTable->price;
            $businessId = $shopOrderTable->businessId;
            $groupKey = $sn . '_' . $productId;

            if (!isset($groupedOrders[$groupKey])) {
                $groupedOrders[$groupKey] = [
                    'main_order' => $shopOrderTable,
                    'main_order_price' => 0,
                    'main_order_number' => 0,
                    'businessId' => $businessId,
                    'skus' => [],
                    'sn' => $sn,
                    'product_id' => $productId
                ];
            }

            $groupedOrders[$groupKey]['main_order_price'] += $buyPrice * $buyNum;
            $groupedOrders[$groupKey]['main_order_number'] = $buyNum;

            // 收集该产品下的所有SKU信息
            $skuId = $shopOrderTable->skuId;
            if (isset($skus[$skuId])) {
                $skuInfo = HyProductsSkuModel::formatItem($skus[$skuId]);
                $skuInfo->setBuyNum($buyNum);
                $skuInfo->setBuyPrice($buyPrice);
                $groupedOrders[$groupKey]['skus'][] = $skuInfo;
            }
        }

        return $groupedOrders;
    }

    /**
     * 组装最终的订单商品详情
     * @param array $groupedOrders
     * @param array $companies
     * @param array $products
     * @return array
     * @throws Throwable
     */
    private function assembleOrderShopDetails(array $groupedOrders, array $companies, array $products): array
    {
        $shopOrderProtoLists = [];

        foreach ($groupedOrders as $group) {
            $sn = $group['sn'];
            $productId = $group['product_id'];
            $mainOrder = $group['main_order'];
            $businessId = $group['businessId'];

            // 创建主要的ShopOrderProto
            $shopOrderProto = ShopOrderModel::formatItem($mainOrder);
            $shopOrderProto->setPrice($group['main_order_price']);
            $shopOrderProto->setNum($group['main_order_number']);

            // 设置公司信息
            if (isset($companies[$businessId])) {
                $shopOrderProto->setCompanies(HyCompaniesModel::formatItem($companies[$businessId]));
            }

            // 设置产品信息
            if (isset($products[$productId])) {
                $shopOrderProto->setCompaniesService(HyCompaniesServiceModel::formatItem($products[$productId]));
            }

            // 设置多个SKU信息
            if (!empty($group['skus'])) {
                $shopOrderProto->setSkus($group['skus']);
            }

            if (!isset($shopOrderProtoLists[$sn])) {
                $shopOrderProtoLists[$sn] = [];
            }
            $shopOrderProtoLists[$sn][] = $shopOrderProto;
        }

        return $shopOrderProtoLists;
    }

    /**
     * 构建状态查询条件
     * @param $status
     * @return array|null
     */
    public function buildStatusCondition($status): ?array
    {
        return match ($status) {
            OrderStatusEnum::WAIT_PAY => [OrderTable::STATUS, '=', \Generate\Models\Datas\OrderModel::StatusWait_pay],
            OrderStatusEnum::WAIT_DELIVERY => [OrderTable::STATUS, '=', \Generate\Models\Datas\OrderModel::StatusWait_delivery],
            OrderStatusEnum::WAIT_RECEIVE => [OrderTable::STATUS, '=', \Generate\Models\Datas\OrderModel::StatusWait_receive],
            OrderStatusEnum::FINISH => [OrderTable::STATUS, '=', \Generate\Models\Datas\OrderModel::StatusFinish],
            OrderStatusEnum::REFUND => [OrderTable::STATUS, '=', \Generate\Models\Datas\OrderModel::StatusRefund],
            default => null,
        };
    }
} 