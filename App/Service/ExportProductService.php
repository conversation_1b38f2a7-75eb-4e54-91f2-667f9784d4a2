<?php

namespace App\Service;

use Exception;
use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Redis;
use Swlib\Connect\PoolRedis;
use Swlib\Exception\AppException;
use Swlib\Utils\Server;
use Throwable;
use Vtiful\Kernel\Excel;

class ExportProductService
{


    const string PROGRESS_KEY = 'export:product:progress:';


    private array $_companiesNames = [];


    /**
     * @throws AppException
     * @throws Exception
     */
    public static function export(array $data): string
    {
        $filePath = $data['filePath'];
        $taskId = uniqid();
        Server::task([__CLASS__, 'exportTask'], [
            'filePath' => $filePath,
            'taskId' => $taskId,
            'companiesId' => $data['companiesId'] ?? 0
        ]);
        return $taskId;
    }


    /**
     * @throws Throwable
     */
    public function exportTask(array $data): void
    {

        $filePath = $data['filePath'];
        $taskId = $data['taskId'];
        $config = ['path' => PUBLIC_DIR . 'export/'];
        $excel = new Excel($config);

        $file = $excel->fileName(basename($filePath))
            ->gridline(Excel::GRIDLINES_SHOW_ALL)// 设置工作表网格线
            ->freezePanes(1, 0)
            ->setColumn('A:A', 30)
            ->setColumn('B:B', 40)
            ->setColumn('C:C', 30)
            ->setColumn('D:D', 10)
            ->setColumn('E:E', 16)
            ->setColumn('F:F', 16)
            ->header(['公司名称', '产品名称', '生产厂商', '包装规格', '注册/备案号', '医保编码', '是否销售', '型号', '价格', '对应编码', '最小起订量', '库存']);


        $where = [
            HyCompaniesServiceTable::TYPE => HyCompaniesServiceModel::TypeChan_pin
        ];
        if (isset($data['companiesId']) && $data['companiesId']) {
            $where[HyCompaniesServiceTable::COMPANIES_ID] = $data['companiesId'];
        }

        $total = new HyCompaniesServiceTable()->where($where)->count();

        if ($total === 0) {
            $file->output();
            PoolRedis::call(function (Redis $redis) use ($taskId) {
                $key = self::PROGRESS_KEY . $taskId;
                $redis->set($key, 1);
                $redis->expire(self::PROGRESS_KEY . $taskId, 300);
            });
            return;
        }

        $batchSize = 1000;
        $productIds = [];
        $productItems = [];
        $rowIndex = 0;

        /** @var  HyCompaniesServiceTable $item */
        foreach (new HyCompaniesServiceTable()->where($where)->generator() as $index => $item) {
            $productIds[] = $item->id;
            $productItems[] = $item;

            if (count($productIds) >= $batchSize || ($index + 1) === $total) {
                $skusData = new HyProductsSkuTable()->where([
                    [HyProductsSkuTable::PRODUCT_ID, 'in', $productIds]
                ])->selectAll();

                $skusByProductId = [];
                foreach ($skusData as $sku) {
                    $skusByProductId[$sku->productId][] = $sku;
                }

                /** @var HyCompaniesServiceTable $productItem */
                foreach ($productItems as $productItem) {
                    $productSkus = $skusByProductId[$productItem->id] ?? [];

                    if (empty($productSkus)) {
                        $rowIndex = $this->getRowIndex($rowIndex, $file, $productItem);
                    } else {
                        /** @var HyProductsSkuTable $sku */
                        foreach ($productSkus as $sku) {
                            $rowIndex = $this->getRowIndex($rowIndex, $file, $productItem);
                            $file->insertText($rowIndex, 7, $sku->name);
                            $file->insertText($rowIndex, 8, $sku->price);
                            $file->insertText($rowIndex, 9, $sku->code);
                            $file->insertText($rowIndex, 10, $sku->minBuy);
                            $file->insertText($rowIndex, 11, $sku->inventory);
                        }
                    }
                }

                $productIds = [];
                $productItems = [];
            }

            PoolRedis::call(function (Redis $redis) use ($index, $taskId, $total) {
                $key = self::PROGRESS_KEY . $taskId;
                $ratio = ($index + 1) / $total;
                $redis->set($key, $ratio);
                $redis->expire(self::PROGRESS_KEY . $taskId, 300);
            });
        }
        $file->output();

        PoolRedis::call(function (Redis $redis) use ($taskId) {
            $key = self::PROGRESS_KEY . $taskId;
            $redis->set($key, 1);
            $redis->expire(self::PROGRESS_KEY . $taskId, 300);
        });

    }


    /**
     * @throws Throwable
     */
    public static function getProgress(string $taskId)
    {
        return PoolRedis::call(function (Redis $redis) use ($taskId) {
            $ratio = $redis->get(self:: PROGRESS_KEY . $taskId);
            if ($ratio) {
                return $ratio * 100;
            }
            return 0;
        });
    }


    /**
     * @throws Throwable
     */
    private function getCompaniesName($companiesId)
    {
        if (isset($this->_companiesNames[$companiesId])) {
            return $this->_companiesNames[$companiesId];
        }

        $name = new HyCompaniesTable()->where([
            HyCompaniesTable::ID => $companiesId
        ])->selectField(HyCompaniesTable::NAME);

        if ($name) {
            $this->_companiesNames[$companiesId] = $name;
            return $name;
        }


        return '';
    }

    /**
     * @param int $rowIndex
     * @param Excel $file
     * @param HyCompaniesServiceTable $productItem
     * @return int
     * @throws Throwable
     */
    private function getRowIndex(int $rowIndex, Excel $file, HyCompaniesServiceTable $productItem): int
    {
        $rowIndex++;
        $file->insertText($rowIndex, 0, $this->getCompaniesName($productItem->companiesId));
        $file->insertText($rowIndex, 1, $productItem->productName);
        $file->insertText($rowIndex, 2, $productItem->servicePerson);
        $file->insertText($rowIndex, 3, $productItem->unit);
        $file->insertText($rowIndex, 4, $productItem->productNo);
        $file->insertText($rowIndex, 5, $productItem->yiBao);
        $file->insertText($rowIndex, 6, $productItem->isSale ? '销售' : '');
        return $rowIndex;
    }


}