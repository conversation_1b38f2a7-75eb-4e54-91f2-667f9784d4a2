<?php

namespace App\Service;

use Exception;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Generate\Tables\Datas\ShopOrderTable;
use Redis;
use Swlib\Connect\PoolRedis;
use Swlib\Exception\AppException;
use Swlib\Utils\Server;
use Swlib\Table\Expression;
use Throwable;
use Vtiful\Kernel\Excel;

class ExportHyProductsSkuService
{
    const string PROGRESS_KEY = 'export:hy_products_sku:progress:';

    /**
     * @throws AppException
     * @throws Exception
     */
    public static function export(array $data): string
    {
        $filePath = $data['filePath'];
        $taskId = uniqid();
        Server::task([__CLASS__, 'exportTask'], [
            'filePath' => $filePath,
            'taskId' => $taskId,
            'userId' => $data['userId'] ?? 0
        ]);
        return $taskId;
    }

    /**
     * @throws Throwable
     */
    public function exportTask(array $data): void
    {
        $filePath = $data['filePath'];
        $taskId = $data['taskId'];
        $userId = $data['userId'] ?? 0;
        
        $config = ['path' => PUBLIC_DIR . 'export/'];
        $excel = new Excel($config);

        $file = $excel->fileName(basename($filePath))
            ->gridline(Excel::GRIDLINES_SHOW_ALL)
            ->freezePanes(1, 0)
            ->setColumn('A:A', 20)
            ->setColumn('B:B', 30)
            ->setColumn('C:C', 15)
            ->setColumn('D:D', 15)
            ->setColumn('E:E', 15)
            ->setColumn('F:F', 15)
            ->setColumn('G:G', 15)
            ->setColumn('H:H', 15)
            ->setColumn('I:I', 15)
            ->setColumn('J:J', 15)
            ->header([
                '产品名称',
                '规格名称',
                '价格',
                '成本',
                '销售额',
                '毛利',
                '毛利率',
                '购买人数',
                '转化率',
                '库存'
            ]);

        // 获取用户对应的公司ID
        $companiesId = new HyCompaniesTable()->where([
            HyCompaniesTable::USER_ID => $userId,
            HyCompaniesTable::TYPE => 1,
        ])->selectField(HyCompaniesTable::ID);

        if (empty($companiesId)) {
            $file->output();
            PoolRedis::call(function (Redis $redis) use ($taskId) {
                $key = self::PROGRESS_KEY . $taskId;
                $redis->set($key, 1);
                $redis->expire(self::PROGRESS_KEY . $taskId, 300);
            });
            return;
        }

        $where = [
            HyCompaniesServiceTable::COMPANIES_ID => $companiesId,
        ];

        // 获取所有SKU数据
        $total = new HyProductsSkuTable()
            ->join(HyCompaniesServiceTable::TABLE_NAME, HyCompaniesServiceTable::ID, HyProductsSkuTable::PRODUCT_ID)
            ->where($where)
            ->count(HyProductsSkuTable::ID);

        if ($total === 0) {
            $file->output();
            PoolRedis::call(function (Redis $redis) use ($taskId) {
                $key = self::PROGRESS_KEY . $taskId;
                $redis->set($key, 1);
                $redis->expire(self::PROGRESS_KEY . $taskId, 300);
            });
            return;
        }

        $batchSize = 1000;
        $rowIndex = 0;
        $processed = 0;

        // 分批处理数据
        $page = 1;
        while ($processed < $total) {
            $lists = new HyProductsSkuTable()
                ->field(HyProductsSkuTable::FIELD_ALL)
                ->join(HyCompaniesServiceTable::TABLE_NAME, HyCompaniesServiceTable::ID, HyProductsSkuTable::PRODUCT_ID)
                ->where($where)
                ->page($page, $batchSize)
                ->selectAll();

            if (empty($lists)) {
                break;
            }

            // 获取产品ID列表
            $productIds = [];
            $skuIds = [];
            foreach ($lists as $table) {
                $productIds[] = $table->productId;
                $skuIds[] = $table->id;
            }

            // 查询产品名称
            $products = [];
            if ($productIds) {
                $products = new HyCompaniesServiceTable()->where([
                    [HyCompaniesServiceTable::ID, 'in', $productIds],
                ])->formatId2Name(HyCompaniesServiceTable::ID, HyCompaniesServiceTable::PRODUCT_NAME);
            }

            // 查询销售数据
            $saleNums = [];
            $usersBuySkuId = [];
            if ($skuIds) {
                $sumField = ShopOrderTable::getFuncField(ShopOrderTable::NUM, 'SUM');
                $saleNums = new ShopOrderTable()->where([
                    [ShopOrderTable::SKU_ID, 'in', $skuIds],
                ])->group(ShopOrderTable::SKU_ID)->formatId2Name(ShopOrderTable::SKU_ID, $sumField);

                $countField = new Expression('count(DISTINCT shop_order.user_id)');
                $usersBuySkuId = new ShopOrderTable()->where([
                    [ShopOrderTable::SKU_ID, 'in', $skuIds],
                ])->group(ShopOrderTable::SKU_ID)->formatId2Name(ShopOrderTable::SKU_ID, $countField);
            }

            // 写入Excel数据
            foreach ($lists as $table) {
                $rowIndex++;
                
                // 产品名称
                $productName = $products[$table->productId] ?? '';
                
                // 销售额、毛利、毛利率
                $salesVolume = 0;
                $grossProfit = 0;
                $grossProfitMargin = 0;
                
                if (isset($saleNums[$table->id])) {
                    $saleNum = $saleNums[$table->id];
                    $salesVolume = $saleNum * $table->price;
                    $cost = $saleNum * $table->cost;
                    $grossProfit = $salesVolume - $cost;
                    
                    if ($salesVolume > 0) {
                        $grossProfitMargin = ($grossProfit / $salesVolume) * 100;
                    }
                }

                // 购买人数、转化率
                $userNum = 0;
                $conversionRate = 0;
                
                if (isset($usersBuySkuId[$table->id])) {
                    $userNum = $usersBuySkuId[$table->id];
                    if ($table->view > 0) {
                        $conversionRate = ($userNum / $table->view) * 100;
                    }
                }

                // 写入数据行
                $file->insertText($rowIndex, 0, $productName);
                $file->insertText($rowIndex, 1, $table->name);
                $file->insertText($rowIndex, 2, $table->price);
                $file->insertText($rowIndex, 3, $table->cost);
                $file->insertText($rowIndex, 4, $salesVolume);
                $file->insertText($rowIndex, 5, $grossProfit);
                $file->insertText($rowIndex, 6, round($grossProfitMargin, 2) . '%');
                $file->insertText($rowIndex, 7, $userNum);
                $file->insertText($rowIndex, 8, round($conversionRate, 2) . '%');
                $file->insertText($rowIndex, 9, $table->inventory);
            }

            $processed += count($lists);
            $page++;

            // 更新进度
            PoolRedis::call(function (Redis $redis) use ($processed, $total, $taskId) {
                $key = self::PROGRESS_KEY . $taskId;
                $ratio = $processed / $total;
                $redis->set($key, $ratio);
                $redis->expire($key, 300);
            });
        }

        $file->output();

        // 完成导出
        PoolRedis::call(function (Redis $redis) use ($taskId) {
            $key = self::PROGRESS_KEY . $taskId;
            $redis->set($key, 1);
            $redis->expire($key, 300);
        });
    }

    /**
     * @throws Throwable
     */
    public static function getProgress(string $taskId)
    {
        return PoolRedis::call(function (Redis $redis) use ($taskId) {
            $ratio = $redis->get(self::PROGRESS_KEY . $taskId);
            if ($ratio !== false) {
                return (float)$ratio * 100;
            }
            return 0;
        });
    }
}
