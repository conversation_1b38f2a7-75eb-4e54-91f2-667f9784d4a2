<?php

namespace App\Service;

use App\Controller\Api\HuoYuan\HyCompaniesWithdrawalLogApi;
use Generate\Tables\Datas\HyCompaniesWithdrawalLogTable;
use Swlib\Utils\Log;
use Throwable;

/**
 * 微信提现队列处理服务
 * 专门处理微信转账状态查询的队列任务
 */
class WechatWithdrawalQueueService
{
    /**
     * 查询微信转账状态（队列处理方法）
     * 这是一个静态方法，供 MessageQueue 调用
     * 
     * @param array $data 队列数据
     * @return bool true表示任务完成，false表示需要继续重试
     * @throws Throwable
     */
    public static function queryTransferStatus(array $data): bool
    {
        try {
            // 委托给 HyCompaniesWithdrawalLogApi 的静态方法处理
            return HyCompaniesWithdrawalLogApi::queryWechatTransferStatus($data);
        } catch (Throwable $e) {
            $orderNo = $data['order_no'] ?? 'unknown';
            $withdrawalLogId = $data['withdrawal_log_id'] ?? 'unknown';
            
            Log::save("微信提现队列处理异常 - 提现记录ID: {$withdrawalLogId}, 订单号: {$orderNo}, 错误: " . $e->getMessage());
            
            // 如果是严重错误，可以考虑停止重试
            // 这里选择继续重试，让上层逻辑决定是否停止
            throw $e;
        }
    }

    /**
     * 创建微信转账状态查询队列任务
     * 
     * @param int $withdrawalLogId 提现记录ID
     * @param int $companyId 商家ID
     * @param string $orderNo 商户订单号
     * @param float $withdrawalAmount 提现金额
     * @param int $delaySeconds 延迟秒数
     * @return int 队列任务ID
     * @throws Throwable
     */
    public static function createQueryTask(
        int $withdrawalLogId,
        int $companyId,
        string $orderNo,
        float $withdrawalAmount,
        int $delaySeconds = 5
    ): int {
        $queueData = [
            'withdrawal_log_id' => $withdrawalLogId,
            'company_id' => $companyId,
            'order_no' => $orderNo,
            'withdrawal_amount' => $withdrawalAmount,
            'query_count' => 0, // 查询次数计数
            'max_query_count' => 30 // 最大查询次数
        ];

        // 创建延迟队列任务
        $queueId = \Swlib\Queue\MessageQueue::push(
            [self::class, 'queryTransferStatus'],
            $queueData,
            $delaySeconds, // 延迟时间
            30, // 最大执行30次
            [5, 10, 20, 30, 60, 90, 120, 180, 240, 300, 600, 1200, 1800, 3600] // 重试间隔
        );

        Log::save("创建微信转账查询队列任务 - 提现记录ID: {$withdrawalLogId}, 订单号: {$orderNo}, 队列ID: {$queueId}");
        
        return $queueId;
    }

    /**
     * 手动触发转账状态查询
     * 用于管理后台手动重新查询转账状态
     * 
     * @param int $withdrawalLogId 提现记录ID
     * @return array 查询结果
     * @throws Throwable
     */
    public static function manualQueryTransferStatus(int $withdrawalLogId): array
    {
        // 查询提现记录
        $withdrawalLog = new HyCompaniesWithdrawalLogTable()->where([
            [HyCompaniesWithdrawalLogTable::ID, '=', $withdrawalLogId]
        ])->selectOne();

        if (!$withdrawalLog) {
            throw new \Exception('提现记录不存在');
        }

        if ($withdrawalLog->isComplete) {
            return [
                'success' => true,
                'message' => '提现已完成',
                'status' => 'completed'
            ];
        }

        // 解析订单号
        $extStr = $withdrawalLog->extStr ?? '';
        $parts = explode('|', $extStr);
        if (count($parts) < 2) {
            throw new \Exception('提现记录数据异常，无法获取订单号');
        }

        $orderNo = $parts[1];

        // 调用微信查询接口
        $queryResult = WxService::queryTransferByOrderNo($orderNo);

        if (!$queryResult || isset($queryResult['code'])) {
            return [
                'success' => false,
                'message' => '查询失败: ' . ($queryResult['message'] ?? '未知错误'),
                'status' => 'query_failed'
            ];
        }

        $state = $queryResult['state'] ?? '';
        
        // 根据状态处理
        switch ($state) {
            case 'SUCCESS':
                // 转账成功，更新记录
                HyCompaniesWithdrawalLogApi::handleTransferSuccess($withdrawalLog, $queryResult);
                return [
                    'success' => true,
                    'message' => '转账成功',
                    'status' => 'success',
                    'data' => $queryResult
                ];

            case 'FAIL':
            case 'CANCELLED':
                // 转账失败，需要手动处理回滚
                return [
                    'success' => false,
                    'message' => '转账失败: ' . ($queryResult['fail_reason'] ?? '未知原因'),
                    'status' => 'failed',
                    'data' => $queryResult,
                    'need_manual_refund' => true
                ];

            default:
                return [
                    'success' => true,
                    'message' => '转账处理中',
                    'status' => 'processing',
                    'data' => $queryResult
                ];
        }
    }

    /**
     * 获取提现记录的转账状态
     * 
     * @param int $withdrawalLogId 提现记录ID
     * @return array 状态信息
     * @throws Throwable
     */
    public static function getWithdrawalStatus(int $withdrawalLogId): array
    {
        $withdrawalLog = new HyCompaniesWithdrawalLogTable()->where([
            [HyCompaniesWithdrawalLogTable::ID, '=', $withdrawalLogId]
        ])->selectOne();

        if (!$withdrawalLog) {
            throw new \Exception('提现记录不存在');
        }

        return [
            'id' => $withdrawalLog->id,
            'company_id' => $withdrawalLog->companiesId,
            'amount' => $withdrawalLog->score,
            'is_complete' => $withdrawalLog->isComplete,
            'complete_time' => $withdrawalLog->completeTime,
            'complete_msg' => $withdrawalLog->completeMsg,
            'complete_serial_number' => $withdrawalLog->completeSerialNumber,
            'create_time' => $withdrawalLog->time,
            'is_wechat' => $withdrawalLog->isWechat,
            'is_bank' => $withdrawalLog->isBank,
            'is_alipay' => $withdrawalLog->isAlipay
        ];
    }
}
