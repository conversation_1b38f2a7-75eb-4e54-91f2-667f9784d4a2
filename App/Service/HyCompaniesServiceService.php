<?php

namespace App\Service;

use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Swlib\Exception\AppException;
use Throwable;

class HyCompaniesServiceService
{

    /**
     * 表单验证
     * @throws AppException
     * @throws Throwable
     */
    public static function verify(HyCompaniesServiceTable $table): void
    {
        if (empty($table->type)) {
            throw new AppException('请输入服务类型');
        }
        if (empty($table->companiesId)) {
            throw new AppException('请输入厂家ID');
        }
        if (empty($table->userId)) {
            throw new AppException('请输入用户ID');
        }

        self::fillContact($table);

        switch ($table->type) {
            case HyCompaniesServiceModel::TypeJia_gong:
            case HyCompaniesServiceModel::TypeZhong_zhi_ti:
            case HyCompaniesServiceModel::TypeZheng_qi:
            case HyCompaniesServiceModel::TypeYun_yin:
            case HyCompaniesServiceModel::TypeZhuang_xiu:
            case HyCompaniesServiceModel::TypeShe_ji:
            case HyCompaniesServiceModel::TypeQian_fang:
            case HyCompaniesServiceModel::TypeJia_meng:
                self::verifyJiaGong($table);
                break;
            case HyCompaniesServiceModel::TypeXin_wen:
                self::verifyXinWen($table);
                break;
            case HyCompaniesServiceModel::TypeInstall_add:
                self::verifyInstall($table);
                break;
            case HyCompaniesServiceModel::TypeExpo:
                self::verifyExpo($table);
                break;
            case HyCompaniesServiceModel::TypeWx_add:
                self::verifyWeiXiu($table);
                break;
            case HyCompaniesServiceModel::TypeJiao_yu:
                self::verifyJiaoYu($table);
                break;
            case HyCompaniesServiceModel::TypeJing_jia:
                self::verifyJingJia($table);
                break;
            case HyCompaniesServiceModel::TypeZhuan_rang:
                self::verifyZhuanRang($table);
                break;
            case HyCompaniesServiceModel::TypeEr_shou:
                self::verifyErShou($table);
                break;
            case HyCompaniesServiceModel::TypeDa_she_bei:
            case HyCompaniesServiceModel::TypeDai_li:
                self::verifyDaSheBei($table);
                break;
            case HyCompaniesServiceModel::TypeDing_zhi:
                self::verifyDingZhi($table);
                break;
            case HyCompaniesServiceModel::TypeYao_pin:
                self::verifyYaoPin($table);
                break;
            case HyCompaniesServiceModel::TypeJin_rong:
                self::verifyJinRong($table);
                break;
            case HyCompaniesServiceModel::TypeChan_pin:
                self::verifyChanPin($table);
                break;
            default:
                throw new AppException('未知的服务类型' . $table->type);
        }
    }


    /**
     * @throws AppException
     * @throws Throwable
     */
    private static function fillContact(HyCompaniesServiceTable $table): void
    {
        //如果表单中有联系方式，厂家没有，则更新厂家联系方式
        if ($table->contactPhone || $table->contactPerson) {
            $find = new HyCompaniesTable()->field([
                HyCompaniesTable::PHONE,
                HyCompaniesTable::CONTACT_PERSON,
            ])->where([
                HyCompaniesTable::ID => $table->companiesId,
            ])->selectOne();
            $update = [];
            if (empty($find->contactPerson) && $table->contactPerson) {
                $update[HyCompaniesTable::CONTACT_PERSON] = $table->contactPerson;
            }
            if (empty($find->phone) && $table->contactPhone) {
                $update[HyCompaniesTable::PHONE] = $table->contactPhone;
            }
            if ($update) {
                new HyCompaniesTable()->where([
                    HyCompaniesTable::ID => $table->companiesId
                ])->update($update);
            }
        }
        // 如果表单中没有联系方式，则从厂家表中获取
        if (empty($table->contactPerson) || empty($table->contactPhone)) {
            $companiesTable = new HyCompaniesTable()->field([
                HyCompaniesTable::CONTACT_PERSON,
                HyCompaniesTable::PHONE
            ])->where([
                HyCompaniesTable::ID => $table->companiesId
            ])->selectOne();
            if (empty($companiesTable)) {
                throw new AppException('厂家不存在');
            }
            if (empty($table->contactPerson) && $companiesTable->contactPerson) {
                $table->contactPerson = $companiesTable->contactPerson;
            }
            if (empty($table->contactPhone) && $companiesTable->phone) {
                $table->contactPhone = $companiesTable->phone;
            }
        }
    }

    /**
     * 加工表单验证
     * @throws AppException
     */
    private static function verifyJiaGong(HyCompaniesServiceTable $table): void
    {
        if (empty($table->description)) {
            throw new AppException('请输入介绍');
        }

//        if (empty($table->images)) {
//            throw new AppException('请输入图片URL，多个图片用逗号分割存储');
//        }
    }

    /**
     * 新闻表单验证
     * @throws AppException
     */
    private static function verifyXinWen(HyCompaniesServiceTable $table): void
    {
        if (empty($table->productName)) {
            throw new AppException('请输入标题');
        }

//        if (empty($table->images)) {
//            throw new AppException('请输入图片URL，多个图片用逗号分割存储');
//        }
    }

    /**
     * 安装表单验证
     * @throws AppException
     */
    private static function verifyInstall(HyCompaniesServiceTable $table): void
    {
        if (empty($table->productName)) {
            throw new AppException('请输入安装产品');
        }
        if (empty($table->companiesType)) {
            throw new AppException('请输入所属厂家');
        }
    }

    /**
     * 展会验证
     */
    private static function verifyExpo(HyCompaniesServiceTable $table): void
    {

    }

    /**
     * 维修表单验证
     * @throws AppException
     */
    private static function verifyWeiXiu(HyCompaniesServiceTable $table): void
    {
        if (empty($table->productName)) {
            throw new AppException('请输入门诊名称');
        }
        if (empty($table->serviceAddress)) {
            throw new AppException('请输入维修地址');
        }
        if (empty($table->description)) {
            throw new AppException('请输入维修简介');
        }
    }


    /**
     * 竞价表单验证
     * @throws AppException
     */
    private static function verifyJingJia(HyCompaniesServiceTable $table): void
    {
        if (empty($table->productName)) {
            throw new AppException('请输入商品名称');
        }
        if (empty($table->price) || empty($table->priceMax)) {
            throw new AppException('请输入预算范围');
        }

        if (empty($table->description)) {
            throw new AppException('请输入配置要求');
        }


    }

    /**
     * 教育表单验证
     * @throws AppException
     */
    private static function verifyJiaoYu(HyCompaniesServiceTable $table): void
    {
//        if (empty($table->startAt)) {
//            throw new AppException('请输入开课时间');
//        }

        if (empty($table->description)) {
            throw new AppException('请输入介绍');
        }
        if (empty($table->price)) {
            throw new AppException('请输入价格');
        }
        if (empty($table->images)) {
            throw new AppException('请输入课程图片');
        }
        if (empty($table->servicePerson)) {
            throw new AppException('请输入课程图片');
        }
        if (empty($table->serviceDesc)) {
            throw new AppException('请输入讲师简介');
        }
    }

    /**
     * 门诊转让表单验证
     * @throws AppException
     */
    private static function verifyZhuanRang(HyCompaniesServiceTable $table): void
    {

        if (empty($table->description)) {
            throw new AppException('请输入介绍');
        }
        if (empty($table->productNum)) {
            throw new AppException('请输入椅位数');
        }
        if (empty($table->productSize)) {
            throw new AppException('请输入面积');
        }
        if (empty($table->images)) {
            throw new AppException('请输入图片URL，多个图片用逗号分割存储');
        }
    }

    /**
     * 产品验证
     * @throws AppException
     */
    private static function verifyChanPin(HyCompaniesServiceTable $table): void
    {
        if (empty($table->productName)) {
            throw new AppException('请输入产品名称');
        }
        if (empty($table->description)) {
            throw new AppException('请输入产品介绍');
        }
        if (empty($table->images)) {
            throw new AppException('请上传产品图片');
        }
    }

    /**
     * 加工表单验证
     * @throws AppException
     */
    private static function verifyErShou(HyCompaniesServiceTable $table): void
    {

        if (empty($table->description)) {
            throw new AppException('请输入介绍');
        }
        if (empty($table->images)) {
            throw new AppException('请输入图片URL，多个图片用逗号分割存储');
        }
    }

    /**
     * 金融大厅
     * @throws AppException
     */
    private static function verifyJinRong(HyCompaniesServiceTable $table): void
    {
        if (empty($table->fundingNeed)) {
            throw new AppException('请输入资金需求');
        }


        if (empty($table->description)) {
            throw new AppException('请输入介绍');
        }
    }

    /**
     * 定制
     * @throws AppException
     */
    private static function verifyDingZhi(HyCompaniesServiceTable $table): void
    {
        if (empty($table->productName)) {
            throw new AppException('定制产品名称');
        }
        if (empty($table->images)) {
            throw new AppException('产品图片');
        }

        if (empty($table->description)) {
            throw new AppException('请输入介绍');
        }
    }

    /**
     * 药品
     * @throws AppException
     */
    private static function verifyYaoPin(HyCompaniesServiceTable $table): void
    {
        if (empty($table->productName)) {
            throw new AppException('定制产品名称');
        }

        if (empty($table->productNo)) {
            throw new AppException('请输入注册证号');
        }

//        if (empty($table->images)) {
//            throw new AppException('产品图片');
//        }
//
//        if (empty($table->description)) {
//            throw new AppException('请输入介绍');
//        }
    }


    /**
     * 金融大厅
     * @throws AppException
     */
    private static function verifyDaSheBei(HyCompaniesServiceTable $table): void
    {
        if (empty($table->productName)) {
            throw new AppException('请输入产品名称');
        }

        if (empty($table->description)) {
            throw new AppException('请输入介绍');
        }
    }
}