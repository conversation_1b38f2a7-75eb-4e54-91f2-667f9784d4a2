<?php

namespace App\Service;

use AlibabaCloud\SDK\Dysmsapi\V20170525\Dysmsapi;
use AlibabaCloud\SDK\Dyvmsapi\V20170525\Dyvmsapi;
use AlibabaCloud\SDK\Dyvmsapi\V20170525\Models\SingleCallByTtsRequest;
use Exception;
use AlibabaCloud\Tea\Exception\TeaError;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\SendSmsRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use Generate\ConfigEnum;
use Swlib\Exception\AppException;


class AliSmsService
{

    /**
     * 使用AK&SK初始化账号Client
     * @return Dysmsapi Client
     */
    public static function createClient(): Dysmsapi
    {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/311677.html。
        $config = new Config([
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
            "accessKeyId" => ConfigEnum::ALI_ACCESS_KEY_ID,
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
            "accessKeySecret" => ConfigEnum::ALI_ACCESS_KEY_SECRET
        ]);
        // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        $config->endpoint = "dysmsapi.aliyuncs.com";
        return new Dysmsapi($config);
    }

    /**
     * @throws AppException
     */
    public static function send($phone, $code): void
    {
        $client = self::createClient();
        $sendSmsRequest = new SendSmsRequest([
            "phoneNumbers" => $phone,
            "signName" => "四川牙谷福睿网络",
            "templateCode" => "SMS_306870138",
            "templateParam" => "{\"code\":\"$code\"}"
        ]);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $client->sendSmsWithOptions($sendSmsRequest, $runtime);
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw new AppException("发送短信错误 %s", $error->getMessage());
        }
    }

    public static function createYYClient(): Dyvmsapi
    {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/311677.html。
        $config = new Config([
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
            "accessKeyId" => ConfigEnum::ALI_ACCESS_KEY_ID,
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
            "accessKeySecret" => ConfigEnum::ALI_ACCESS_KEY_SECRET
        ]);
        // Endpoint 请参考 https://api.aliyun.com/product/Dyvmsapi
        $config->endpoint = "dyvmsapi.aliyuncs.com";
        return new Dyvmsapi($config);
    }

    /**
     * @throws AppException
     */
    public static function sendYY($phone, $code): void
    {
        $client = self::createYYClient();

        $singleCallByTtsRequest = new SingleCallByTtsRequest([
            "calledShowNumber" => "",
            "calledNumber" => $phone,
            "ttsCode" => "TTS_313445007",
            "ttsParam" => "{\"product\":\"口小白\",\"code\":$code}",
            "playTimes" => 2
        ]);

        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $client->singleCallByTtsWithOptions($singleCallByTtsRequest, $runtime);
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            throw new AppException("发送语音验证码错误 %s", $error->getMessage());
        }
    }
}