<?php

namespace App\Service;

use Generate\Tables\Datas\AddressTable;
use Generate\Tables\Datas\OrderLogisticsTable;
use Generate\Tables\Datas\OrderTable;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Swlib\Exception\AppException;
use Swlib\Utils\Log;
use Throwable;

/**
 * 物流状态查询服务
 * 对物流状态查询进行封装，提供可复用的查询方法
 */
class LogisticsStatusService
{
    /**
     * 查询物流状态
     *
     * @param string $logisticsNumber 物流单号
     * @return array 返回物流状态信息
     * @throws Throwable
     * @throws AppException
     */
    public static function queryLogisticsStatus(string $logisticsNumber): array
    {
        if (empty($logisticsNumber)) {
            throw new AppException('物流单号不能为空');
        }

        // 查询物流记录
        $orderLogisticsTable = new OrderLogisticsTable()
            ->where([
                [OrderLogisticsTable::LOGISTICS_NUMBER, '=', $logisticsNumber],
            ])
            ->selectOne();

        if (empty($orderLogisticsTable)) {
            throw new AppException('物流信息不存在');
        }

        // 如果缓存时间未过期且状态为已签收，直接返回缓存数据
        if ($orderLogisticsTable->lastTime > 0 && 
            ($orderLogisticsTable->lastTime > time() - 3600 || $orderLogisticsTable->status == 'SIGN')) {
            return [
                'logistics_number' => $logisticsNumber,
                'status' => $orderLogisticsTable->status,
                'news' => $orderLogisticsTable->news,
                'last_detail' => $orderLogisticsTable->lastDetail ? json_decode($orderLogisticsTable->lastDetail, true) : null,
                'last_time' => $orderLogisticsTable->lastTime,
                'sn' => $orderLogisticsTable->sn,
                'is_signed' => $orderLogisticsTable->status === 'SIGN'
            ];
        }

        // 获取收货地址信息
        $addId = new OrderTable()->where([
            OrderTable::SN => $orderLogisticsTable->sn,
        ])->selectField(OrderTable::ADDR_ID);

        $phone = new AddressTable()->where([
            AddressTable::ID => $addId
        ])->selectField(AddressTable::PHONE);

        // 调用第三方API查询最新物流状态
        $logisticsData = self::fetchLogisticsFromApi($logisticsNumber, $phone);

        // 更新本地物流状态
        new OrderLogisticsTable()->where([
            OrderLogisticsTable::LOGISTICS_NUMBER => $logisticsNumber,
        ])->update([
            OrderLogisticsTable::LAST_TIME => time(),
            OrderLogisticsTable::NEWS => $logisticsData['theLastMessage'],
            OrderLogisticsTable::LAST_DETAIL => json_encode($logisticsData, JSON_UNESCAPED_UNICODE),
            OrderLogisticsTable::STATUS => $logisticsData['logisticsStatus'],
        ]);

        return [
            'logistics_number' => $logisticsNumber,
            'status' => $logisticsData['logisticsStatus'],
            'news' => $logisticsData['theLastMessage'],
            'last_detail' => $logisticsData,
            'last_time' => time(),
            'sn' => $orderLogisticsTable->sn,
            'is_signed' => $logisticsData['logisticsStatus'] === 'SIGN'
        ];
    }

    /**
     * 批量查询订单的物流状态
     *
     * @param string $sn 订单号
     * @return array 返回订单所有物流包裹的状态
     * @throws Throwable
     */
    public static function queryOrderLogisticsStatus(string $sn): array
    {
        if (empty($sn)) {
            throw new AppException('订单号不能为空');
        }

        // 获取订单所有物流记录
        $logisticsRecords = new OrderLogisticsTable()
            ->where([
                OrderLogisticsTable::SN => $sn
            ])
            ->selectAll();

        if (empty($logisticsRecords)) {
            return [];
        }

        $results = [];
        foreach ($logisticsRecords as $record) {
            try {
                $status = self::queryLogisticsStatus($record->logisticsNumber);
                $results[] = $status;
            } catch (Throwable $e) {
                // 记录错误但继续处理其他包裹
                Log::save("查询物流状态失败 - 物流单号: $record->logisticsNumber, 错误: " . $e->getMessage());
            }
        }

        return $results;
    }

    /**
     * 检查订单是否全部已签收
     *
     * @param string $sn 订单号
     * @return bool true表示全部已签收，false表示未全部签收
     * @throws Throwable
     */
    public static function isOrderFullySigned(string $sn): bool
    {
        $logisticsStatuses = self::queryOrderLogisticsStatus($sn);
        
        if (empty($logisticsStatuses)) {
            return false;
        }

        return array_all($logisticsStatuses, fn($status) => $status['is_signed']);

    }

    /**
     * 获取订单的签收状态统计
     *
     * @param string $sn 订单号
     * @return array 返回签收状态统计
     * @throws Throwable
     */
    public static function getOrderSignedStatistics(string $sn): array
    {
        $logisticsStatuses = self::queryOrderLogisticsStatus($sn);
        
        $total = count($logisticsStatuses);
        $signed = 0;
        $unsigned = 0;

        foreach ($logisticsStatuses as $status) {
            if ($status['is_signed']) {
                $signed++;
            } else {
                $unsigned++;
            }
        }

        return [
            'sn' => $sn,
            'total_packages' => $total,
            'signed_packages' => $signed,
            'unsigned_packages' => $unsigned,
            'is_fully_signed' => $unsigned === 0 && $total > 0,
            'sign_rate' => $total > 0 ? round($signed / $total * 100, 2) : 0
        ];
    }

    /**
     * 从第三方API获取物流信息
     *
     * @param string $expressNo 快递单号
     * @param string $phone 手机号
     * @return array 物流数据
     * @throws AppException|GuzzleException
     */
    public static function fetchLogisticsFromApi(string $expressNo, string $phone): array
    {
        $url = "https://kzexpress.market.alicloudapi.com/api-mall/api/express/query";
        $params = [
            'expressNo' => $expressNo,
            'mobile' => $phone
        ];

        $client = new Client();
        $response = $client->get($url, [
            'headers' => [
                'Authorization' => 'APPCODE eaaae86d994f40d5a38882e28be3fda6'
            ],
            'query' => $params
        ]);

        $res = $response->getBody()->getContents();
        $arr = json_decode($res, true);

        if (empty($arr) || !isset($arr['data'])) {
            throw new AppException('获取物流信息失败');
        }

        return $arr['data'];
    }

    /**
     * 获取物流状态的中文描述
     *
     * @param string $status 物流状态
     * @return string 中文描述
     */
    public static function getStatusDescription(string $status): string
    {
        $statusMap = [
            'WAIT_ACCEPT' => '待揽收',
            'ACCEPT' => '已揽收',
            'TRANSPORT' => '运输中',
            'DELIVERING' => '派件中',
            'AGENT_SIGN' => '已代签收',
            'SIGN' => '已签收',
            'FAILED' => '包裹异常'
        ];

        return $statusMap[$status] ?? '未知状态';
    }
}
