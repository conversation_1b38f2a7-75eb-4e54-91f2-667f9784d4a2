<?php

namespace App\Service;


use Generate\Tables\Datas\OrderLogisticsTable;
use Generate\Tables\Datas\ShopOrderTable;
use Swlib\Queue\MessageQueue;
use Swlib\Utils\Log;
use Throwable;

/**
 * 订单自动确认收货服务
 * 处理延迟消息队列，实现自动确认收货功能
 */
class OrderAutoConfirmService
{
    /**
     * 检查订单物流状态并决定下一步操作
     * 这是延迟消息队列的入口方法
     *
     * @param array $data 消息数据，包含订单号等信息
     * @return bool true表示任务完成，false表示需要重试
     * @throws Throwable
     */
    public function checkOrderLogisticsStatus(array $data): bool
    {
        try {
            $sn = $data['sn'] ?? '';
            $checkCount = $data['check_count'] ?? 0; // 检查次数
            $maxCheckCount = $data['max_check_count'] ?? 30; // 最大检查次数（30天）

            if (empty($sn)) {
                Log::save("订单自动确认收货 - 订单号为空", 'order_auto_confirm');
                return true; // 参数错误，不再重试
            }

            Log::save("开始检查订单物流状态 - 订单号: $sn, 检查次数: $checkCount", 'order_auto_confirm');

            // 检查订单是否已经完成
            if ($this->isOrderCompleted($sn)) {
                Log::save("订单已完成，无需继续检查 - 订单号: $sn", 'order_auto_confirm');
                return true;
            }

            // 检查是否超过最大检查次数
            if ($checkCount >= $maxCheckCount) {
                Log::save("订单检查次数超过限制，停止检查 - 订单号: $sn, 检查次数: $checkCount", 'order_auto_confirm');
                return true;
            }

            // 查询订单物流状态
            $signedStatistics = LogisticsStatusService::getOrderSignedStatistics($sn);
            
            Log::save("订单物流状态统计 - 订单号: $sn, 统计: " . json_encode($signedStatistics), 'order_auto_confirm');

            if ($signedStatistics['is_fully_signed']) {
                // 全部已签收，延迟7天自动确认收货
                $this->scheduleAutoConfirm($sn);
                Log::save("订单全部已签收，已安排7天后自动确认收货 - 订单号: $sn", 'order_auto_confirm');
            } else {
                // 未全部签收，1天后再次检查
                $this->scheduleNextCheck($sn, $checkCount + 1, $maxCheckCount);
                Log::save("订单未全部签收，已安排1天后再次检查 - 订单号: $sn", 'order_auto_confirm');
            }
            return true;

        } catch (Throwable $e) {
            Log::saveException($e, 'order_auto_confirm');
            return false; // 发生异常，需要重试
        }
    }

    /**
     * 自动确认收货
     * 延迟消息队列调用的自动确认收货方法
     *
     * @param array $data 消息数据
     * @return bool true表示任务完成，false表示需要重试
     * @throws Throwable
     */
    public function autoConfirmReceiving(array $data): bool
    {
        try {
            $sn = $data['sn'] ?? '';
            $logisticsNumber = $data['logistics_number'] ?? '';
            $skuId = $data['sku_id'] ?? 0;

            if (empty($sn) || empty($logisticsNumber) || empty($skuId)) {
                Log::save("自动确认收货 - 参数错误: " . json_encode($data), 'order_auto_confirm');
                return true; // 参数错误，不再重试
            }

            Log::save("开始自动确认收货 - 订单号: $sn, 物流单号: $logisticsNumber, SKU ID: $skuId", 'order_auto_confirm');

            // 检查订单是否已经完成
            if ($this->isOrderCompleted($sn)) {
                Log::save("订单已完成，无需自动确认收货 - 订单号: $sn", 'order_auto_confirm');
                return true;
            }

            // 再次检查物流状态，确保仍然是已签收状态
            $logisticsStatus = LogisticsStatusService::queryLogisticsStatus($logisticsNumber);
            if (!$logisticsStatus['is_signed']) {
                Log::save("物流状态已变更为未签收，取消自动确认收货 - 订单号: $sn, 物流单号: $logisticsNumber", 'order_auto_confirm');
                return true;
            }

            // 执行确认收货
            $orderLogisticsService = new OrderLogisticsService();
            $result = $orderLogisticsService->confirmReceiving($sn, $skuId, $logisticsNumber);

            if ($result) {
                Log::save("自动确认收货成功 - 订单号: $sn, 物流单号: $logisticsNumber, SKU ID: $skuId", 'order_auto_confirm');
                return true;
            } else {
                Log::save("自动确认收货失败 - 订单号: $sn, 物流单号: $logisticsNumber, SKU ID: $skuId", 'order_auto_confirm');
                return false; // 确认收货失败，需要重试
            }

        } catch (Throwable $e) {
            Log::saveException($e, 'order_auto_confirm');
            return false; // 发生异常，需要重试
        }
    }

    /**
     * 安排下次检查物流状态
     *
     * @param string $sn 订单号
     * @param int $checkCount 检查次数
     * @param int $maxCheckCount 最大检查次数
     * @throws Throwable
     */
    private function scheduleNextCheck(string $sn, int $checkCount, int $maxCheckCount): void
    {
        $delayTime = 86400; // 1天后检查

        MessageQueue::push(
            [self::class, 'checkOrderLogisticsStatus'],
            [
                'sn' => $sn,
                'check_count' => $checkCount,
                'max_check_count' => $maxCheckCount
            ],
            $delayTime,
            3 // 最大重试3次
        );
    }

    /**
     * 安排自动确认收货
     *
     * @param string $sn 订单号
     * @throws Throwable
     */
    private function scheduleAutoConfirm(string $sn): void
    {
        // 获取订单的所有物流记录
        $logisticsRecords = new OrderLogisticsTable()
            ->where([
                OrderLogisticsTable::SN => $sn,
                OrderLogisticsTable::CONFIRM_RECEIPT => 0 // 未确认收货的
            ])
            ->selectAll();

        $delayTime = 86400 * 7; // 7天后自动确认收货

        foreach ($logisticsRecords as $record) {
            MessageQueue::push(
                [self::class, 'autoConfirmReceiving'],
                [
                    'sn' => $sn,
                    'logistics_number' => $record->logisticsNumber,
                    'sku_id' => $record->skuId
                ],
                $delayTime,
                3 // 最大重试3次
            );
        }
    }

    /**
     * 检查订单是否已经完成
     *
     * @param string $sn 订单号
     * @return bool true表示已完成，false表示未完成
     * @throws Throwable
     */
    private function isOrderCompleted(string $sn): bool
    {
        // 检查是否所有物流记录都已确认收货
        $unconfirmedCount = new OrderLogisticsTable()
            ->where([
                OrderLogisticsTable::SN => $sn,
                OrderLogisticsTable::CONFIRM_RECEIPT => 0
            ])
            ->count();

        return $unconfirmedCount === 0;
    }

    /**
     * 获取订单的发货统计信息
     *
     * @param string $sn 订单号
     * @return array 发货统计信息
     * @throws Throwable
     */
    public function getOrderShippingStatistics(string $sn): array
    {
        // 获取订单商品信息
        $shopOrders = new ShopOrderTable()
            ->where([ShopOrderTable::SN => $sn])
            ->selectAll();

        $totalQuantity = 0;
        $shippedQuantity = 0;

        foreach ($shopOrders as $order) {
            $totalQuantity += $order->num;
            $shippedQuantity += $order->quantityShipped;
        }

        $isFullyShipped = $totalQuantity > 0 && $shippedQuantity >= $totalQuantity;

        return [
            'sn' => $sn,
            'total_quantity' => $totalQuantity,
            'shipped_quantity' => $shippedQuantity,
            'unshipped_quantity' => $totalQuantity - $shippedQuantity,
            'is_fully_shipped' => $isFullyShipped,
            'shipping_rate' => $totalQuantity > 0 ? round($shippedQuantity / $totalQuantity * 100, 2) : 0
        ];
    }

    /**
     * 启动订单自动确认收货流程
     * 在订单全部发货后调用此方法
     *
     * @param string $sn 订单号
     * @throws Throwable
     */
    public static function startAutoConfirmProcess(string $sn): void
    {
        $delayTime = 86400 * 3; // 3天后开始检查

        MessageQueue::push(
            [__CLASS__, 'checkOrderLogisticsStatus'],
            [
                'sn' => $sn,
                'check_count' => 0,
                'max_check_count' => 30 // 最多检查30次（30天）
            ],
            $delayTime,
            3 // 最大重试3次
        );

        Log::save("已启动订单自动确认收货流程 - 订单号: $sn, 3天后开始检查", 'order_auto_confirm');
    }
}
