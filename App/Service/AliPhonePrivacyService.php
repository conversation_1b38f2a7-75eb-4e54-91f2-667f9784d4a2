<?php

namespace App\Service;

use AlibabaCloud\SDK\Dyplsapi\V20170525\Dyplsapi;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\BindAxgRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\CreateAxgGroupRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\DeleteAxgGroupRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\OperateAxgGroupRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\QuerySubsIdRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\UnbindSubscriptionRequest;
use Darabonba\OpenApi\Models\Config;
use Exception;
use Generate\ConfigEnum;

use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use Generate\Tables\Datas\PrivacyPhoneTable;
use Swlib\Exception\AppException;
use Swlib\Queue\MessageQueue;
use Throwable;

/**
 * 阿里云号码隐私服务为
 */
class AliPhonePrivacyService
{

    const string POOL_KEY = "FC100000183284003";

    /**
     * 使用AK&SK初始化账号Client
     * @return Dyplsapi Client
     */
    public static function createClient(): Dyplsapi
    {
        $config = new Config([
            "accessKeyId" => ConfigEnum::ALI_ACCESS_KEY_ID,
            "accessKeySecret" => ConfigEnum::ALI_ACCESS_KEY_SECRET
        ]);
        // Endpoint 请参考 https://api.aliyun.com/product/Dyplsapi
        $config->endpoint = "dyplsapi.aliyuncs.com";
        return new Dyplsapi($config);
    }


    /**
     * 获取号码组 ID
     * @param $xPhone
     * @return int|mixed
     * @throws AppException
     * @throws Throwable
     */
    public static function getGid($xPhone): mixed
    {
        $gid = new PrivacyPhoneTable()->where([
            PrivacyPhoneTable::PRIVACY_PHONE => $xPhone
        ])->selectField(PrivacyPhoneTable::G_ID);
        if (empty($gid)) {
            $gid = self::groupCreate($xPhone);
        }
        return $gid;
    }


    /**
     * 创建号码组
     * @param $privacy_phone
     * @return int
     * @throws AppException
     * @throws Throwable
     */
    public static function groupCreate($privacy_phone): int
    {
        $client = self::createClient();
        $createAxgGroupRequest = new CreateAxgGroupRequest([
            "poolKey" => "FC100000183284003",
            // 隐私号码作为号码组名称
            "name" => $privacy_phone
        ]);

        $runtime = new RuntimeOptions([]);
        // 复制代码运行请自行打印 API 的返回值
        $r = $client->createAxgGroupWithOptions($createAxgGroupRequest, $runtime);
        if ($r->statusCode !== 200) {
            throw new AppException("操作号码组失败 %s", $r->body->message);
        }
        if ($r->body->code != 'OK') {
            throw new AppException("操作号码组失败 %s", $r->body->message);
        }

        new PrivacyPhoneTable()->where([
            PrivacyPhoneTable::PRIVACY_PHONE => $privacy_phone
        ])->update([
            PrivacyPhoneTable::G_ID => $r->body->groupId
        ]);

        return $r->body->groupId;
    }


    /**
     * 创建号码组
     * @param $privacy_phone
     * @throws AppException
     * @throws Throwable
     */
    public static function groupDelete($privacy_phone): void
    {
        $client = self::createClient();
        $createAxgGroupRequest = new DeleteAxgGroupRequest([
            "poolKey" => "FC100000183284003",
            // 隐私号码作为号码组名称
            "groupId" => $privacy_phone
        ]);

        $runtime = new RuntimeOptions([]);
        // 复制代码运行请自行打印 API 的返回值
        $r = $client->deleteAxgGroupWithOptions($createAxgGroupRequest, $runtime);
        if ($r->statusCode !== 200) {
            throw new AppException("操作号码组失败 %s", $r->body->message);
        }
        if ($r->body->code != 'OK') {
            throw new AppException("操作号码组失败 %s", $r->body->message);
        }
    }


    /**
     * @param $xPhone
     * @param $bPhone
     * @param bool $add
     * @return true
     * @throws AppException
     * @throws Throwable
     */
    public static function groupAction($xPhone, $bPhone, bool $add = true): bool
    {
        $client = self::createClient();
        $operateAxgGroupRequest = new OperateAxgGroupRequest([
            "poolKey" => self::POOL_KEY,
            "groupId" => self::getGid($xPhone),
            "operateType" => $add ? "addNumbers" : 'deleteNumbers', // deleteNumbers：删除号码。
            "numbers" => "$bPhone"
        ]);
        $runtime = new RuntimeOptions([]);
        // 复制代码运行请自行打印 API 的返回值
        $r = $client->operateAxgGroupWithOptions($operateAxgGroupRequest, $runtime);

        if ($r->statusCode !== 200) {
            throw new AppException("操作号码组失败 %s", $r->body->message);
        }
        if ($r->body->code != 'OK') {
            throw new AppException("操作号码组失败 %s", $r->body->message);
        }
        return true;
    }

    /**
     * @throws AppException|Throwable
     */
    public static function bind($xPhone, $aPhone, $bPhone): bool
    {
        $client = self::createClient();
        $bindAxgRequest = new BindAxgRequest([
            "poolKey" => self::POOL_KEY,
            "phoneNoA" => $aPhone,
            "groupId" => self::getGid($xPhone),
            "phoneNoX" => $xPhone,
            "phoneNoB" => $bPhone,
            "expiration" => date('Y-m-d H:i:s', time() + 3600)
        ]);
        $runtime = new RuntimeOptions([]);
        $r = self::groupAction($xPhone, $bPhone);
        if (empty($r)) {
            throw new AppException('添加到分组失败');
        }

        // 复制代码运行请自行打印 API 的返回值
        $r = $client->bindAxgWithOptions($bindAxgRequest, $runtime);

        if ($r->statusCode !== 200) {
            throw new AppException("绑定号码失败 %s", $r->body->message);
        }
        if ($r->body->code != 'OK') {
            throw new AppException("绑定号码失败 %s", $r->body->message);
        }

        $subsId = $r->body->secretBindDTO->subsId;

        if (empty($subsId)) {
            throw new AppException("绑定号码失败 没有找到绑定关系ID");
        }


        $updateRes = new PrivacyPhoneTable()->where([
            PrivacyPhoneTable::PRIVACY_PHONE => $xPhone
        ])->update([
            PrivacyPhoneTable::SEND_PHONE => $aPhone,
            PrivacyPhoneTable::ANSWER_PHONE => $bPhone,
            PrivacyPhoneTable::SUBS_ID => $subsId,
            PrivacyPhoneTable::BIND_TIME => time()
        ]);

        return (bool)$updateRes;
    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    public static function unBind($subsId, $bPhone, $xPhone): bool
    {

        // 删除云端
        self::groupAction($xPhone, $bPhone, false);

        if ($subsId) {
            $runtime = new RuntimeOptions([]);

            $client = self::createClient();
            $unbindSubscriptionRequest = new UnbindSubscriptionRequest([
                "poolKey" => self::POOL_KEY,
                "subsId" => $subsId,
                "secretNo" => $xPhone
            ]);

            // 复制代码运行请自行打印 API 的返回值
            $r = $client->unbindSubscriptionWithOptions($unbindSubscriptionRequest, $runtime);

            if ($r->statusCode !== 200) {
                throw new AppException("绑定号码失败 %s", $r->body->message);
            }
            if ($r->body->code != 'OK') {
                throw new AppException("绑定号码失败 %s", $r->body->message);
            }
        }

        $dbUpdate[PrivacyPhoneTable::SUBS_ID] = '';
        $dbUpdate[PrivacyPhoneTable::BIND_TIME] = 0;
        $dbUpdate[PrivacyPhoneTable::SEND_PHONE] = '';
        $dbUpdate[PrivacyPhoneTable::ANSWER_PHONE] = '[]';


        new PrivacyPhoneTable()->where([
            PrivacyPhoneTable::PRIVACY_PHONE => $xPhone
        ])->update($dbUpdate);

        return true;
    }


    /**
     * @throws Throwable
     * @throws AppException
     */
    public static function getXPhone($aPhone, $bPhone): string
    {
        $find = new PrivacyPhoneTable()->where([
            [PrivacyPhoneTable::SEND_PHONE, '=', $aPhone],
            [PrivacyPhoneTable::ANSWER_PHONE, 'like', "%$bPhone%"],
        ])->selectOne();
        if ($find) {
            // 如果已经存在了 就更新时间
            new PrivacyPhoneTable()->where([
                PrivacyPhoneTable::ID => $find->id,
            ])->update([
                PrivacyPhoneTable::BIND_TIME => time()
            ]);
            return $find->privacyPhone;
        }

        // 查找到最久没有使用的隐私号码
        /** @var PrivacyPhoneTable $find */
        $find = new PrivacyPhoneTable()->order([
            PrivacyPhoneTable::BIND_TIME => 'asc'
        ])->selectOne();

        $xPhone = $find->privacyPhone;
        if ($find->subsId) {
            $r = self::unBind($find->subsId, $bPhone, $xPhone);
            if ($r !== true) {
                throw new AppException("解绑失败");
            }
        }

        $r = self::bind($xPhone, $aPhone, $bPhone);

        if ($r !== true) {
            throw new AppException("绑定失败");
        }
        MessageQueue::push([__CLASS__, 'queueUnBind'], [], 3600);
        return $xPhone;
    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    public function queueUnBind(): true
    {
        $all = new PrivacyPhoneTable()->where([
            [PrivacyPhoneTable::BIND_TIME, '<', time() - 3600],
            [PrivacyPhoneTable::BIND_TIME, '>', 0]
        ])->selectAll();

        foreach ($all as $item) {
            try {
                AliPhonePrivacyService::unBind($item->subsId, $item->answerPhone, $item->privacyPhone);
            } catch (Throwable $e) {
                var_dump($e->getMessage());
                var_dump($e->getTraceAsString());
                // 解绑失败，获取最新的subsId，然后下一轮再次解绑
                AliPhonePrivacyService::getSubsId($item->privacyPhone);
            }
        }
        return true;
    }


    /**
     * @throws Throwable
     * @throws AppException
     */
    public static function getSubsId($xPhone): void
    {
        $client = self::createClient();
        $querySubsIdRequest = new QuerySubsIdRequest([
            "poolKey" => self::POOL_KEY,
            "phoneNoX" => $xPhone
        ]);

        $runtime = new RuntimeOptions([]);
        // 复制代码运行请自行打印 API 的返回值
        $r = $client->querySubsIdWithOptions($querySubsIdRequest, $runtime);


        if ($r->statusCode !== 200) {
            throw new AppException("绑定号码失败 %s", $r->body->message);
        }
        if ($r->body->code != 'OK') {
            throw new AppException("绑定号码失败 %s", $r->body->message);
        }

        $subsId = $r->body->subsId;

        if ($subsId) {
            new PrivacyPhoneTable()->where([
                PrivacyPhoneTable::PRIVACY_PHONE => $xPhone
            ])->update([
                PrivacyPhoneTable::SUBS_ID => $subsId,
                PrivacyPhoneTable::BIND_TIME => time() - 3600,
            ]);
        }
    }


}