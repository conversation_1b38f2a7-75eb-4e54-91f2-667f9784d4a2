<?php

namespace App\Service;

use App\Controller\Api\Dt\HyCompaniesServiceApi;
use Exception;
use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Redis;
use Swlib\Connect\PoolRedis;
use Swlib\Exception\AppException;
use Swlib\Utils\File;
use Swlib\Utils\Log;
use Swlib\Utils\Server;
use Throwable;
use Vtiful\Kernel\Excel;

class ImportProductService
{


    const string UPLOAD_DIR = RUNTIME_DIR . 'import/product/';
    const string PROGRESS_KEY = 'import:product:progress:';


    private array $_companiesId = [];
    private array $userIds = [];


    /**
     * @throws AppException
     * @throws Exception
     */
    public static function upload(): string
    {
        $filePath = File::upload(self::UPLOAD_DIR);

        $taskId = uniqid();

        $config = ['path' => ''];
        $excel = new Excel($config);
        $excel = $excel->openFile($filePath)
            ->openSheet();
        $total = 0;
        while (($excel->nextRow()) !== NULL) {
            $total++;
        }


        Server::task([__CLASS__, 'uploadExcel'], [
            'filePath' => $filePath,
            'taskId' => $taskId,
            'total' => $total,
        ]);

        return $taskId;
    }

    /**
     * @throws Throwable
     */
    public static function getProgress(string $taskId)
    {
        return PoolRedis::call(function (Redis $redis) use ($taskId) {
            $ratio = $redis->get(self:: PROGRESS_KEY . $taskId);
            if ($ratio) {
                return $ratio * 100;
            }
            return 0;
        });
    }

    /**
     * 处理Excel文件上传并写入数据库
     *
     */
    public function uploadExcel($data): void
    {
        $filePath = $data['filePath'];
        $taskId = $data['taskId'];
        $total = $data['total'];

        try {
            $config = ['path' => ''];
            $excel = new Excel($config);

            // 读取测试文件
            $excel = $excel->openFile($filePath)
                ->openSheet();


            $index = -1;

            while (($row = $excel->nextRow()) !== NULL) {
                $index++;

                PoolRedis::call(function (Redis $redis) use ($index, $taskId, $total) {
                    $key = self:: PROGRESS_KEY . $taskId;
                    $cacheRatio = $redis->get($key);
                    if ($cacheRatio == 1) {
                        return;
                    }
                    $ratio = $index / $total;
                    $currRatio = max($cacheRatio, $ratio);
                    $redis->set($key, $currRatio);
                    $redis->expire(self:: PROGRESS_KEY . $taskId, 300);
                });

                if ($index === 0) {
                    continue;
                }

                try {
                    $companiesName = trim($row[0]);
                    $productName = trim($row[1]);
                    $servicePerson = trim($row[2]);// 生产厂商
                    $unit = trim($row[3]);// 包装规格
                    $productNo = trim($row[4]); // 注册号 ，备案号
                    $yiBao = trim($row[5]); //医保编码
                    $isSale = trim($row[6]); //是否销售
                    $skuName = trim($row[7]); //型号
                    $price = round(trim($row[8] ?: 0), 2); //价格
                    $code = trim($row[9] ?: 0); //对应编码
                    $minBuyNum = ceil(trim($row[10] ?: 0)); //最小起订量
                    $inventory = ceil(trim($row[11] ?: 0)); //库存


                    $isSale = $isSale == '销售' ? 1 : 0;

                    if (
                        empty($companiesName)
                        || empty($productName)
                        || empty($skuName)
                    ) {
                        continue;
                    }

                    $companiesId = $this->getCompaniesId($companiesName);
                    if (empty($companiesId)) {
                        continue;
                    }

                    $productId = $this->getProductId($companiesId, $productName, $servicePerson, $unit, $productNo, $yiBao, $isSale);
                    $this->saveSku($price, $minBuyNum, $inventory, $productId, $skuName, $code);
                } catch (Throwable $e) {
                    Log::saveException($e, 'import_product');
                }
            }


            PoolRedis::call(function (Redis $redis) use ($taskId) {
                $key = self:: PROGRESS_KEY . $taskId;
                $redis->set($key, 1);
                $redis->expire(self:: PROGRESS_KEY . $taskId, 300);
            });

        } catch (Throwable $e) {
            Log::saveException($e, 'import_product');
        }
    }


    /**
     * @throws Throwable
     */
    private function saveSku($price, $minBuyNum, $inventory, $productId, $skuName, $code): void
    {
        $id = new HyProductsSkuTable()->where([
            HyProductsSkuTable::PRODUCT_ID => $productId,
            HyProductsSkuTable::NAME => $skuName,
        ])->selectField(HyProductsSkuTable::ID);
        if (empty($id)) {
            new HyProductsSkuTable()->insert([
                HyProductsSkuTable::PRODUCT_ID => $productId,
                HyProductsSkuTable::NAME => $skuName,
                HyProductsSkuTable::PRICE => $price,
                HyProductsSkuTable::MIN_BUY => $minBuyNum,
                HyProductsSkuTable::CODE => $code,
                HyProductsSkuTable::INVENTORY => $inventory,
            ]);
        } else {
            new HyProductsSkuTable()->where([
                HyProductsSkuTable::PRODUCT_ID => $productId,
                HyProductsSkuTable::NAME => $skuName,
            ])->update([
                HyProductsSkuTable::PRICE => $price,
                HyProductsSkuTable::MIN_BUY => $minBuyNum,
                HyProductsSkuTable::CODE => $code,
                HyProductsSkuTable::INVENTORY => $inventory,
            ]);
        }

    }


    /**
     * @throws Throwable
     */
    private function getCompaniesId($name)
    {
        if (isset($this->_companiesId[$name])) {
            return $this->_companiesId[$name];
        }

        $id = new HyCompaniesTable()->where([
            HyCompaniesTable::NAME => $name
        ])->selectField(HyCompaniesTable::ID);

        if ($id) {
            $this->_companiesId[$name] = $id;
            return $id;
        }

        $id = new HyCompaniesTable()->insert([
            HyCompaniesTable::NAME => $name
        ]);

        if ($id) {
            $this->_companiesId[$name] = $id;
            return $id;
        }

        return 0;
    }

    /**
     * @throws Throwable
     */
    private function getProductId($companiesId, $name, $servicePerson, $unit, $productNo, $yiBao, $isSale): ?int
    {
        $product = new HyCompaniesServiceTable()->where([
            HyCompaniesServiceTable::COMPANIES_ID => $companiesId,
            HyCompaniesServiceTable::PRODUCT_NAME => $name,
            HyCompaniesServiceTable::TYPE => HyCompaniesServiceModel::TypeChan_pin
        ])->selectOne();

        if ($product) {
            $updateData = [];

            if ($product->servicePerson !== $servicePerson) {
                $updateData[HyCompaniesServiceTable::SERVICE_PERSON] = $servicePerson;
            }
            if ($product->unit !== $unit) {
                $updateData[HyCompaniesServiceTable::UNIT] = $unit;
            }
            if ($product->productNo !== $productNo) {
                $updateData[HyCompaniesServiceTable::PRODUCT_NO] = $productNo;
            }
            if ($product->yiBao !== $yiBao) {
                $updateData[HyCompaniesServiceTable::YI_BAO] = $yiBao;
            }
            if ($product->isSale != $isSale) {
                $updateData[HyCompaniesServiceTable::IS_SALE] = $isSale;
            }

            if (!empty($updateData)) {
                new HyCompaniesServiceTable()->where([
                    HyCompaniesServiceTable::ID => $product->id
                ])->update($updateData);

                $ctrl = new HyCompaniesServiceApi();
                $ctrl->countSaleNum(['companiesId' => $companiesId]);
            }

            return $product->id;
        }

        $id = new HyCompaniesServiceTable()->insert([
            HyCompaniesServiceTable::COMPANIES_ID => $companiesId,
            HyCompaniesServiceTable::TYPE => HyCompaniesServiceModel::TypeChan_pin,
            HyCompaniesServiceTable::PRODUCT_NAME => $name,
            HyCompaniesServiceTable::SERVICE_PERSON => $servicePerson,
            HyCompaniesServiceTable::UNIT => $unit,
            HyCompaniesServiceTable::PRODUCT_NO => $productNo,
            HyCompaniesServiceTable::YI_BAO => $yiBao,
            HyCompaniesServiceTable::IS_SALE => $isSale,
            HyCompaniesServiceTable::USER_ID => $this->getUserId($companiesId),
        ]);


        $ctrl = new HyCompaniesServiceApi();
        $ctrl->countSaleNum(['companiesId' => $companiesId]);

        if ($id) {
            return $id;
        }

        return 0;
    }

    /**
     * @throws Throwable
     */
    private function getUserId(int $companiesId)
    {
        if (isset($this->_userIds[$companiesId])) {
            return $this->userIds[$companiesId];
        }
        $userId = new HyCompaniesTable()->where([
            HyCompaniesTable::ID => $companiesId,
        ])->selectField(HyCompaniesTable::USER_ID);

        $this->userIds[$companiesId] = $userId;

        return $userId;

    }

}