<?php

namespace App\Service;

use Generate\Models\Datas\HyCompaniesServiceModel;
use Generate\Models\Datas\HyProductsSkuModel;
use Generate\Models\Datas\OrderLogisticsModel;
use Generate\Models\Datas\OrderModel;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Generate\Tables\Datas\OrderLogisticsTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\ShopOrderTable;
use Protobuf\Datas\OrderLogistics\OrderLogisticsProto;
use Swlib\Exception\AppException;
use Swlib\Table\Db;
use Swlib\Utils\Log;
use Throwable;

/**
 * 订单物流服务类
 */
class OrderLogisticsService
{
    /**
     * 更新发货数量
     * @throws Throwable
     * @throws AppException
     */
    public function updateReceiveNum($sn, $skuId, $num): void
    {
        $shopOrder = new ShopOrderTable()->where([
            ShopOrderTable::SKU_ID => $skuId,
            ShopOrderTable::SN => $sn,
        ])->selectOne();
        // 剩余未发货数量 是  下单数量 - 已经发货数量 - 已经退款数量
        $lastNum = $shopOrder->num - $shopOrder->quantityShipped - $shopOrder->returnedQuantity;
        if ($num > $lastNum) {
            throw new AppException('剩余未发货数量' . $lastNum);
        }

        // 更新已经发货数量
        new ShopOrderTable()->where([
            ShopOrderTable::ID => $shopOrder->id
        ])->update([
            ShopOrderTable::QUANTITY_SHIPPED => Db::incr(ShopOrderTable::QUANTITY_SHIPPED, $num),
        ]);
    }

    /**
     * 更新订单状态
     * @param $sn
     * @return void
     * @throws Throwable
     */
    public function updateTableStatus($sn): void
    {
        $allReceive = true;// 全部已经发货
        foreach (new ShopOrderTable()->field([
            ShopOrderTable::NUM,
            ShopOrderTable::QUANTITY_SHIPPED,
        ])->where([
            ShopOrderTable::SN => $sn
        ])->selectAll() as $tempTable) {
            if ($tempTable->num - $tempTable->quantityShipped > 0) {
                $allReceive = false;
                break;
            }
        }

        // 更新订单状态,是待收货状态 ,或者部分发货状态
        new OrderTable()->where([
            OrderTable::SN => $sn
        ])->update([
            OrderTable::STATUS => $allReceive ? OrderModel::StatusWait_receive : OrderModel::StatusPartial_shipment
        ]);
    }

    /**
     * 获取订单相关的物流、产品和SKU数据
     * @param string $sn
     * @return array
     * @throws Throwable
     */
    public function getLogisticsData(string $sn): array
    {
        $orderLogisticsTable = new OrderLogisticsTable();
        $logisticsEntries = $orderLogisticsTable
            ->where([OrderLogisticsTable::SN => $sn])
            ->order([OrderLogisticsTable::PRI_KEY => 'desc'])
            ->selectAll();

        list($servicesInfo, $skusInfo, $shopOrdersInfo) = $this->getRelatedData($orderLogisticsTable, $sn);

        return [$logisticsEntries, $servicesInfo, $skusInfo, $shopOrdersInfo];
    }

    /**
     * 获取产品、SKU和订单相关信息
     * @param OrderLogisticsTable $orderLogisticsTable
     * @param string $sn
     * @return array
     * @throws Throwable
     */
    public function getRelatedData(OrderLogisticsTable $orderLogisticsTable, string $sn): array
    {
        $productIds = $orderLogisticsTable->getArrayByField(OrderLogisticsTable::PRODUCT_ID);
        $skuIds = $orderLogisticsTable->getArrayByField(OrderLogisticsTable::SKU_ID);

        $servicesInfo = new HyCompaniesServiceTable()
            ->where([[HyCompaniesServiceTable::ID, 'in', $productIds]])
            ->field(HyCompaniesServiceTable::FIELD_ALL)
            ->formatId2Array(HyCompaniesServiceTable::ID);

        $skusInfo = new HyProductsSkuTable()
            ->where([[HyProductsSkuTable::ID, 'in', $skuIds]])
            ->field(HyProductsSkuTable::FIELD_ALL)
            ->formatId2Array(HyProductsSkuTable::ID);

        $shopOrdersInfo = [];
        if (!empty($skuIds)) {
            $shopOrdersInfo = new ShopOrderTable()
                ->where([
                    ShopOrderTable::SN => $sn,
                    [ShopOrderTable::SKU_ID, 'in', $skuIds]
                ])
                ->field(ShopOrderTable::FIELD_ALL)
                ->formatId2Array(ShopOrderTable::SKU_ID);
        }

        return [$servicesInfo, $skusInfo, $shopOrdersInfo];
    }

    /**
     * 构建单个包裹的基础Proto信息
     * @param OrderLogisticsTable[] $packageEntries
     * @return OrderLogisticsProto
     * @throws Throwable
     */
    public function buildPackageProto(array $packageEntries): OrderLogisticsProto
    {
        $firstEntry = $packageEntries[0];
        $packageProto = OrderLogisticsModel::formatItem($firstEntry);

        // 统计包裹内总发货数量
        $totalPackageNum = array_sum(array_column($packageEntries, 'num'));
        $packageProto->setNum($totalPackageNum);

        return $packageProto;
    }

    /**
     * 构建产品和SKU信息
     * @param OrderLogisticsTable[] $packageEntries
     * @param HyCompaniesServiceTable[] $servicesInfo
     * @param HyProductsSkuTable[] $skusInfo
     * @param ShopOrderTable[] $shopOrdersInfo
     * @return array
     * @throws Throwable
     */
    public function buildProductsAndSkus(array $packageEntries, array $servicesInfo, array $skusInfo, array $shopOrdersInfo): array
    {
        // 按产品ID分组
        $productsInPackage = [];
        foreach ($packageEntries as $entry) {
            $productsInPackage[$entry->productId][] = $entry;
        }

        $companyServiceProtos = [];
        foreach ($productsInPackage as $productId => $productSkuEntries) {
            if (!isset($servicesInfo[$productId])) {
                continue;
            }

            $serviceTable = $servicesInfo[$productId];
            $this->sanitizeServiceTable($serviceTable);
            $serviceProto = HyCompaniesServiceModel::formatItem($serviceTable);

            // 填充产品下的SKU列表
            $skuProtos = [];
            /** @var OrderLogisticsTable $skuEntry */
            foreach ($productSkuEntries as $skuEntry) {
                $skuId = $skuEntry->skuId;
                if (!isset($skusInfo[$skuId])) {
                    continue;
                }

                $skuTable = $skusInfo[$skuId];
                $this->sanitizeSkuTable($skuTable);
                $skuProto = HyProductsSkuModel::formatItem($skuTable);

                // 从ShopOrderTable获取下单时的价格信息
                if (isset($shopOrdersInfo[$skuId])) {
                    $shopOrder = $shopOrdersInfo[$skuId];
                    $skuProto->setPrice($shopOrder->price);
                }

                $skuProto->setBuyNum($skuEntry->num);

                $skuProtos[] = $skuProto;
            }

            $serviceProto->setSkus($skuProtos);
            $companyServiceProtos[] = $serviceProto;
        }

        return $companyServiceProtos;
    }

    /**
     * 构建层级结构的响应数据
     * @param OrderLogisticsTable[] $logisticsEntries
     * @param HyCompaniesServiceTable[] $servicesInfo
     * @param HyProductsSkuTable[] $skusInfo
     * @param ShopOrderTable[] $shopOrdersInfo
     * @return array
     * @throws Throwable
     */
    public function buildHierarchicalResponse(array $logisticsEntries, array $servicesInfo, array $skusInfo, array $shopOrdersInfo): array
    {
        // 1. 按物流单号分组，形成包裹
        $packages = [];
        foreach ($logisticsEntries as $entry) {
            $packages[$entry->logisticsNumber][] = $entry;
        }

        $finalProtos = [];
        foreach ($packages as $packageEntries) {
            // 2. 构建包裹基础信息
            $packageProto = $this->buildPackageProto($packageEntries);

            // 3. 构建产品和SKU信息
            $companyServiceProtos = $this->buildProductsAndSkus($packageEntries, $servicesInfo, $skusInfo, $shopOrdersInfo);

            // 4. 将产品列表设置到包裹Proto中
            $packageProto->setCompaniesService($companyServiceProtos);
            $finalProtos[] = $packageProto;
        }

        return $finalProtos;
    }

    /**
     * 构建单个包裹的响应数据
     * @param OrderLogisticsTable[] $logisticsEntries
     * @param HyCompaniesServiceTable[] $servicesInfo
     * @param HyProductsSkuTable[] $skusInfo
     * @param ShopOrderTable[] $shopOrdersInfo
     * @return OrderLogisticsProto
     * @throws Throwable
     */
    public function buildSinglePackageResponse(array $logisticsEntries, array $servicesInfo, array $skusInfo, array $shopOrdersInfo): OrderLogisticsProto
    {
        // 构建包裹基础信息
        $packageProto = $this->buildPackageProto($logisticsEntries);

        // 构建产品和SKU信息
        $companyServiceProtos = $this->buildProductsAndSkus($logisticsEntries, $servicesInfo, $skusInfo, $shopOrdersInfo);

        // 将产品列表设置到包裹Proto中
        $packageProto->setCompaniesService($companyServiceProtos);

        return $packageProto;
    }

    /**
     * 清理 HyCompaniesServiceTable 对象，处理空值
     * @param object $serviceTable
     */
    public function sanitizeServiceTable(object $serviceTable): void
    {
        $serviceTable->isPub = $serviceTable->isPub ?? 0;
        $serviceTable->productSize = $serviceTable->productSize ?? 0;
        $serviceTable->productNum = $serviceTable->productNum ?? 0;
        $serviceTable->isTop = $serviceTable->isTop ?? 0;
        $serviceTable->isBanner = $serviceTable->isBanner ?? 0;
        $serviceTable->price = $serviceTable->price ?? 0.0;
        $serviceTable->priceMax = $serviceTable->priceMax ?? 0.0;
        $serviceTable->isSale = $serviceTable->isSale ?? 0;
    }

    /**
     * 清理 HyProductsSkuTable 对象，处理空值
     * @param object $skuTable
     */
    public function sanitizeSkuTable(object $skuTable): void
    {
        $skuTable->price = $skuTable->price ?? 0.0;
    }

    /**
     * 确认收货
     * @param string $sn 订单号
     * @param int $skuId 规格ID
     * @param string $logisticsNumber 物流单号
     * @return bool
     * @throws Throwable
     * @throws AppException
     */
    public function confirmReceiving(string $sn, int $skuId, string $logisticsNumber): bool
    {
        // 1. 验证物流信息是否存在
        $logistics = new OrderLogisticsTable()->where([
            OrderLogisticsTable::SN => $sn,
            OrderLogisticsTable::SKU_ID => $skuId,
            OrderLogisticsTable::LOGISTICS_NUMBER => $logisticsNumber,
        ])->selectOne();

        if (empty($logistics)) {
            throw new AppException('物流信息不存在');
        }

        new OrderLogisticsTable()->where([
            OrderLogisticsTable::ID => $logistics->id,
        ])->update([
            OrderLogisticsTable::CONFIRM_RECEIPT => 1,
            OrderLogisticsTable::CONFIRM_RECEIPT_TIME => date("Y-m-d H:i:s", time()),
        ]);


        // 查询已经收货的数量
        $num = new OrderLogisticsTable()->where([
            OrderLogisticsTable::SN => $sn,
            OrderLogisticsTable::CONFIRM_RECEIPT => 1,
        ])->sum(OrderLogisticsTable::NUM);


        // 更新该规格的收货数量
        new ShopOrderTable()->where([
            ShopOrderTable::SKU_ID => $skuId,
            ShopOrderTable::SN => $sn,
        ])->update([
            ShopOrderTable::COMPLETED_QUANTITY => $num // 设置收货数量为包裹数量，表示全部收到
        ]);

        //  获取该订单所有商品信息
        $shopOrders = new ShopOrderTable()->where([
            ShopOrderTable::SN => $sn
        ])->selectAll();


        //  检查订单是否全部收货
        $allReceived = true;
        foreach ($shopOrders as $shopOrder) {
            // 如果收货数量小于下单数量，则表示未全部收货
            if ($shopOrder->completedQuantity < $shopOrder->num) {
                $allReceived = false;
                break;
            }
        }

        // 更新订单状态
        if ($allReceived) {
            // 全部收货，订单完成
            new OrderTable()->where([
                OrderTable::SN => $sn
            ])->update([
                OrderTable::STATUS => OrderModel::StatusFinish,
                OrderTable::COMPLETE_TIME => time() // 更新完成时间
            ]);

            // 订单完成时，将商家的冻结余额转为可用余额
            $this->processOrderBalanceTransfer($sn, $shopOrders);
        }


        return true;
    }

    /**
     * 处理订单余额转换
     * 将商家的冻结余额转为可用余额
     *
     * @param string $sn 订单号
     * @param array $shopOrders 订单商品信息
     * @return void
     * @throws Throwable
     */
    private function processOrderBalanceTransfer(string $sn, array $shopOrders): void
    {
        // 按商家分组计算需要转换的金额
        $companyAmounts = [];

        foreach ($shopOrders as $shopOrder) {
            $companyId = $shopOrder->businessId;
            $amount = $shopOrder->price * $shopOrder->num; // 单价 * 数量

            if (!isset($companyAmounts[$companyId])) {
                $companyAmounts[$companyId] = 0;
            }
            $companyAmounts[$companyId] += $amount;
        }

        // 为每个商家执行余额转换
        foreach ($companyAmounts as $companyId => $amount) {
            try {
                HyCompaniesService::transferOrderBalance($sn, $companyId, $amount);
            } catch (Throwable $e) {
                // 记录错误但不影响主流程
                // 可以考虑添加日志记录或者重试机制
                Log::save("订单{$sn}商家{$companyId}余额转换失败: " . $e->getMessage());
            }
        }
    }

}