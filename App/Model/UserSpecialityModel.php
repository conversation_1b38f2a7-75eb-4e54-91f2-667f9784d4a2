<?php

namespace App\Model;

use Generate\Tables\Datas\SpecialityTable;
use Generate\Tables\Datas\UserSpecialityTable;
use Swlib\Connect\PoolRedis;
use Redis;
use Throwable;

class UserSpecialityModel
{
    /**
     * 查询出来用户的技能列表
     * @return UserSpecialityTable[]
     * @throws Throwable
     */
    public static function lists(int $typeId = 0, int $page = 1): array
    {

        $where = [];

        if ($typeId) {
            $where[] = [SpecialityTable::TYPE, '=', $typeId];
        }

        return new SpecialityTable()
            ->page($page, 10)
            ->where($where)
            ->selectAll();
    }

    /**
     * 查询出来用户的技能列表
     * @return UserSpecialityTable[]
     * @throws Throwable
     */
    public static function userLists(int $userId = 0): array
    {

        $where = [];
        if ($userId) {
            $where[] = [UserSpecialityTable::USER_ID, '=', $userId];
        }


        $key = "userSpecialityList:$userId";

        return new UserSpecialityTable()
            ->field([
                SpecialityTable::NAME,
            ])
            ->join(SpecialityTable::TABLE_NAME, SpecialityTable::ID, UserSpecialityTable::SPECIALITY_ID)
            ->cache(0, $key)
            ->where($where)
            ->selectAll();
    }

    /**
     * @throws Throwable
     */
    public static function userAdd(array $saveData): false|int
    {
        if (empty($saveData)) {
            return false;
        }

        $userIds = array_column($saveData, UserSpecialityTable::USER_ID);
        (new UserSpecialityTable)->where([
            [UserSpecialityTable::USER_ID, 'in', $userIds]
        ])->delete();

        $res = (new UserSpecialityTable)->insertAll($saveData);

        PoolRedis::call(function (Redis $redis) use ($userIds) {
            $redis->del(...array_map(function ($userId) {
                return "userSpecialityList:$userId";
            }, array_unique($userIds)));
        });
        return $res;
    }

}