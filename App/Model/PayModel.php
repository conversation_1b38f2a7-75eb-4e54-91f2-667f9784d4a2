<?php

namespace App\Model;

use Generate\RouterPath;
use Generate\Tables\Datas\OrderTable;
use Swlib\Exception\AppException;
use Swlib\Queue\MessageQueue;
use Throwable;
use Yansongda\Artful\Exception\ContainerException;
use Yansongda\Artful\Exception\InvalidParamsException;
use Yansongda\Artful\Exception\ServiceNotFoundException;
use Yansongda\Pay\Pay;

class PayModel
{

    const int PayTypeAliPay = 1;
    const int PayTypeWechat = 2;

    const array PayTypes = [
        self::PayTypeAliPay => '支付宝',
        self::PayTypeWechat => '微信'
    ];


    public static function getConfig(): array
    {
        $host = "https://api.zhonguoyagu.com/";
        return [
            'alipay' => [
                'default' => [
                    // 必填-支付宝分配的 app_id
                    'app_id' => '2021004187604146',
                    // 必填-应用私钥 字符串或路径
                    // 在 https://open.alipay.com/develop/manage 《应用详情->开发设置->接口加签方式》中设置
                    'app_secret_cert' => 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC457yBF5mEVz1pfncu4x3/Jzo3Q8zEevPgOYP2uexnWto9+KHBCQi1o/FAfhJPoSrZ/mFQoFyVZ8FRCYw0c+hroTplbl7MlI64G4nt4dsMswAGU4qHRu2KcoVhHMQ3g68ttx9UIfmWAIdvgkw3zsUPf50Qk0r46fKi8p4YxjFOE+LmMEu1MwzhUXixAjsu4cp5Og48/9T+ezg9JkmO7+7T1PwOoy0SE0VY4qTuRNrTUZPAvFL9ZiSONPZhwWu8XK1ilvSk4B1e+NJn6hfcDvoVF/gKNuSf8j75/gveH9CyieESX0CPaWqoSq+16mqtszUbuhsOzlXmkDHW6h9ZPsy5AgMBAAECggEBAJyXtO7m255We63x6mGM+oRx5j2Tb8EeF5dXXkj/IBRPvvAotmRSkEu0McZafotthGrloei0RnRJUFCe81wgpT8xZdEs7Z/4V/3XZLJPSkGw/l5dNb5m9RkA77BUumABZ8Qhh6iqtUKx5AEtqfrBY+ZCPvQgqaewCUqyie2VO4BQp3ANA5Y+yk7kems2pt4P56fHAezyVoscrNVkVdXqCTmLvpAA+dXaLXowHKWXplUAiaPMW8Epp9mFd8IESQZ9CH/+Wn58TjVXSo6YQqQ2V5i6iK54wfcY2D76SkD/9tVur7GxHon0BDxLU0LCH2lz0Aqzb9Cll39qgILxtfzcPGECgYEA+BnV6K/iwfI0KjyALgnHsFmWd6GDJOFTXk163zAvZLSjp0/ed1YYh8/smObwKafRzVEt0hVQLXsQ64tYhwE8O1LFFbmC7LuPgRCoT1wyCQpQr9ysYojxHTGAwozgb8MLr+uNVZhEw5NRG457G2zfPoCW7NXECy04peOCUpMOOS0CgYEAvsrR1RcIbaFdC1+ENahJHQfo10iGwTETaMF06xyQaHPyULcO+vHhZLmroplEa3H2zQxoUuLECMDitKuRsxjz7k6gz4AvfvM3326xkvADx3z9wfXVojiIfem5ZjtBIdSwB2XlY/j8T7PK7jUB6SnOZNA9AgRJKKuWcTzx/mRqAT0CgYAWjwrTMIL2zZYOrReemAcPMuYqlIX/KXnSHkto1dHJX19uDCYXRb3PQ4mo0X7O2tKJz7LR4lw69nCwaigvhl46qqj+87JC4j8HJkOXi7OE/OofQ3ptLzR1ShYCGpshrmN2qH1eZg0Cl3eNgOQSt1CJN1oByOIdfgwjkU89QN/PNQKBgEp6RAUJ07lwBzE8XRDRG5FgmA0J+8iIjsoS0sBAy0JrGIlXnFprd4/pRs3XRLUorK4IjOTmLp44JYdffiXKtNYvDWlS4beN3B61SE5SsTb7XNeNTpLfvLBiP4Rf1yIMs5Z/uVcYR+4ZAjuQp6lML4x9GYOjBqgAq16S6uhibFsFAoGARsnY0TfPhSh09Q0pBSr8+HKHPpTsl7ezKSDdxolO/HYZ2yQ6EcZf9IevUq3LssJMWhXgXKOhjFco9Py9mpk46kgQfeKYef/fT0sIVngucAHGYYL5D9OHJnzWwO8Au9sPSB3AusNBFXy9R0SR3XraaU2enIl4HbHhgVGm2x5hPOQ=',
                    // 必填-应用公钥证书 路径
                    // 设置应用私钥后，即可下载得到以下3个证书
                    'app_public_cert_path' => ROOT_DIR . 'cert/ali_cert20241017120836/app_appCertPublicKey_2021004187604146.crt',
                    // 必填-支付宝公钥证书 路径
                    'alipay_public_cert_path' => ROOT_DIR . 'cert/ali_cert20241017120836/app_alipayCertPublicKey_RSA2.crt',
                    // 必填-支付宝根证书 路径
                    'alipay_root_cert_path' => ROOT_DIR . 'cert/ali_cert20241017120836/app_alipayRootCert.crt',
                    'return_url' => $host . RouterPath::ToolAliPayReturn,
                    'notify_url' => $host . RouterPath::ToolAliPayNotify,
                    // 选填-第三方应用授权token
                    'app_auth_token' => '',
                    // 选填-服务商模式下的服务商 id，当 mode 为 Pay::MODE_SERVICE 时使用该参数
                    'service_provider_id' => '',
                    // 选填-默认为正常模式。可选为： MODE_NORMAL, MODE_SANDBOX, MODE_SERVICE
                    'mode' => Pay::MODE_NORMAL,
                ]
            ],
            'wechat' => [
                'default' => [
                    // 必填-商户号，服务商模式下为服务商商户号
                    // 可在 https://pay.weixin.qq.com/ 账户中心->商户信息 查看
                    'mch_id' => '**********',
                    // 选填-v2 商户私钥
                    'mch_secret_key_v2' => 'xGllHgs23svF7jYxGv6wLK3svTdJg345',
                    // 必填-v3 商户秘钥
                    // 即 API v3 密钥(32字节，形如md5值)，可在 账户中心->API安全 中设置
                    'mch_secret_key' => 'xGlH6Gdf457jYKxGv6kwL4yHdlIJg345',
                    // 必填-商户私钥 字符串或路径
                    // 即 API证书 PRIVATE KEY，可在 账户中心->API安全->申请API证书 里获得
                    // 文件名形如：apiclient_key.pem
                    'mch_secret_cert' => ROOT_DIR . 'cert/wx_cert/apiclient_key.pem',
                    // 必填-商户公钥证书路径
                    // 即 API证书 CERTIFICATE，可在 账户中心->API安全->申请API证书 里获得
                    // 文件名形如：apiclient_cert.pem
                    'mch_public_cert_path' =>  ROOT_DIR . 'cert/wx_cert/apiclient_cert.pem',
                    // 必填-微信回调url
                    // 不能有参数，如?号，空格等，否则会无法正确回调
                    'notify_url' => 'https://yansongda.cn/wechat/notify',
                    // 选填-公众号 的 app_id
                    // 可在 mp.weixin.qq.com 设置与开发->基本配置->开发者ID(AppID) 查看
                    'mp_app_id' => '2016082000291234',
                    // 选填-小程序 的 app_id
                    'mini_app_id' => 'wxd968c528743adc19',
                    // 选填-app 的 app_id
                    'app_id' => '',
                    // 选填-服务商模式下，子公众号 的 app_id
                    'sub_mp_app_id' => '',
                    // 选填-服务商模式下，子 app 的 app_id
                    'sub_app_id' => '',
                    // 选填-服务商模式下，子小程序 的 app_id
                    'sub_mini_app_id' => '',
                    // 选填-服务商模式下，子商户id
                    'sub_mch_id' => '',
                    // 选填-微信平台公钥证书路径, optional，强烈建议 php-fpm 模式下配置此参数
                    'wechat_public_cert_path' => [
                        'PUB_KEY_ID_01**********2025040100297900003046' => ROOT_DIR . 'cert/wx_cert/wx_pub_key.pem',
                    ],
                    // 选填-默认为正常模式。可选为： MODE_NORMAL, MODE_SERVICE
                    'mode' => Pay::MODE_NORMAL,
                ]
            ],
            'unipay' => [
                'default' => [
                    // 必填-商户号
                    'mch_id' => '777290058167151',
                    // 选填-商户密钥：为银联条码支付综合前置平台配置：https://up.95516.com/open/openapi?code=unionpay
                    'mch_secret_key' => '979da4cfccbae7923641daa5dd7047c2',
                    // 必填-商户公私钥
                    'mch_cert_path' => __DIR__ . '/Cert/unipayAppCert.pfx',
                    // 必填-商户公私钥密码
                    'mch_cert_password' => '000000',
                    // 必填-银联公钥证书路径
                    'unipay_public_cert_path' => __DIR__ . '/Cert/unipayCertPublicKey.cer',
                    // 必填
                    'return_url' => 'https://yansongda.cn/unipay/return',
                    // 必填
                    'notify_url' => 'https://yansongda.cn/unipay/notify',
                    'mode' => Pay::MODE_NORMAL,
                ],
            ],
            'douyin' => [
                'default' => [
                    // 选填-商户号
                    // 抖音开放平台 --> 应用详情 --> 支付信息 --> 产品管理 --> 商户号
                    'mch_id' => '73744242495132490630',
                    // 必填-支付 Token，用于支付回调签名
                    // 抖音开放平台 --> 应用详情 --> 支付信息 --> 支付设置 --> Token(令牌)
                    'mch_secret_token' => 'douyin_mini_token',
                    // 必填-支付 SALT，用于支付签名
                    // 抖音开放平台 --> 应用详情 --> 支付信息 --> 支付设置 --> SALT
                    'mch_secret_salt' => 'oDxWDBr4U7FAAQ8hnGDm29i4A6pbTMDKme4WLLvA',
                    // 必填-小程序 app_id
                    // 抖音开放平台 --> 应用详情 --> 支付信息 --> 支付设置 --> 小程序appid
                    'mini_app_id' => 'tt226e54d3bd581bf801',
                    // 选填-抖音开放平台服务商id
                    'thirdparty_id' => '',
                    // 选填-抖音支付回调地址
                    'notify_url' => 'https://yansongda.cn/douyin/notify',
                ],
            ],
            'jsb' => [
                'default' => [
                    // 服务代码
                    'svr_code' => '',
                    // 必填-合作商ID
                    'partner_id' => '',
                    // 必填-公私钥对编号
                    'public_key_code' => '00',
                    // 必填-商户私钥(加密签名)
                    'mch_secret_cert_path' => '',
                    // 必填-商户公钥证书路径(提供江苏银行进行验证签名用)
                    'mch_public_cert_path' => '',
                    // 必填-江苏银行的公钥(用于解密江苏银行返回的数据)
                    'jsb_public_cert_path' => '',
                    //支付通知地址
                    'notify_url' => '',
                    // 选填-默认为正常模式。可选为： MODE_NORMAL:正式环境, MODE_SANDBOX:测试环境
                    'mode' => Pay::MODE_NORMAL,
                ],
            ],
            'logger' => [
                'enable' => true,
                'file' =>  RUNTIME_DIR .  'log/pay.log',
                'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                'type' => 'single', // optional, 可选 daily.
                'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
            ],
            'http' => [ // optional
                'timeout' => 5.0,
                'connect_timeout' => 5.0,
                // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
            ],
        ];
    }


    /**
     * 查询支付结果
     * @throws ServiceNotFoundException
     * @throws ContainerException
     * @throws InvalidParamsException
     * @throws Throwable
     */
    public static function aliPayQuery($sn): true|string
    {
        Pay::config(PayModel::getConfig());

        $result = Pay::alipay()->query([
            'out_trade_no' => $sn,
            '_action' => 'agreement', // 商家收款查询
        ]);

        // 【描述】交易状态：
        //  WAIT_BUYER_PAY（交易创建，等待买家付款）、
        //  TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、
        //  TRADE_SUCCESS（交易支付成功）、
        //  TRADE_FINISHED（交易结束，不可退款）
        $trade_status = $result->get('trade_status');

        // 支付宝流水号
        $trade_no = $result->get('trade_no');


        if ($trade_status === 'TRADE_SUCCESS') {
            $ret = new OrderTable()->addWhere(OrderTable::SN, $sn)->update([
                // 支付成功变成待发货
//                OrderTable::ORDER_STATUS => OrderStatusEnum::WAIT_DELIVERY,
                OrderTable::PAY_TIME => time(),
                OrderTable::PAY_TYPE => self::PayTypeAliPay,
                // 支付流水号
                OrderTable::PAY_NO => $trade_no,
            ]);

            MessageQueue::push([OrderModel::class, 'addByCount'], [
                'sn' => $sn
            ]);

            return (bool)$ret;
        }

        return false;
    }


    /**
     * 支付宝发起退款,后台审核成功后调用
     * @throws ServiceNotFoundException
     * @throws ContainerException
     * @throws InvalidParamsException
     * @throws Throwable
     */
    public static function aliPayRefund(string $sn, float $refund_amount): true|string
    {
        Pay::config(PayModel::getConfig());

        // 退款前判断
        $orderTable = OrderModel::orderRefundCheck($sn, $refund_amount);

        $result = Pay::alipay()->refund([
            'out_trade_no' => $sn,
            'refund_amount' => $refund_amount
        ]);

        // 接口返回fund_change=Y为退款成功，fund_change=N或无此字段值返回时需通过退款查询接口进一步确认退款状态
        $fund_change = $result->get('fund_change');

        // 支付宝退款流水号
        $trade_no = $result->get('trade_no');

        if ($fund_change === 'Y') {
            // 确认退款
            return OrderModel::orderRefund($sn, $refund_amount, $trade_no);
        } else {
            // 需要进一步查询退款是否成功
            new OrderTable()->where([
                [OrderTable::ID, '=', $orderTable->id]
            ])->update([
                OrderTable::REFUND_STATUS => 1,
            ]);


            // 添加到消息队列，查询退款结果
            MessageQueue::push([__CLASS__, 'aliPayRefundQuery'], [
                'sn' => $sn,
                'refund_amount' => $refund_amount
            ]);

        }

        return false;
    }

    /**
     * @throws Throwable
     */
    public function aliPayRefundQuery(array $data): bool
    {
        $sn = $data['sn'];
        $refund_amount = $data['refund_amount'];
        Pay::config(PayModel::getConfig());

        $result = Pay::alipay()->query([
            'out_trade_no' => $sn,
            '_action' => 'refund', // 退款查询
        ]);

        // 【描述】退款状态。枚举值： REFUND_SUCCESS 退款处理成功；
        // 未返回该字段表示退款请求未收到或者退款失败；
        // 注：如果退款查询发起时间早于退款时间，或者间隔退款发起时间太短，可能出现退款查询时还没处理成功，后面又处理成功的情况，
        //建议商户在退款发起后间隔10秒以上再发起退款查询请求。
        $refund_status = $result->get('refund_status');

        if ($refund_status !== 'REFUND_SUCCESS') {
            throw new AppException('支付状态不是REFUND_SUCCESS，当前状态：%s', $refund_status);
        }
        $trade_no = $result->get('trade_no');
        $ret = OrderModel::orderRefund($sn, $refund_amount, $trade_no);
        if ($ret !== true) {
            throw new AppException('退款失败');
        }
        return true;
    }
}