<?php

namespace App\Model;

use Generate\Tables\Datas\ZpJobsCollectTable;
use Throwable;

class JobCollectModel
{
    /**
     * 查询职位是否收藏了
     * @param int $userId
     * @param array $jobIds 职位id列表
     * @param array $jobLists 职位列表
     * @return void
     * @throws Throwable
     */
    public static function getByJobs(int $userId, array $jobIds, array $jobLists): void
    {
        if (empty($userId)) {
            return;
        }

        $ids = new ZpJobsCollectTable()->where([
            [ZpJobsCollectTable::USER_ID, '=', $userId],
            [ZpJobsCollectTable::JOBS_ID, 'in', $jobIds],
        ])->getArrayByField(ZpJobsCollectTable::JOBS_ID);

        foreach ($jobLists as $jobItem) {
            if (in_array($jobItem->getId(), $ids)) {
                $jobItem->setIsCollected(true);
            }
        }

    }

    /**
     * @throws Throwable
     */
    public static function getByUserLists(int $userId, int $page): array
    {

        return new ZpJobsCollectTable()->where([
        [ZpJobsCollectTable::USER_ID, '=', $userId],
    ])->page($page, 10)->selectAll();
    }


}