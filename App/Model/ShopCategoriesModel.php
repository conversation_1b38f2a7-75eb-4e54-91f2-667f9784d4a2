<?php

namespace App\Model;

use Generate\Tables\Datas\ShopCategoriesTable;
use Throwable;

class ShopCategoriesModel
{

    /**
     * @throws Throwable
     */
    public static function getChildrenIds(int $id): array
    {
        $ret = [$id];
        /** @var $row ShopCategoriesTable */
        foreach (new ShopCategoriesTable()->field(ShopCategoriesTable::ID)->addWhere(ShopCategoriesTable::PARENT_ID, $id)->generator() as $row) {
            $ret[] = $row->id;
        }
        return $ret;
    }

}