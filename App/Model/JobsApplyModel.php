<?php

namespace App\Model;


use Generate\Tables\Datas\ZpJobsApplyTable;
use Generate\Tables\Datas\ZpJobsTable;
use Protobuf\Jobs\JobsApplyItem;
use Protobuf\Jobs\JobsApplyLists;
use Throwable;

class JobsApplyModel
{

    const string PENDING = 'pending';
    const string REJECTED = 'rejected';
    const string ACCEPTED = 'accepted';
    const string INTERVIEW = 'interview';

    const array STATUS_MAPS = [
        self::PENDING => '暂无结果',
        self::INTERVIEW => '面试',
        self::REJECTED => '已拒绝',
        self::ACCEPTED => '已通过'
    ];


    /**
     * 添加职位收藏
     * @throws Throwable
     */
    public static function apply(int $jobsId, int $userId, ?int $resumeId = null): bool
    {
        $find = (new ZpJobsApplyTable)->where([
            [ZpJobsApplyTable::JOB_ID, '=', $jobsId],
            [ZpJobsApplyTable::USER_ID, '=', $userId],
        ])->selectOne();

        $companyId = new ZpJobsTable()->where([
            ZpJobsTable::ID => $jobsId
        ])->selectField(ZpJobsTable::COMPANY_ID);

        if ($find) {
            $res = (new ZpJobsApplyTable)->where([
                ZpJobsApplyTable::ID => $find->id,
            ])->update([
                ZpJobsApplyTable::RESUME_ID => $resumeId,
                ZpJobsApplyTable::TIME => time(),
                ZpJobsApplyTable::STATUS => JobsApplyModel::PENDING,
            ]);
        } else {
            $res = (new ZpJobsApplyTable)->insert([
                ZpJobsApplyTable::JOB_ID => $jobsId,
                ZpJobsApplyTable::USER_ID => $userId,
                ZpJobsApplyTable::RESUME_ID => $resumeId,
                ZpJobsApplyTable::COMPANY_ID => $companyId,
                ZpJobsApplyTable::TIME => time(),
                ZpJobsApplyTable::STATUS => JobsApplyModel::PENDING,
            ]);
        }


        if ($res) {
            return true;
        }
        return false;
    }


    /**
     * 查询 面试列表
     * @param int $userId
     * @param int $page
     * @param int $type 1  待面试  2： 已经面试
     * @param $datetime
     * @return JobsApplyLists
     * @throws Throwable
     */
    public static function applyList(int $userId, int $page, int $type, $datetime): JobsApplyLists
    {
        $jobApplyTable = new ZpJobsApplyTable();

        $where = [];
        $where[] = [ZpJobsApplyTable::USER_ID, '=', $userId];
        if ($type === 1) {
            $where[] = [ZpJobsApplyTable::INTERVIEW_USER_ID, 'is null'];
        } else {
            $where[] = [ZpJobsApplyTable::INTERVIEW_USER_ID, '>', 0];
        }

        if ($datetime) {
            // 这里 datetime 传入的  2049-01-01 00:00:00
            // 所以查询一天就是+86400
            $time = strtotime($datetime);
            $where[] = [ZpJobsApplyTable::TIME, '>', $time];
            $where[] = [ZpJobsApplyTable::TIME, '<', $time + 86400];
        }

        $tables = $jobApplyTable->page($page, 10)->where($where)->selectAll();

        $jobIds = $jobApplyTable->getArrayByField(ZpJobsApplyTable::JOB_ID);
        $jobs = JobsModel::getJobList($jobIds);

        $messages = [];
        foreach ($tables as $table) {
            $jobId = $table->jobId;

            $msg = new JobsApplyItem();
            $msg->setId($table->id);
            $msg->setJobId($table->jobId);
            if (isset($jobs[$jobId])) {
                $msg->setJobs($jobs[$jobId]);
            }

            $msg->setUserId($table->userId);
            $msg->setApplicationTime(date('Y-m-d', $table->time));
            $msg->setStatus(self::STATUS_MAPS[$table->status]);
            $msg->setInterviewTime(date('Y-m-d H:i', $table->interviewTime));
            $msg->setFeedback($table->feedback);
            $msg->setFeedback($table->feedback);
            $msg->setInterviewUserId($table->interviewUserId ?: 0);
            $messages[] = $msg;
        }

        $listsMessage = new JobsApplyLists();
        $listsMessage->setLists($messages);
        return $listsMessage;

    }

    /**
     * @throws Throwable
     */
    public static function hasApply(int $jobId, int $userId = 0): bool
    {
        if (empty($userId)) {
            return false;
        }

        $find = (new ZpJobsApplyTable)->where([
            [ZpJobsApplyTable::JOB_ID, '=', $jobId],
            [ZpJobsApplyTable::USER_ID, '=', $userId],
        ])->selectOne();

        return (bool)$find;

    }

}