<?php

namespace App\Model;


use Generate\Tables\Datas\UserTable;
use Protobuf\Datas\User\UserProto;
use Swlib\Exception\AppException;
use Throwable;

class UserModel
{

    /**
     * @throws Throwable
     */
    public static function formatUser(UserTable $table): UserProto
    {
        $message = new UserProto();
        $message->setUsername($table->username);
        $message->setPhone($table->phone);
        $message->setHead($table->head);
        $message->setNickname($table->nickname);
        $message->setId($table->id);
        $message->setEmail($table->email);
        $message->setBirthday($table->birthday);
        $message->setGender($table->gender);

        $addr = $table->addr;
        $addrArr = explode(',', $addr);
        if (isset($addrArr[0])) {
            $message->setProvince($addrArr[0]);
        }
        if (isset($addrArr[1])) {
            $message->setCity($addrArr[1]);
        }
        if (isset($addrArr[2])) {
            $message->setArea($addrArr[2]);
        }
        if (isset($addrArr[3])) {
            $message->setAddr($addrArr[3]);
        }


        return $message;
    }

    /**
     * @throws Throwable
     */
    public static function findByUserId(int $userId): int
    {
        $findUserId = (new UserTable)->where([
            [UserTable::ID, '=', $userId],
        ])->selectField(UserTable::ID);

        if (empty($findUserId)) {
            throw new AppException("用户不存在");
        }

        return $findUserId;
    }

}