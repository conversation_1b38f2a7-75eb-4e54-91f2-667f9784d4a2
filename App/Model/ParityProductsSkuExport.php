<?php

namespace App\Model;

use Generate\Tables\Datas\ParityProductsSkuTable;
use Generate\Tables\Datas\ParityProductsTable;
use Generate\Tables\Datas\ParitySourceTable;

use Swlib\Queue\MessageQueue;
use Swoole\Coroutine;
use Throwable;
use Vtiful\Kernel\Excel;

class ParityProductsSkuExport
{


    /**
     * @throws Throwable
     */
    public function diff(array $data): bool
    {
        // 读取文件
        $filepath = $data["filePath"];
        $dir = dirname($filepath);
        $filename = basename($filepath); // 取得文件名称
        $config = ['path' => $dir];
        $excel = new Excel($config);
        $excel = $excel->openFile($filename)->setType([
            0 => Excel::TYPE_STRING,
            1 => Excel::TYPE_STRING,
            2 => Excel::TYPE_STRING,
            3 => Excel::TYPE_STRING,
            4 => Excel::TYPE_STRING,
            5 => Excel::TYPE_STRING,
            6 => Excel::TYPE_STRING,
            7 => Excel::TYPE_STRING,
            8 => Excel::TYPE_STRING,
            9 => Excel::TYPE_STRING,
        ])->openSheet();
        $xlsxHeader = $excel->nextRow();


        // 写入文件
        $saveFilePath = $data["saveFilePath"];
        $saveDir = dirname($saveFilePath);
        $saveFilename = basename($saveFilePath);
        $saveExcel = new Excel(['path' => $saveDir]);
        $saveExcel = $saveExcel->fileName($saveFilename, 'sheet1');
        $saveExcel = $saveExcel->insertText(0, 0, '产品名称');
        $saveExcel = $saveExcel->insertText(0, 1, '规格名称');

        $saveExcel = $saveExcel->insertText(0, 2, '励齿编码');
        $saveExcel = $saveExcel->insertText(0, 3, '励齿URL');

        $saveExcel = $saveExcel->insertText(0, 4, '牙易编码');
        $saveExcel = $saveExcel->insertText(0, 5, '牙易URL');

        $saveExcel = $saveExcel->insertText(0, 6, '苗苗编码');
        $saveExcel = $saveExcel->insertText(0, 7, '苗苗URL');

        $saveExcel = $saveExcel->insertText(0, 8, '松佰编码');
        $saveExcel = $saveExcel->insertText(0, 9, '松佰URL');

        $saveExcel = $saveExcel->insertText(0, 10, 'e看牙编码');
        $saveExcel = $saveExcel->insertText(0, 11, 'e看牙URL');


        $saveExcel = $saveExcel->insertText(0, 12, '牙医帮编码');
        $saveExcel = $saveExcel->insertText(0, 13, '牙医帮URL');
        $saveExcel = $saveExcel->insertText(0, 14, '表格来源数量');
        $saveExcel = $saveExcel->insertText(0, 15, '数据库来源数量');
        $saveExcel = $saveExcel->insertText(0, 16, '判定');


        $pNumber = 0;
        $rowIndex = 0;
        while (($row = $excel->nextRow()) !== NULL) {
            $rowIndex++;

            Coroutine::create(function () use (&$pNumber, &$saveExcel, $rowIndex, $row, $data) {

                $productName = $row[0];
                if (empty($productName)) return;
                $skuName = $row[1];

                $sources = new ParitySourceTable()
                    ->join(ParityProductsTable::TABLE_NAME, ParityProductsTable::ID, ParitySourceTable::PRODUCT_ID)
                    ->join(ParityProductsSkuTable::TABLE_NAME, ParityProductsSkuTable::ID, ParitySourceTable::SKU_ID)
                    ->where([
                        ParityProductsTable::NAME => $productName,
                        ParityProductsSkuTable::NAME => $skuName,
                    ])->selectAll();

                if (empty($sources)) {
                    $saveExcel = $saveExcel->insertText($rowIndex, 14, '未找到商品');
                    return;
                }

                $tableCount = 0;
                list($lc, $ye, $mm, $sb, $eky, $yyb, $count) = $this->getSourceUrl($sources);

                $saveExcel = $saveExcel->insertText($rowIndex, 0, $productName);
                $saveExcel = $saveExcel->insertText($rowIndex, 1, $skuName);

                //励齿编码
                if ($row[3]) {
                    $tableCount++;
                }
                $saveExcel = $saveExcel->insertText($rowIndex, 2, $row[3]);
                $saveExcel = $saveExcel->insertText($rowIndex, 3, $lc);

                //牙易编码
                if ($row[4]) {
                    $tableCount++;
                }
                $saveExcel = $saveExcel->insertText($rowIndex, 4, $row[4]);
                $saveExcel = $saveExcel->insertText($rowIndex, 5, $ye);

                //苗苗编码
                if ($row[5]) {
                    $tableCount++;
                }
                $saveExcel = $saveExcel->insertText($rowIndex, 6, $row[5]);
                $saveExcel = $saveExcel->insertText($rowIndex, 7, $mm);

                //松佰编码
                if ($row[6]) {
                    $tableCount++;
                }
                $saveExcel = $saveExcel->insertText($rowIndex, 8, $row[6]);
                $saveExcel = $saveExcel->insertText($rowIndex, 9, $sb);

                //e看牙编码
                if ($row[7]) {
                    $tableCount++;
                }
                $saveExcel = $saveExcel->insertText($rowIndex, 10, $row[7]);
                $saveExcel = $saveExcel->insertText($rowIndex, 11, $eky);

                //牙医帮编码
                if ($row[8]) {
                    $tableCount++;
                }
                $saveExcel = $saveExcel->insertText($rowIndex, 12, $row[8]);
                $saveExcel = $saveExcel->insertText($rowIndex, 13, $yyb);


                $saveExcel = $saveExcel->insertText($rowIndex, 14, $tableCount);
                $saveExcel = $saveExcel->insertText($rowIndex, 15, $count);
                $saveExcel = $saveExcel->insertText($rowIndex, 16, $count == $tableCount ? '一致' : '不一致');


                MessageQueue::updateProgress($data['_msgId'], $rowIndex);
                $pNumber--;
            });
            $pNumber++;
            while ($pNumber > 64) {
                Coroutine::sleep(1);
            }
        }

        $saveExcel->output();
        return true;
    }

    /**
     * @throws Throwable
     */
    public function export(array $data): bool
    {
        $filePath = $data['filePath'];
        $saveDir = dirname($filePath);
        $fileName = basename($filePath);

        $config = [
            'path' => $saveDir, // xlsx文件保存路径
        ];
        $excel = new Excel($config);
        // fileName 会自动创建一个工作表，你可以自定义该工作表名称，工作表名称为可选参数
        $excel = $excel->fileName($fileName, 'sheet1')
            ->header([
                '商品名称',
                '规格名称',
                '励齿',
                '牙易',
                '苗苗',
                '松佰',
                'e看牙',
                '牙医帮',
                '来源数量'
            ]);

        $query = new ParityProductsSkuTable()->order([
            ParityProductsSkuTable::PRODUCT_ID => 'asc',
            ParityProductsSkuTable::ID => 'asc',
        ])
            ->join(ParityProductsTable::TABLE_NAME, ParityProductsTable::ID, ParityProductsSkuTable::PRODUCT_ID)
            ->field([
                ParityProductsSkuTable::ID,
                ParityProductsSkuTable::NAME,
                ParityProductsTable::ID,
                ParityProductsTable::NAME,
            ]);


        $countQuery = clone $query;
        $count = $countQuery->count();
        $rowIndex = 0;
        $page = 0;
        while (true) {
            $newQuery = clone $query;
            $page++;
            $res = $newQuery->page($page, 2000)->selectAll();
            if (empty($res)) {
                MessageQueue::updateProgress($data['_msgId'], 100);
                break;
            }

            $parallelSize = 64;
            Coroutine\parallel(64, function () use (&$res, &$parallelSize, $data, $count, &$rowIndex, $excel) {
                /**@var ParityProductsTable $row */
                while ($row = array_shift($res)) {
                    $rowIndex++;
                    $this->getSourceInfo($excel, $row, $rowIndex);
                    $ratio = round($rowIndex / $count * 100, 2);
                    MessageQueue::updateProgress($data['_msgId'], $ratio);
                    $parallelSize--;
                }
            });
            while ($parallelSize > 0) {
                Coroutine::sleep(1);
            }
        }

        $excel->output();
        return true;
    }

    /**
     * @throws Throwable
     */
    private function getSourceInfo($excel, $row, $rowIndex): void
    {

        $productName = $row->getByField(ParityProductsTable::NAME);
        $productId = $row->getByField(ParityProductsTable::ID);
        $skuName = $row->getByField(ParityProductsSkuTable::NAME);
        $skuId = $row->getByField(ParityProductsSkuTable::ID);

        $sources = new ParitySourceTable()->where([
            ParitySourceTable::SKU_ID => $skuId,
            ParitySourceTable::PRODUCT_ID => $productId,
        ])->selectAll();

        list($lc, $ye, $mm, $sb, $eky, $yyb, $count) = $this->getSourceUrl($sources);

        $excel->insertText($rowIndex, 0, $productName);
        $excel->insertText($rowIndex, 1, $skuName);
        $excel->insertText($rowIndex, 2, $lc);
        $excel->insertText($rowIndex, 3, $ye);
        $excel->insertText($rowIndex, 4, $mm);
        $excel->insertText($rowIndex, 5, $sb);
        $excel->insertText($rowIndex, 6, $eky);
        $excel->insertText($rowIndex, 7, $yyb);
        $excel->insertText($rowIndex, 8, $count);
    }


    private function getSourceUrl(array $sources): array
    {
        $lc = ''; //'励齿',
        $ye = ''; //    '牙易',
        $mm = '';  // '苗苗',
        $sb = '';//  '松佰',
        $eky = '';//  'e看牙',
        $yyb = '';// '牙医帮'
        $count = 0;
        foreach ($sources as $source) {
            $url = $source->getUrl('');
            if (empty($url)) {
                continue;
            }
            $count++;
            switch ($source->sourceId) {
                case 1:
                    $mm = $url;
                    break;
                case 2:
                    $lc = $url;
                    break;
                case 3:
                    $ye = $url;
                    break;
                case 4:
                    $sb = $url;
                    break;
                case 5:
                    $eky = $url;
                    break;
                case 7:
                    $yyb = $url;
                    break;

            }
        }
        return [$lc, $ye, $mm, $sb, $eky, $yyb, $count];
    }
}