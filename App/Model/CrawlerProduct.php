<?php

namespace App\Model;
require_once ROOT_DIR . "sdk/aliyun-oss-php-sdk-2.7.1/autoload.php";

use App\Service\EnvironmentVariableCredentialsProvider;
use Generate\Tables\Datas\ParityProductsSkuTable;
use Generate\Tables\Datas\ParityProductsTable;
use Generate\Tables\Datas\ParitySourceTable;
use Generate\Tables\Datas\ProductSkuTable;
use Generate\Tables\Datas\ProductsTable;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Handler\StreamHandler;
use GuzzleHttp\HandlerStack;
use Swlib\Exception\AppException;
use OSS\Core\OssException;
use OSS\Http\RequestCore_Exception;
use OSS\OssClient;
use Throwable;

class CrawlerProduct
{

    /**
     * @throws AppException
     * @throws Throwable
     */
    public static function run(ParitySourceTable $source): void
    {
        // 不做爬取数据相关逻辑了
        //echo $source->id . PHP_EOL;
        return;
        if ($source->url) {
            switch ($source->sourceId) {
                case 1:
                    CrawlerProduct::mmm($source, true);
                    break;
                case 2:
                    CrawlerProduct::lc($source, true);
                    break;
                case 3:
                    CrawlerProduct::yYzx($source, true);
                    break;
                case 4:
                    CrawlerProduct::sb($source, true);
                    break;
                case 5:
                    CrawlerProduct::eky($source, true);
                    break;
                case 7:
                    CrawlerProduct::yyb($source, true);
                    break;
            }
        } else {
            self::getSourceDetail($source);
        }

    }

    /**
     * @throws Throwable
     */
    private static function getSourceDetail(ParitySourceTable $source): void
    {
        if ($source->url) return;
        $sourceId = $source->sourceId;
        $find = false;
        if ($source->cId) {
            $find = new ProductSkuTable()->where([
                [ProductSkuTable::SOURCE_ID, '=', $sourceId],
                [ProductSkuTable::ID, '=', $source->cId],
            ])->selectOne();
        }

        if (empty($find) && $source->cCode) {
            $find = new ProductSkuTable()->where([
                [ProductSkuTable::SOURCE_ID, '=', $sourceId],
                [ProductSkuTable::CODE, '=', $source->cCode],
            ])->selectOne();
        }
        if (empty($find)) return;
        $pic = new ProductsTable()->where([
            ProductsTable::ID => $find->productId
        ])->selectField(ProductsTable::PICTURE);

        self::update($source, $find->price, $pic);

    }

    /**
     * 梅苗苗
     * @throws AppException
     * @throws Throwable
     */
    public static function mmm(ParitySourceTable $source, bool $forceUpdate = false): void
    {
        $id = $source->id;
        $url = $source->url;
        $arr = explode('/', $url);
        $mmmProductId = array_pop($arr);
        $cachePath = RUNTIME_DIR . "crawler/mmm/$id.txt";
        $res = self::getCache($cachePath, $forceUpdate, 'https://w.mmm920.com/api/product/detail', [
            'id' => $mmmProductId
        ]);

        if (!$res['isSuccess']) {
            self::update(source: $source, msg: $res['message']);
            return;
        }

        $price[] = $res['data']['selectStandard']['price'] ?? 99999999;
        $price[] = $res['data']['selectStandard']['marketPrice'] ?? 99999999;
        $price[] = $res['data']['selectStandard']['discountPrice'] ?? 99999999;
        $picture = "https://image.mmm920.com/upload" . str_replace("{0}", '400x400', $res['data']['picturePath']);
        self::update($source, self::getMinPrice($price), $picture);
    }

    /**
     * 励齿
     * @throws Throwable
     */
    public static function lc(ParitySourceTable $source, bool $forceUpdate = false): void
    {
        $id = $source->id;
        $url = $source->url;
        $r = explode('?', $url);
        parse_str($r[1], $r2);
        $pdCode = $r2['pdCode'];
        $selSku = $r2['selSku'] ?? "";

        $cachePath = RUNTIME_DIR . "crawler/lc/$id.txt";

        $sku = self::_lcQuery("https://api.lichidental.com/lichidental-web/product/detail?pdCode=$pdCode&userId=&channel=PC", $cachePath, $forceUpdate, $pdCode, $selSku);
        if (empty($sku)) {
            $sku = self::_lcQuery("https://api.lichigz.com/lichidental-web/product/detail?pdCode=$pdCode&userId=&channel=PC", $cachePath, $forceUpdate, $pdCode, $selSku);
        }


        if (empty($sku)) {
            self::update(source: $source, msg: '找不到SKU');
            return;
        }

        $picture = '';
        if (isset($sku['imgs'][0]['imgPath'])) {
            $picture = $sku['imgs'][0]['imgPath'];
        }

        $price[] = $sku['productVipPrice'];
        $price[] = $sku['handPrice'];
        $price[] = $sku['productPrice'];
        $price = self::getMinPrice($price);
        self::update($source, $price, $picture);
    }

    /**
     * @throws Throwable
     */
    private static function _lcQuery($url, $cachePath, $forceUpdate, $pdCode, $selSku)
    {
        $res = self::getCache($cachePath, $forceUpdate, $url, [], 'GET');

        $sku = null;
        foreach ($res['body']['productDetailInfo']['productModels'] as $item) {
            if ($selSku) {
                if ($item['pdCode'] == $pdCode && $selSku == $item['productSku']) {
                    $sku = $item;
                    break;
                }
            } else {
                if ($item['pdCode'] == $pdCode) {
                    $sku = $item;
                    break;
                }
            }

        }
        return $sku;
    }

    /**
     * 牙e在线
     * @throws Throwable
     */
    public static function yYzx(ParitySourceTable $source, bool $forceUpdate = false): void
    {
        $id = $source->id;
        $url = $source->url;
        $r = explode('?', $url);
        parse_str($r[1], $r2);


        $cachePath = RUNTIME_DIR . "crawler/yezx/$id.txt";
        $res = self::getCache($cachePath, $forceUpdate, "https://apps.yae920.com/goods/goodsBuyInfo?goodsId={$r2['goodsId']}");


        if (!$res['success']) {
            self::update(source: $source, msg: $res['msg']);
            return;
        }
        $picture = $res['data']['picture'];
        $price[] = $res['data']['discountPriceYuan'] ?? 99999999;
        $price[] = $res['data']['salePriceYuan'] ?? 99999999;
        $price[] = $res['data']['couponPriceYuan'] ?? 99999999;
        $price = self::getMinPrice($price);

        self::update($source, $price, $picture);


    }

    /**
     * 松佰 商城
     * @throws Throwable
     */
    public static function sb(ParitySourceTable $source, bool $forceUpdate = false): void
    {
        $id = $source->id;
        $url = $source->url;
        $r = explode('?', $url);
        parse_str($r[1], $r2);


        $cachePath = RUNTIME_DIR . "crawler/sbsc/$id.txt";
        $token = 'a744d624-6268-474a-9044-87eefd6ed3c4';
        $res = self::getCache($cachePath, $forceUpdate,
            "https://app.202832.com/v2/api/common/product?custId=&lastTokenId=$token&saleId={$r2['saleId']}&productId=&productNum=1&source=pc&zoneId=",
            [],
            'GET',
            [
                'tokenid' => $token
            ]
        );

        if (!isset($res['content']['price'])) {
            self::update(source: $source, msg: '找不到价格-已下架');
            return;
        }

        if (isset($res['content']['show_price'])) {
            $price = ltrim($res['content']['show_price'], '¥');
            $price = ltrim($price, '￥');
        } else {
            $price = $res['content']['price'] * $res['content']['whole_discount'];
        }

        $picture = $res['content']['pics'][0]['bigPic'] ?? '';
        self::update($source, (float)$price, $picture);
    }

    /**
     * e看牙商城
     * @throws Throwable
     */
    public static function eky(ParitySourceTable $source, bool $forceUpdate = false): void
    {
        $id = $source->id;
        $url = $source->url;
        $r = explode('?', $url);
        $arr = explode("/", $r[0]);
        $productId = array_pop($arr);
        parse_str($r[1], $r2);
        if (!isset($r2['skuId'])) {
            self::update(source: $source, msg: "URL 格式错误，请检查");
            return;
        }
        $skuId = $r2['skuId'];


        $cachePath = RUNTIME_DIR . "crawler/eky/$id.txt";
        $token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJvcmdfb2ZmaWNlaWQiOiJtYWxsIiwic3ViIjoiYW5vbnltb3VzIiwiaXNzIjoiTGlua2VkQ2FyZSIsIm9yZ190ZW5hbnRpZCI6Im1hbGwiLCJwZXJzb25OYW1lIjoiYW5vbnltb3VzIiwiYXVkIjoibWFsbCIsIm9yZ19pZCI6Im1hbGwiLCJvcmdfbmFtZSI6ImFub255bW91cyIsImN1c3RvbWVyX2lkIjoiLTIiLCJleHAiOjE3NTczODcxMzQsImlhdCI6MTcyNTg1MTEzNCwianRpIjoiODVjNmRkYmItOGY2Yy00MjBkLTkwZjMtMGI0NjA4ZjBmM2ZkIiwicGVyc29uX2lkIjoiLTIifQ.9OpnhHg54ymIce_e1EKoGX6Hrmcv4yJl-O24739NyQc';
        $res = self::getCache($cachePath, $forceUpdate,
            "https://b.linkedcare.cn/etrade/product/getNewProductDetail",
            [
                "clientType" => "PC",
                "userId" => "::",
                "shoppingGroup" => 0,
                "productId" => $productId,
                "skuId" => $skuId,
                "tradePriceType" => null
            ],
            'POST',
            [
                "authorization" => $token
            ]
        );
        if (isset($res['data']['errorType'])) {
            self::update(source: $source, msg: $res['msg']);
            return;
        }

        $sku = false;
        if (count($res['data']['productSku']) > 1) {
            foreach ($res['data']['productSku'] as $item) {
                if ($item['id'] == $skuId) {
                    $sku = $item;
                    break;
                }
            }
            // 根据ID 找不到，根据名称再找一次
            if (empty($sku)) {
                $skuTable = new ParityProductsSkuTable()->addWhere(ParityProductsSkuTable::ID, $source->skuId)->selectOne();
                $name = $skuTable->name;
                foreach ($res['data']['productSku'] as $item) {
                    $cName = $item['specification'];

                    if ($cName == $name || stripos($name, $cName) || stripos($cName, $name)) {
                        $sku = $item;
                        break;
                    }
                }
            }
        } else {
            $sku = $res['data']['productSku'][0];
        }

        if (empty($sku)) {
            self::update(source: $source, msg: '找不到规格');
            return;
        }

        $picture = $sku['imgList'][0];

        $price[] = $sku['showPrice'] ?? 999999999;
        $price[] = $sku['salesPrice'] ?? 999999999;
//        $price[] = $sku['reminderPrice'] ?? 999999999;
        $price = self::getMinPrice($price);
        if ($price == 999999999) {
            self::update(source: $source, msg: '找不到对应的规格');
        } else {
            self::update($source, $price, $picture);
        }

    }

    /**
     * 牙医帮
     * @throws Throwable
     */
    public static function yyb(ParitySourceTable $source, bool $forceUpdate = false): void
    {
        $id = $source->id;
        $url = $source->url;
        $r = explode('?', $url);
        parse_str($r[1], $r2);


        $cachePath = RUNTIME_DIR . "crawler/yyb/$id.txt";
        $res = self::getCache(cachePath: $cachePath, forceUpdate: $forceUpdate,
            url: "https://api-v2.yayibang.com/index.php?method=Goods.detail&plat=pc&api=MobileV389&ver=3.8.9&imei=1cfece08-2571-4fd4-873a-082b1416cb42",
            headers: [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            bodyParams: [
                'user_id' => '',
                'sign' => '',
                'goods_spec_id' => $r2['goods_spec_id'],
            ],
        );
        if (!isset($res['result']['image'])) {
            self::update(source: $source, msg: '找不到商品，商品已经下架');
            return;
        }
        $picture = $res['result']['image'];
        $price[] = $res['result']['goods_amount'];
        $price[] = $res['result']['price'];
        $price[] = $res['result']['price_market'];
        $price[] = $res['result']['price_min'];
        $price = self::getMinPrice($price);

        self::update($source, $price, $picture);
    }

    private static function getMinPrice(array $prices)
    {
        $ret = [];
        foreach ($prices as $price) {
            if ($price > 0) {
                $ret[] = $price;
            }
        }
        return min($ret);
    }

    /**
     * @throws GuzzleException
     * @throws AppException
     */
    private static function getCache(string $cachePath, bool $forceUpdate, string $url, array $jsonParams = [], string $method = "POST", array $headers = [], array $bodyParams = []): false|array|null
    {
        $dir = dirname($cachePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
        $res = null;
        if ($forceUpdate === false) {
            if (is_file($cachePath)) {
                $res = file_get_contents($cachePath);
            }
        }


        if ($res === null) {
            // 创建 StreamHandler
            $streamHandler = new StreamHandler();
            // 创建 HandlerStack 并设置 StreamHandler
            $handlerStack = HandlerStack::create($streamHandler);
            $client = new Client(['handler' => $handlerStack]);

            $headers = array_merge($headers, [
                "User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
            ]);


            $data = [];
            $data['headers'] = $headers;

            if ($jsonParams) {
                $data['json'] = $jsonParams;
            }
            if ($bodyParams) {
                $data['form_params'] = $bodyParams;
            }
            $response = $client->request($method, $url, $data);

            $code = $response->getStatusCode();

            if ($code != 200) {
                throw new AppException("请求异常 error code %d", $code);
            }

            $res = $response->getBody()->getContents();
            file_put_contents($cachePath, $res);
        }
        return json_decode($res, true);
    }


    /**
     * @throws Throwable
     */
    private static function update(ParitySourceTable $source, float $price = 0, string $picture = '', string $msg = ''): void
    {
        $updateSource = [
            ParitySourceTable::LAST_RUN_TIME => time(),
        ];

        if ($price) {
            $updateSource[ParitySourceTable::PRICE] = $price;
        }
        if ($picture) {
            $picture = self::uploadPicture($picture);
        }


        if ($msg) {
            $updateSource[ParitySourceTable::ERR_MSG] = $msg;
        } else {
            $updateSource[ParitySourceTable::ERR_MSG] = "";
        }
        if ($picture) {
            $updateSource[ParitySourceTable::PICTURE] = $picture;
        }

        new ParitySourceTable()->addWhere(ParitySourceTable::ID, $source->id)->update($updateSource);


        $minPrice = new ParitySourceTable()->where([
            [ParitySourceTable::SKU_ID, '=', $source->skuId],
            [ParitySourceTable::PRICE, '>', 0],
        ])->min(ParitySourceTable::PRICE);
        $updateProduct = [
            ParityProductsTable::PRICE => $minPrice,
        ];
        if ($picture) {
            $updateProduct[ParityProductsTable::PICTURE] = $picture;
        }

        new ParityProductsTable()->addWhere(ParityProductsTable::ID, $source->productId)->update($updateProduct);

        $updateSku = [
            ParityProductsSkuTable::PRICE => $minPrice,
        ];
        if ($picture) {
            $updateSku[ParityProductsSkuTable::PICTURE] = $picture;
        }
        new ParityProductsSkuTable()->addWhere(ParityProductsSkuTable::ID, $source->skuId)->update($updateSku);
    }

    /**
     * @throws OssException
     * @throws RequestCore_Exception
     */
    public static function uploadPicture(string $picture): false|string
    {
        // 初始化cURL会话
        $ch = curl_init($picture);

        // 设置cURL选项
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 将响应数据返回到变量中

        // 执行cURL请求
        $response = curl_exec($ch);

        // 获取HTTP状态码
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // 关闭cURL会话
        curl_close($ch);

        $hz = '.jpg';
        if (str_contains($picture, '.png')) {
            $hz = '.png';
        } elseif (str_contains($picture, '.jpeg')) {
            $hz = '.jpeg';
        } elseif (str_contains($picture, '.gif')) {
            $hz = '.gif';
        } elseif (str_contains($picture, '.webp')) {
            $hz = '.webp';
        }

        $filename = uniqid() . date('YmdHis') . $hz;
        // 检查状态码
        if ($httpCode == 200) {
            $provider = new EnvironmentVariableCredentialsProvider();

            $endpoint = "https://oss-cn-chengdu.aliyuncs.com";
            $bucket = "fryl-shop-image";
            $object = "upload/$filename";


            $config = array(
                "provider" => $provider,
                "endpoint" => $endpoint,
            );


            $ossClient = new OssClient($config);
            $ossClient->putObject($bucket, $object, $response);

            return "https://static.zhonguoyagu.com/upload/$filename";
        } else {
            return false;
        }


    }

}