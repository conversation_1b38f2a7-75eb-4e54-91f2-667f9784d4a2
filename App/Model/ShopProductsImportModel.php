<?php

namespace App\Model;


use Exception;
use Generate\Tables\Datas\BrandTable;
use Generate\Tables\Datas\ParityProductsSkuTable;
use Generate\Tables\Datas\ParityProductsTable;
use Generate\Tables\Datas\ParitySourceTable;
use Generate\Tables\Datas\ProductSkuTable;
use Generate\Tables\Datas\ProductsTable;
use Swlib\Connect\PoolRedis;
use Swlib\Exception\AppException;
use Swlib\Queue\MessageQueue;
use Swlib\Utils\Log;
use Redis;
use Swoole\Coroutine;
use Throwable;
use Vtiful\Kernel\Excel;

class ShopProductsImportModel
{

    /**
     * @throws Exception
     * @throws Throwable
     */
    public function import(array $data): bool
    {

        $filepath = $data["filePath"];
        if (!is_file($filepath)) {
            throw new AppException("文件不存在%s", $filepath);
        }

        $dir = dirname($filepath);
        $filename = basename($filepath); // 取得文件名称

        // 读取文件
        $config = ['path' => $dir];
        $excel = new Excel($config);
        $excel = $excel->openFile($filename)->setType([
            0 => Excel::TYPE_STRING,
            1 => Excel::TYPE_STRING,
            2 => Excel::TYPE_STRING,
            3 => Excel::TYPE_STRING,
            4 => Excel::TYPE_STRING,
            5 => Excel::TYPE_STRING,
            6 => Excel::TYPE_STRING,
            7 => Excel::TYPE_STRING,
            8 => Excel::TYPE_STRING,
            9 => Excel::TYPE_STRING,
        ])->openSheet();

        $xlsxHeader = $excel->nextRow();
        $header = [
            "A" => '商品名称',
            "B" => '规格名称',
            "C" => '商品分类',
            "D" => '励齿编码',
            "E" => '牙易编码',
            "F" => '苗苗编码',
            "G" => '松佰编码',
            "H" => 'e看牙编码',
            "I" => '佳沃思编码',
            "J" => '牙医帮编码',
            "K" => '品牌名称',
        ];


        foreach (array_values($header) as $index => $value) {
            if ($xlsxHeader[$index] != $value) {
                throw new AppException("导入的表格不正确 %s", var_export($xlsxHeader, true));
            }
        }

        $progress = $data['_progress'];
        $count = 0;
        // 此处判断请使用【!==】运算符进行判断；
        // 如果使用【!=】进行判断，出现空行时，返回空数组，将导致读取中断；
        while (($row = $excel->nextRow()) !== NULL) {
            $count++;
            // 已经执行过了，被中断的任务
            if ($count <= $progress) {
                continue;
            }
            try {

                $this->_exec($row);

                MessageQueue::updateProgress($data['_msgId'], $count);
            } catch (Exception $e) {
                Log::saveException($e, 'importCSV');
            }
        }

        return true;
    }


    /**
     * @throws Throwable
     */
    private function _exec($row): void
    {

        if (empty($row[0]) || empty($row[1])) return;

        $name = $row[0];
        $skuName = $row[1];
        $baranName = $row[10];
        // 写入商品
        $productId = $this->saveProduct($name, $baranName);
        $skuId = $this->saveSku($productId, $skuName);

        Coroutine::create(function () use ($row, $productId, $skuId, $name, $skuName) {
            $liChi = $row[3] ?? '';
            $this->saveSourceInfo(2, $liChi, $productId, $skuId);
        });

        Coroutine::create(function () use ($row, $productId, $skuId, $name, $skuName) {
            $yaYiZaiXian = $row[4] ?? '';
            $this->saveSourceInfo(3, $yaYiZaiXian, $productId, $skuId);
        });

        Coroutine::create(function () use ($row, $productId, $skuId, $name, $skuName) {
            $meiMiaoMiao = $row[5] ?? '';
            $this->saveSourceInfo(1, $meiMiaoMiao, $productId, $skuId);
        });

        Coroutine::create(function () use ($row, $productId, $skuId, $name, $skuName) {
            $songBai = $row[6] ?? '';
            $this->saveSourceInfo(4, $songBai, $productId, $skuId);
        });

        Coroutine::create(function () use ($row, $productId, $skuId, $name, $skuName) {
            $eKanYa = $row[7] ?? '';
            $this->saveSourceInfo(5, $eKanYa, $productId, $skuId);
        });

        Coroutine::create(function () use ($row, $productId, $skuId, $name, $skuName) {
            $yaYiBang = $row[9] ?? '';
            $this->saveSourceInfo(7, $yaYiBang, $productId, $skuId);
        });

    }


    /**
     * @throws Throwable
     */
    private function saveSourceInfo(int $sourceId, string $url, int $productId, int $skuId): void
    {
        if (empty($url)) return;
        $url = trim($url);

        if (empty($url)) {
            return;
        }

        if (!str_starts_with($url, 'http') && !str_starts_with($url, 'https')) {
            $url = $this->getSourceUrl($sourceId, $url);
        }

        $id = new ParitySourceTable()->where([
            [ParitySourceTable::SOURCE_ID, '=', $sourceId],
            [ParitySourceTable::PRODUCT_ID, '=', $productId],
            [ParitySourceTable::SKU_ID, '=', $skuId],
        ])->selectField(ParitySourceTable::ID);

        if ($id) {
            new ParitySourceTable()->where([
                ParitySourceTable::ID=>$id
            ])->update([
                ParitySourceTable::URL => $url
            ]);
        } else {
            new ParitySourceTable()->insert([
                ParitySourceTable::SOURCE_ID => $sourceId,
                ParitySourceTable::URL => $url,
                ParitySourceTable::PRODUCT_ID => $productId,
                ParitySourceTable::SKU_ID => $skuId
            ]);
        }

    }

    /**
     * @throws Throwable
     */
    private function getSourceUrl(int $sourceId, string $code): string
    {
        // 从商品规格中查找
        $url = new ProductSkuTable()->where([
            [ProductSkuTable::SOURCE_ID, '=', $sourceId],
            [ProductSkuTable::CODE, '=', $code],
        ])->selectField(ProductSkuTable::URL);

        if (!empty($url)) {
            return $url;
        }

        // 从商品表查找
        $url = new ProductsTable()
            ->where([
                [ProductsTable::SOURCE_ID, '=', $sourceId],
                [ProductsTable::CODE, '=', $code],
            ])
            ->join(ProductSkuTable::TABLE_NAME, ProductSkuTable::PRODUCT_ID, ProductsTable::ID)
            ->selectField(ProductSkuTable::URL);
        if (!empty($url)) {
            return $url;
        }


        return '';
    }


    /**
     * @throws Throwable
     */
    private function saveProduct(string $name, string $baranName): int
    {
        $id = new ParityProductsTable()->where([
            [ParityProductsTable::NAME, '=', $name]
        ])->selectField(ParityProductsTable::ID);

        if (!empty($id)) {
            return $id;
        }

        return new ParityProductsTable()->insert([
            ParityProductsTable::NAME => $name,
            ParityProductsTable::BRAND_ID => $this->getBaranId($baranName),
        ]);

    }

    /**
     * @throws Throwable
     */
    private function saveSku(int $productId, string $skuName): int
    {

        $skuId = new ParityProductsSkuTable()->where([
            [ParityProductsSkuTable::PRODUCT_ID, '=', $productId],
            [ParityProductsSkuTable::NAME, '=', $skuName],
        ])->selectField(ParityProductsSkuTable::ID);

        if ($skuId) {
            return $skuId;
        }

        return new ParityProductsSkuTable()->insert([
            ParityProductsSkuTable::PRODUCT_ID => $productId,
            ParityProductsSkuTable::NAME => $skuName,
        ]);

    }


    /**
     * @throws Throwable
     */
    private function getBaranId($name): int
    {
        if (empty($name)) {
            return 0;
        }
        $id = PoolRedis::call(function (Redis $redis) use ($name) {
            $id = $redis->hGet('brandName2Id', $name);
            if ($id) {
                return $id;
            }

            $id = new BrandTable()->where([
                [BrandTable::NAME, '=', $name]
            ])->selectField(BrandTable::ID);


            if (empty($id)) {
                $id = new BrandTable()->insert([
                    BrandTable::NAME => $name,
                    BrandTable::PICTURE => '',
                    BrandTable::PREFIX => ''
                ]);
            }
            $redis->hSet('brandName2Id', $name, $id);
            return $id;
        });
        return intval($id);

    }


}