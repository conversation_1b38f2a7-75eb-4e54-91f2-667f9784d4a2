<?php

namespace App\Model;

use Generate\Tables\Datas\ZpCompaniesTable;
use Protobuf\Datas\ZpCompanies\ZpCompaniesAuthTypeEnum;
use Protobuf\Datas\ZpCompanies\ZpCompaniesProto;
use Protobuf\Datas\ZpCompanies\ZpCompaniesStatusEnum;
use Throwable;

class ZpCompaniesModel
{
    /**
     * @throws Throwable
     */
    public static function formatDetail(ZpCompaniesTable $find, int $focusCount, bool $isFocus): ZpCompaniesProto
    {
        $message = new ZpCompaniesProto();
        $message->setId($find->id);
        $message->setName($find->name);
        $message->setProvince($find->province);
        $message->setCity($find->city);
        $message->setLocation($find->location);
        $message->setDescription($find->description);
        $message->setUrl($find->url);
        $message->setLogo($find->logo);
        $sloganWelfare = $find->sloganWelfare ?: [];
        $message->setSloganWelfare($sloganWelfare);
        $message->setEnvironmentPics($find->environmentPics ?: []);
        $message->setFocusCount($focusCount);
        $message->setIsFocus($isFocus);
        $message->setBusinessLicense($find->businessLicense);
        $message->setPermit($find->permit);
        $message->setStatus(ZpCompaniesStatusEnum::value($find->status ?: 'UNDEFINED_STATUS'));
        $message->setAuthType(ZpCompaniesAuthTypeEnum::value($find->authType ?: 'UNDEFINED_AUTHTYPE'));
        $message->setContactsName($find->contactsName);
        $message->setContactsPhone($find->contactsPhone);
        $message->setExpireTime($find->expireTime);
        $message->setMsg($find->msg);
        return $message;

    }
}