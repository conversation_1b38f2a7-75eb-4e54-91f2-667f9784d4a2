<?php

namespace App\Model;

use Generate\Tables\Datas\BrandTable;
use Generate\Tables\Datas\FootprintTable;
use Generate\Tables\Datas\ProductSkuTable;
use Generate\Tables\Datas\ShopProductsImageTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Generate\Tables\Datas\SourceTable;
use Protobuf\Product\ShopProductImageItem;
use Protobuf\Product\ShopProductItem;
use Protobuf\Product\ShopProductLists;
use Protobuf\Product\ShopProductRequest;
use Protobuf\Product\ShopProductSku;
use Protobuf\Product\ShopSourcePrice;
use Throwable;

class ShopProductsModel
{

    /**
     * @throws Throwable
     */
    public static function formatSkuByShopCar(ShopProductsSkuTable $sku, $carId = 0): ShopProductSku
    {
        $skuItem = new ShopProductSku();
        $skuItem->setId($sku->id);
        $skuItem->setName($sku->name);
        $skuItem->setUnit($sku->unit ?: '');
        $skuItem->setPrice($sku->price);
        $skuItem->setMarketPrice($sku->marketPrice);
        $skuItem->setPicture($sku->picture);
        $skuItem->setCarId($carId);
        return $skuItem;
    }

    /**
     * @throws Throwable
     */
    public function getLists(ShopProductRequest $request): ShopProductLists
    {
        $page = $request->getPage() ?: 1;
        $size = $request->getSize() ?: 10;
        $sort = $request->getSort();
        $sortField = $request->getSortField();
        $where = $this->getListsWhere($request);


        $order = [];
        if ($sort && $sortField) {
            $order[$sortField] = $sort;
        }

        $shopProductsTable = new ShopProductsTable();
        $lists = $shopProductsTable->order($order)->page($page, $size)->where($where)->selectAll();

        $total = (new ShopProductsTable)->where($where)->count();

        $ret = [];
        if (!empty($lists)) {
            $brandIds = $shopProductsTable->getArrayByField(ShopProductsTable::BRAND_ID);// 品牌编码
            $brands = $this->getBrands($brandIds);

            $productIds = $shopProductsTable->getArrayByField(ShopProductsTable::ID);
            $imgRet = $this->getImages($productIds);
            $productSkus = $this->getProductSkus($productIds);

            foreach ($lists as $list) {
                $ret[] = $this->formatDetail($list, $productSkus, $brands, $imgRet);
            }
        }

        $message = new ShopProductLists();
        $message->setLists($ret);
        $message->setTotal($total);
        $message->setTotalPage(ceil($total / $size));

        return $message;
    }


    /**
     * @throws Throwable
     */
    public function formatDetail(ShopProductsTable $shopProductTable, array $productSkus, array $brands, array $imgRet): ShopProductItem
    {

        $productMessage = new ShopProductItem();

        $skus = [];
        /**@var ShopProductsSkuTable $skuTable */
        foreach ($productSkus as $skuTable) {
            if ($skuTable->productId != $shopProductTable->id) {
                continue;
            }
            $sourceInfo = $skuTable->sourceInfo;

            $sourcePriceMessages = [];
            if ($sourceInfo) {
                $sourceInfoArr = json_decode($sourceInfo, true);
                if ($sourceInfoArr) {
                    // 更新最新的价格
                    $sourceInfoArr = $this->getNewSourcePrice($shopProductTable, $sourceInfoArr, $skuTable->id);
                    usort($sourceInfoArr, function ($a, $b) {
                        return $a['price'] <=> $b['price'];
                    });
                    foreach ($sourceInfoArr as $item) {
                        $sourcePriceMessage = new ShopSourcePrice();
                        $sourcePriceMessage->setPrice($item['price']);
                        $sourcePriceMessage->setSourceId(intval($item['id']));
                        $sourcePriceMessage->setSourceName($item['source_name']);
                        $sourcePriceMessage->setIcon($item['source_icon']);
                        $sourcePriceMessage->setUrl($item['url']);
                        $sourcePriceMessages[] = $sourcePriceMessage;
                    }
                }
            }


            $skuMessage = new ShopProductSku();
            $skuMessage->setId($skuTable->id);
            $skuMessage->setProductId($skuTable->productId);
            $skuMessage->setName($skuTable->name);
            $skuMessage->setPrice($skuTable->price);
            $skuMessage->setMarketPrice($skuTable->marketPrice);
            $skuMessage->setSourcePrices($sourcePriceMessages);
            $skuMessage->setUnit($skuTable->unit);
            $skus[] = $skuMessage;

        }
        // 获取产品图片
        $productMessage->setPicture($shopProductTable->picture ?: '');
        // 有效期
        $productMessage->setValidUntil($shopProductTable->validUntil ?: '');
        // 批文号
        $productMessage->setApprovalNum($shopProductTable->approvalNumber ?: '');
        $productMessage->setId($shopProductTable->id);
        $productMessage->setName($shopProductTable->name);
        $productMessage->setCode($shopProductTable->code);
        $productMessage->setUnit($shopProductTable->unit);
        $productMessage->setSaleNum(0);
        $productMessage->setPrice($shopProductTable->price);
        $productMessage->setMarketPrice($shopProductTable->marketPrice);
        $productMessage->setBrand($brands[$shopProductTable->brandId] ?? '');
        $productMessage->setSkus($skus);
        $productMessage->setBusinessId($shopProductTable->businessId ?: 0);
        $productMessage->setImages($imgRet[$shopProductTable->id] ?? []);

        return $productMessage;
    }

    /**
     * @throws Throwable
     */
    private function getNewSourcePrice(ShopProductsTable $shopProductTable, array $sourceInfoArr, int $skuId): array
    {

        $sourceIds = [];

        if ($code = $shopProductTable->smmm) {
            $sourceIds[] = 1;
            $sourceInfoArr = $this->updateSourceInfo($skuId, $sourceInfoArr, 1, [
                [ProductSkuTable::CODE, '=', $code],
                [ProductSkuTable::SOURCE_ID, '=', 1]
            ]);
        }
        if ($code = $shopProductTable->slc) {
            $sourceIds[] = 2;
            $sourceInfoArr = $this->updateSourceInfo($skuId, $sourceInfoArr, 2, [
                [ProductSkuTable::CODE, '=', $code],
                [ProductSkuTable::SOURCE_ID, '=', 2]
            ]);
        }
        if ($code = $shopProductTable->syy) {
            $sourceIds[] = 3;
            $sourceInfoArr = $this->updateSourceInfo($skuId, $sourceInfoArr, 3, [
                [ProductSkuTable::CODE, '=', $code],
                [ProductSkuTable::SOURCE_ID, '=', 3]
            ]);
        }
        if ($code = $shopProductTable->ssb) {
            $sourceIds[] = 4;
            $sourceInfoArr = $this->updateSourceInfo($skuId, $sourceInfoArr, 4, [
                [ProductSkuTable::CODE, '=', $code],
                [ProductSkuTable::SOURCE_ID, '=', 4]
            ]);
        }
        if ($code = $shopProductTable->seky) {
            $sourceIds[] = 5;
            $sourceInfoArr = $this->updateSourceInfo($skuId, $sourceInfoArr, 5, [
                [ProductSkuTable::CODE, '=', $code],
                [ProductSkuTable::SOURCE_ID, '=', 5]
            ]);
        }
        if ($code = $shopProductTable->syyb) {
            $sourceIds[] = 7;
            $sourceInfoArr = $this->updateSourceInfo($skuId, $sourceInfoArr, 7, [
                [ProductSkuTable::CODE, '=', $code],
                [ProductSkuTable::SOURCE_ID, '=', 7]
            ]);
        }


        // 如果都没有填写编码;出现了来源；则删除
        foreach ($sourceInfoArr as $key => $source) {
            if (!in_array($source['id'], $sourceIds)) {
                unset($sourceInfoArr[$key]);
            }
        }

        $lists = new ProductSkuTable()
            ->field([
                ProductSkuTable::PRICE,
                ProductSkuTable::CODE,
                ProductSkuTable::SOURCE_ID
            ])
            ->where([
                [ProductSkuTable::CODE, 'in', array_column($sourceInfoArr, 'code')],
                [ProductSkuTable::SOURCE_ID, 'in', array_column($sourceInfoArr, 'id')],
            ])
            ->selectAll();

        foreach ($sourceInfoArr as $key => $source) {
            foreach ($lists as $list) {
                if (
                    $source['id'] == $list->sourceId
                    && $source['code'] == $list->code
                ) {
                    $sourceInfoArr[$key]['price'] = $list->price;
                }
            }
        }

        // 有重复的，删除
        $sourceInfoArr2 = array_column($sourceInfoArr, null, 'id');
        if (count($sourceInfoArr2) != count($sourceInfoArr)) {
            new ShopProductsSkuTable()
                ->where([
                    [ShopProductsSkuTable::ID, '=', $skuId],
                ])->update([
                    ShopProductsSkuTable::SOURCE_INFO => json_encode($sourceInfoArr2, JSON_UNESCAPED_UNICODE)
                ]);
        }
        return $sourceInfoArr2;
    }

    /**
     * @throws Throwable
     */
    private function updateSourceInfo(int $skuId, array $sourceInfo, int $sourceId, array $where): array
    {
        $isFind = false;
        foreach ($sourceInfo as $s) {
            if ((isset($s['source_id']) && $s['source_id'] == $sourceId) || (isset($s['id']) && $s['id'] == $sourceId)) {
                $isFind = true;
                break;
            }
        }

        if ($isFind === true) {
            return $sourceInfo;
        }

        $sourceSku = (new ProductSkuTable)->where($where)->selectOne();
        if (empty($sourceSku)) {
            return $sourceInfo;
        }

        /** @var $source SourceTable */
        $source = new SourceTable()->cache(mt_rand(14400, 86400 * 7))->where([
            [SourceTable::ID, '=', $sourceId]
        ])->selectOne();

        $sourceInfo[] = [
            'id' => $sourceId,
            'source_id' => $sourceId,
            'source_name' => $source->name,
            'source_icon' => $source->icon,
            'unit' => $sourceSku->unit,
            'url' => $sourceSku->url,
            'code' => $sourceSku->code,
            'price' => $sourceSku->price,
            'update_time' => time()
        ];

        // 二维数组根据一个字段去重
        $sourceInfo = array_column($sourceInfo, null, 'id');
        new ShopProductsSkuTable()->where([
            [ShopProductsSkuTable::ID, '=', $skuId],
        ])->update([
            ShopProductsSkuTable::SOURCE_INFO => json_encode($sourceInfo, JSON_UNESCAPED_UNICODE)
        ]);

        return $sourceInfo;
    }

    /**
     * @param integer[] $productIdArr
     * @throws Throwable
     */
    public function getImages(array $productIdArr): array
    {
        $images = (new ShopProductsImageTable)->where([
            [ShopProductsImageTable::PRODUCT_ID, 'in', $productIdArr]
        ])->selectAll();
        $imgRet = [];
        foreach ($images as $image) {
            $productId = $image->productId;
            if (!array_key_exists($productId, $imgRet)) {
                $imgRet[$productId] = [];
            }
            $imgMsg = new ShopProductImageItem();
            $imgMsg->setUrl($image->url);
            $imgRet[$productId][] = $imgMsg;
        }
        return $imgRet;
    }


    /**
     * @throws Throwable
     */
    public function getListsWhere(ShopProductRequest $request): array
    {
        $keyword = $request->getKeyword();
        $categoryId = $request->getCategoryId();
        $brandId = $request->getBrandId();
        $recommendedForYou = $request->getRecommendedForYou();// 查询为你推荐
        $userId = $request->getUserId();

        $where = [];
        if ($recommendedForYou && $userId) {
            // 查询为你推荐
            $productIds = (new FootprintTable)->limit(10)->where([
                [FootprintTable::USER_ID, '=', $userId]
            ])->getArrayByField(FootprintTable::PRODUCT_ID);
            if ($productIds) {
                $where[] = [ShopProductsTable::ID, 'in', $productIds];
            }
        } else {
            // 6650 是全部分类，就不查询分类
            if ($categoryId && $categoryId != 6650) {
                $categoryIds = ShopCategoriesModel::getChildrenIds($categoryId);
                $where[] = [ShopProductsTable::CATEGORY_ID, 'in', $categoryIds];
            }

            if ($keyword) {
                $where[] = [ShopProductsTable::NAME, 'like', "%$keyword%"];
                HistorySearchModel::save($keyword, $userId);
            }
            if ($brandId) {
                $where[] = [ShopProductsTable::BRAND_ID, '=', $brandId];
            }
        }


        return $where;
    }

    /**
     * @throws Throwable
     */
    public function getProductSkus(array $productIds): array
    {
        return (new ShopProductsSkuTable)
            ->where([
                [ShopProductsSkuTable::PRODUCT_ID, 'in', $productIds]
            ])->selectAll();

//        ->field('
//            product_sku.code as sku_code,
//            product_sku.name as sku_name,
//            product_sku.unit,
//            product_sku.price,
//            product_sku.market_price,
//            product_sku.url,
//            products.picture,
//            products.product_id,
//            products.name,
//            products.url as products_url,
//            products.valid_until,
//            products.source_id,
//            products.approval_number
//            ')
    }


    /**
     * @throws Throwable
     */
    public function getBrands(array $brandIds): array
    {
        return (new BrandTable)->where([
            [BrandTable::ID, 'in', $brandIds]
        ])->formatId2Name(BrandTable::ID, BrandTable::NAME);
    }


}