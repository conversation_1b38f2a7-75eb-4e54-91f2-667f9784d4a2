<?php

namespace App\Model;

use Generate\Tables\Datas\BrandTable;
use Generate\Tables\Datas\ProductSkuTable;
use Generate\Tables\Datas\ProductsTable;
use Generate\Tables\Datas\SourceTable;
use Swlib\Queue\MessageQueue;
use Swlib\Table\JoinEnum;
use Throwable;
use Vtiful\Kernel\Excel;

class ProductsExport
{
    /**
     * @throws Throwable
     */
    public function export(array $data): bool
    {
        $filePath = $data['filePath'];
        $saveDir = dirname($filePath);
        $fileName = basename($filePath);

        $config = [
            'path' => $saveDir, // xlsx文件保存路径
        ];
        $excel = new Excel($config);
        // fileName 会自动创建一个工作表，你可以自定义该工作表名称，工作表名称为可选参数
        $filePath = $excel->fileName($fileName, 'sheet1')
            ->header([
                '商品ID',
                '规格编码',
                '商品名称',
                '规格名称',
                '销售价',
                '市场价',
                '单位',
                '平台',
                '品牌名称',
                '源链接',
            ]);


        $query = new ProductSkuTable()
            ->join(ProductsTable::TABLE_NAME, ProductsTable::ID, ProductSkuTable::PRODUCT_ID)
            ->join(SourceTable::TABLE_NAME, SourceTable::ID, ProductSkuTable::SOURCE_ID, JoinEnum::LEFT)
            ->join(BrandTable::TABLE_NAME, BrandTable::ID, ProductsTable::BRAND_ID, JoinEnum::LEFT);
        $query->field([
            ProductsTable::ID,
            ProductSkuTable::CODE,
            ProductsTable::NAME,
            ProductSkuTable::NAME,
            ProductSkuTable::PRICE,
            ProductSkuTable::MARKET_PRICE,
            ProductSkuTable::UNIT,
            ProductSkuTable::SOURCE_ID,
            ProductsTable::BRAND_ID,
            SourceTable::NAME,
            BrandTable::NAME,
            ProductSkuTable::URL,
        ]);
        $countQuery = clone $query;
        $count = $countQuery->count();


        $rowIndex = 1;

        /** @var ProductSkuTable $sku */
        foreach ($query->generator() as $sku) {

            $filePath->insertText($rowIndex, 0, $sku->getByField(ProductsTable::ID));
            $filePath->insertText($rowIndex, 1, $sku->getByField(ProductSkuTable::CODE));
            $filePath->insertText($rowIndex, 2, $sku->getByField(ProductsTable::NAME));
            $filePath->insertText($rowIndex, 3, $sku->getByField(ProductSkuTable::NAME));
            $filePath->insertText($rowIndex, 4, $sku->getByField(ProductSkuTable::PRICE));
            $filePath->insertText($rowIndex, 5, $sku->getByField(ProductSkuTable::MARKET_PRICE));
            $filePath->insertText($rowIndex, 6, $sku->getByField(ProductSkuTable::UNIT));
            $filePath->insertText($rowIndex, 7, $sku->getByField(SourceTable::NAME));
            $filePath->insertText($rowIndex, 8, $sku->getByField(BrandTable::NAME));
            $filePath->insertText($rowIndex, 9, $sku->getByField(ProductSkuTable::URL));
            $rowIndex++;
            MessageQueue::updateProgress($data['_msgId'], round($rowIndex / $count * 100, 2));
        }
        $filePath->output();
        return true;
    }
}