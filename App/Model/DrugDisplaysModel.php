<?php

namespace App\Model;

use Generate\Tables\Datas\DrugDisplaysTable;
use Throwable;

class DrugDisplaysModel
{
    /**
     * @throws Throwable
     */
    public static function getDrugLists(array $hyCompaniesIds): array
    {
        $all = new DrugDisplaysTable()->where([
            [DrugDisplaysTable::HY_COMPANIES_ID, 'in', $hyCompaniesIds]
        ])->selectAll();

        $nodes = [];
        foreach ($all as $value) {
            $hyCompaniesId = $value->hyCompaniesId;
            if (!isset($nodes[$hyCompaniesId])) {
                $nodes[$hyCompaniesId] = [];
            }
            $nodes[$hyCompaniesId][] = \Generate\Models\Datas\DrugDisplaysModel::formatItem($value);
        }

        return $nodes;
    }


    /**
     * @throws Throwable
     */
    public static function getListsByKeyword(string $keyword): array
    {
        return new DrugDisplaysTable()->where([
            [DrugDisplaysTable::NAME, 'like', "%$keyword%"]
        ])->getArrayByField(DrugDisplaysTable::HY_COMPANIES_ID);
    }

}