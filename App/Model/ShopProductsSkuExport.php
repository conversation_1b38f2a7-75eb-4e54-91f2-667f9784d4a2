<?php

namespace App\Model;

use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Swlib\Queue\MessageQueue;
use Throwable;
use Vtiful\Kernel\Excel;

class ShopProductsSkuExport
{
    /**
     * @throws Throwable
     */
    public function export(array $data): bool
    {
        $filePath = $data['filePath'];
        $saveDir = dirname($filePath);
        $fileName = basename($filePath);
        $config = [
            'path' => $saveDir, // xlsx文件保存路径
        ];
        $excel = new Excel($config);

        // fileName 会自动创建一个工作表，你可以自定义该工作表名称，工作表名称为可选参数
        $filePath = $excel->fileName($fileName, 'sheet1')
            ->header([
                'skuId',
                '商品名称',
                '规格名称',
                '梅苗苗',
                '励齿商城',
                '牙e在线',
                '松佰商城',
                'e看牙商城',
                '牙医帮',
                '最高价',
                '最低价',
                '(最高价-最低价)/最低价',
            ]);

        $query = new ShopProductsSkuTable()->join(ShopProductsTable::TABLE_NAME, ShopProductsTable::ID, ShopProductsSkuTable::PRODUCT_ID);
        $query->field([
            ShopProductsSkuTable::ID,
            ShopProductsTable::NAME,
            ShopProductsSkuTable::NAME,
            ShopProductsSkuTable::SOURCE_INFO,
        ]);
        $countQuery = clone $query;
        $count = $countQuery->count();

        $rowIndex = 1;
        /** @var ShopProductsSkuTable $product */
        foreach ($query->generator() as $product) {
            $sourceInfo = $product->sourceInfo ? json_decode($product->sourceInfo, true) : [];
            $prices = array_column($sourceInfo, 'price');
            $min = $prices ? min($prices) : '';
            $max = $prices ? max($prices) : '';

            $filePath->insertText($rowIndex, 0, $product->id);
            $filePath->insertText($rowIndex, 1, $product->getByField(ShopProductsTable::NAME));
            $filePath->insertText($rowIndex, 2, $product->name);
            $filePath->insertText($rowIndex, 3, $this->getSourcePrice($sourceInfo, 1));
            $filePath->insertText($rowIndex, 4, $this->getSourcePrice($sourceInfo, 2));
            $filePath->insertText($rowIndex, 5, $this->getSourcePrice($sourceInfo, 3));
            $filePath->insertText($rowIndex, 6, $this->getSourcePrice($sourceInfo, 4));
            $filePath->insertText($rowIndex, 7, $this->getSourcePrice($sourceInfo, 5));
            $filePath->insertText($rowIndex, 8, $this->getSourcePrice($sourceInfo, 7));
            $filePath->insertText($rowIndex, 9, $max ?: '');
            $filePath->insertText($rowIndex, 10, $min ?: '');
            $filePath->insertText($rowIndex, 11, $min > 0 ? (($max - $min) / $min) : '');


            $rowIndex++;
            MessageQueue::updateProgress($data["_msgId"], intval($rowIndex / $count * 100));
        }
        $filePath->output();
        return true;
    }

    private function getSourcePrice(array $arr, int $sourceId)
    {
        foreach ($arr as $item) {
            if (isset($item['source_id']) && $item['source_id'] == $sourceId || isset($item['id']) && $item['id'] == $sourceId) {
                return $item['price'];
            }
        }
        return '';
    }
}