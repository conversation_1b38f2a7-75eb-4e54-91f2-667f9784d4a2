<?php

namespace App\Model;

use Generate\Tables\Datas\HistorySearchTable;
use Swlib\Exception\AppException;
use Protobuf\Common\Request;
use Protobuf\Common\Success;
use Protobuf\Product\HistorySearchItem;
use Protobuf\Product\HistorySearchLists;
use Throwable;

class HistorySearchModel
{
    /**
     * @throws Throwable
     */
    public static function save(string $keyword, ?int $userId = null): void
    {
        // 搜索记录
        $id = new HistorySearchTable()->where([
            [HistorySearchTable::KEYWORD, '=', $keyword]
        ])->selectField(HistorySearchTable::ID);
        if (empty($id)) {
            $insert = [
                HistorySearchTable::KEYWORD => $keyword,
                HistorySearchTable::NUM => 1,
                HistorySearchTable::TIME => time(),
            ];
            if ($userId) {
                $insert[HistorySearchTable::USER_ID] = $userId;
            }
            new HistorySearchTable()->insert($insert);
        } else {
            new HistorySearchTable()->where([
                [HistorySearchTable::ID, '=', $id]
            ])->update([
                HistorySearchTable::NUM => HistorySearchTable::NUM . '+1',
                HistorySearchTable::TIME => time(),
            ]);
        }
    }

    /**
     * 查询用户的搜索历史记录
     * @throws Throwable
     */
    public static function lists(Request $page): HistorySearchLists
    {
        $pageNo = $page->getPage() ?: 1;
        $pageSize = $page->getSize() ?: 10;
        $userId = $page->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        $lists = new HistorySearchTable()->page($pageNo, $pageSize)->where([
            [HistorySearchTable::USER_ID, '=', $userId]
        ])->order([
            HistorySearchTable::TIME => 'desc'
        ])->selectAll();

        $total = new HistorySearchTable()->where([
            [HistorySearchTable::USER_ID, '=', $userId]
        ])->count();

        return self::formatData($total, $lists);
    }


    /**
     * 查询热门搜索历史
     * @throws Throwable
     */
    public static function listsByHot(Request $page): HistorySearchLists
    {
        $pageNo = $page->getPage() ?: 1;
        $pageSize = $page->getSize() ?: 10;

        $lists = new HistorySearchTable()->page($pageNo, $pageSize)->order([
            HistorySearchTable::NUM => 'desc'
        ])->selectAll();

        $total = new HistorySearchTable()->count();

        return self::formatData($total, $lists);
    }

    /**
     * @throws Throwable
     */
    public static function clear(Request $page): Success
    {
        $userId = $page->getUserId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }

        new HistorySearchTable()->where([
            [HistorySearchTable::USER_ID, '=', $userId]
        ])->delete();

        $success = new Success();
        $success->setSuccess(true);
        return $success;

    }


    /**
     * @throws Throwable
     */
    private static function formatData(int $total, array $lists): HistorySearchLists
    {
        $ret = [];
        /** @var HistorySearchTable $list */
        foreach ($lists as $list) {
            $item = new HistorySearchItem();
            $item->setId($list->id);
            $item->setNum($list->num);
            $item->setKeyword($list->keyword);
            $ret[] = $item;
        }

        $msg = new HistorySearchLists();
        $msg->setLists($ret);
        $msg->setTotal($total);

        return $msg;

    }

}