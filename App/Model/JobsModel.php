<?php

namespace App\Model;

use Generate\Tables\Datas\ZpCompaniesTable;
use Generate\Tables\Datas\ZpJobsLevelTable;
use Generate\Tables\Datas\ZpJobsTable;
use Generate\Tables\Datas\ZpJobsTypeTable;
use Generate\Tables\Datas\ZpJobTagTable;
use Generate\Tables\Datas\ZpTagTable;
use Protobuf\Companies\CompaniesItem;
use Protobuf\Jobs\JobsItem;
use Protobuf\Jobs\JobsLevelItem;
use Protobuf\Jobs\JobsTypeItem;
use Protobuf\Jobs\TagItem;
use Throwable;

class JobsModel
{


    /**
     * 获取公司信息
     * @throws Throwable
     */
    public static function getCompanies(array $companyIds, array $jobLists): void
    {
        if (empty($jobLists)) return;
        $tables = new ZpCompaniesTable()->getCacheByIds($companyIds, ZpCompaniesTable::ID);

        /**@var JobsItem $jobItem */
        foreach ($jobLists as $jobItem) {
            if (!isset($tables[$jobItem->getCompanyId()])) {
                continue;
            }
            $table = $tables[$jobItem->getCompanyId()];
            $CompaniesItem = new CompaniesItem();
            $CompaniesItem->setId($table->id);
            $CompaniesItem->setName($table->name);
            $CompaniesItem->setLogo($table->logo);
            $CompaniesItem->setProvince($table->province);
            $CompaniesItem->setCity($table->city);
            $CompaniesItem->setLocation($table->location);
            $CompaniesItem->setDescription($table->description);
            $jobItem->setCompany($CompaniesItem);
        }
    }

    /**
     * 获取职位分类信息
     * @throws Throwable
     */
    public static function getTypes(array $typeIds, array $jobLists): void
    {
        if (empty($jobLists)) return;
        $tables = new ZpJobsTypeTable()->getCacheByIds($typeIds, ZpJobsTypeTable::ID);
        /**@var ZpJobsTypeTable $table */
        /**@var JobsItem $jobItem */
        foreach ($jobLists as $jobItem) {
            if (!isset($tables[$jobItem->getTypeId()])) {
                continue;
            }
            $table = $tables[$jobItem->getTypeId()];
            $item = new JobsTypeItem();
            $item->setId($table->id);
            $item->setName($table->name);
            $jobItem->setType($item);
        }
    }

    /**
     * 获取职位级别信息
     * @throws Throwable
     */
    public static function getLevels(array $levelIds, array $jobLists): void
    {
        if (empty($jobLists)) return;
        $tables = new ZpJobsLevelTable()->getCacheByIds($levelIds, ZpJobsLevelTable::ID);

        /**@var JobsItem $jobItem */
        foreach ($jobLists as $jobItem) {
            if (!isset($tables[$jobItem->getLevelId()])) {
                continue;
            }
            $table = $tables[$jobItem->getLevelId()];
            $item = new JobsLevelItem();
            $item->setId($table->id);
            $item->setName($table->name);
            $jobItem->setLevel($item);
        }
    }


    /**
     * 获取职位标签
     * @throws Throwable
     */
    public static function getTags(array $jobIds, array $jobLists): void
    {
        if (empty($jobLists)) return;
        $query = new ZpJobTagTable()
            ->field([
                ZpTagTable::NAME,
                ZpJobTagTable::TAG_ID,
                ZpJobTagTable::JOB_ID,
            ])->cache()->addWhere(ZpJobTagTable::JOB_ID, $jobIds, 'in')
            ->join(ZpTagTable::TABLE_NAME, ZpTagTable::ID, ZpJobTagTable::TAG_ID);

        $tagMessages = [];
        foreach ($query->selectAll() as $item) {
            $jobId = $item->jobId;
            if (!array_key_exists($jobId, $tagMessages)) {
                $tagMessages[$jobId] = [];
            }

            $msgItem = new TagItem();
            $msgItem->setId($item->tagId);
            $msgItem->setName($item->getByField(ZpTagTable::NAME));
            $tagMessages[$item->jobId][] = $msgItem;
        }


        foreach ($jobLists as $jobItem) {
            $jobId = $jobItem->getId();
            if (!isset($tagMessages[$jobId])) {
                continue;
            }
            $jobItem->setTags($tagMessages[$jobId]);
        }
    }


    /**
     * 格式化职位信息
     * @throws Throwable
     */
    public static function formatItem(ZpJobsTable $table): JobsItem
    {
        $item = new JobsItem();
        $item->setId($table->id);
        $item->setTitle($table->title);
        $item->setDescription($table->description);
        $item->setAsk($table->ask);
        $item->setSalaryRange($table->salaryRange);
        $item->setCompanyId($table->companyId);
        $item->setTypeId($table->typeId ?: 0);
        $item->setLevelId($table->levelId ?: 0);
        $item->setIsCollected(false);
        return $item;
    }


    /**
     * 根据职位id获取职位列表
     * @param array $jobsIds
     * @return JobsItem[]
     * @throws Throwable
     */
    public static function getJobList(array $jobsIds): array
    {
        $jobsTables = new ZpJobsTable()->getCacheByIds($jobsIds, ZpJobsTable::ID);
        return self::getJobInfo(jobsTables: $jobsTables);
    }

    /**
     * 获取职位详情
     * @return JobsItem[]
     * @throws Throwable
     */
    public static function getJobInfo(array $jobsTables, int $userId = 0): array
    {


        $jobs = [];
        $companyIds = [];
        $typeIds = [];
        $levelIds = [];
        $jobIds = [];
        /**@var ZpJobsTable $table */
        foreach ($jobsTables as $table) {
            $id = $table->id;
            $jobs[$id] = JobsModel::formatItem($table);

            $companyIds[] = $table->companyId;
            if ($table->typeId) {
                $typeIds[] = $table->typeId;
            }
            if ($table->levelId) {
                $levelIds[] = $table->levelId;
            }
            if ($table->id) {
                $jobIds[] = $table->id;
            }
        }

        // 获取公司信息
        JobsModel::getCompanies($companyIds, $jobs);
        // 获取职位分类信息
        JobsModel::getTypes($typeIds, $jobs);
        // 获取职位级别信息
        JobsModel::getLevels($levelIds, $jobs);
        // 获取职位标签
        JobsModel::getTags($jobIds, $jobs);

        if ($userId) {
            // 获取是否收藏
            JobCollectModel::getByJobs($userId, $jobIds, $jobs);
        }

        return $jobs;
    }

}