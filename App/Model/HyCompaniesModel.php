<?php

namespace App\Model;

use Generate\Tables\Datas\HyCompaniesTable;
use Swlib\Exception\AppException;
use Swlib\Utils\Server;
use Throwable;

class HyCompaniesModel
{
    /**
     * @throws AppException
     */
    public static function updateContactPerson(int $id, string $contactPerson): void
    {
        Server::task([__CLASS__, 'taskUpdateContactPerson'], [
            'id' => $id,
            'contactPerson' => $contactPerson,
        ]);

    }

    /**
     * @throws Throwable
     */
    public function taskUpdateContactPerson(array $data): void
    {
        $id = $data['id'];
        $contactPerson = $data['contactPerson'];

        $find = new HyCompaniesTable()->where([
            HyCompaniesTable::ID => $id,
        ])->selectField(HyCompaniesTable::CONTACT_PERSON);

        if (empty($find)) {
            new HyCompaniesTable()->where([
                HyCompaniesTable::ID => $id,
            ])->update([
                HyCompaniesTable::CONTACT_PERSON => $contactPerson,
            ]);
        }


    }

}