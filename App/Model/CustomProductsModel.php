<?php

namespace App\Model;

use Generate\Tables\Datas\CustomProductsTable;
use Throwable;

class CustomProductsModel
{
    /**
     * @throws Throwable
     */
    public static function getCustomProductsLists(array $hyCompaniesIds): array
    {
        $all = new CustomProductsTable()->where([
            [CustomProductsTable::HY_COMPANIES_ID, 'in', $hyCompaniesIds]
        ])->selectAll();

        $nodes = [];
        foreach ($all as $value) {
            $hyCompaniesId = $value->hyCompaniesId;
            if (!isset($nodes[$hyCompaniesId])) {
                $nodes[$hyCompaniesId] = [];
            }
            $nodes[$hyCompaniesId][] = \Generate\Models\Datas\CustomProductsModel::formatItem($value);
        }

        return $nodes;
    }


    /**
     * @throws Throwable
     */
    public static function getListsByKeyword(string $keyword): array
    {
        return new CustomProductsTable()->where([
            [CustomProductsTable::PRODUCT_NAME, 'like', "%$keyword%"]
        ])->getArrayByField(CustomProductsTable::HY_COMPANIES_ID);
    }

}