<?php

namespace App\Model;

use Generate\Tables\Datas\BusinessTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\ShopOrderTable;
use Generate\Tables\Datas\ShopProductByCountTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Protobuf\Datas\Order\OrderProto;
use Swlib\Exception\AppException;
use Swlib\Table\Db;
use Protobuf\Order\OrderByBusinessItem;
use Protobuf\Order\OrderItem;
use Protobuf\Order\OrderRequest;
use Protobuf\Order\ShopOrderItem;
use Throwable;

class OrderModel
{

    // 订单状态常量
    public const StatusWait_pay = 1;        // 待支付
    public const StatusWait_delivery = 2;   // 待发货
    public const StatusWait_receive = 3;    // 待收货
    public const StatusFinish = 4;          // 已完成
    public const StatusRefund = 5;          // 申请退款
    public const StatusRefund_finish = 6;   // 退款完成
    public const StatusCancel = 7;          // 已取消
    public const StatusTimeout = 8;         // 超时取消
    public const StatusPartial_shipment = 9; // 部分发货
    public const StatusPartial_refund = 10;  // 部分退款

    /**
     * 生成订单号
     * @return string
     * @throws Throwable
     */
    public static function createOrderSn(): string
    {
        $todayCount = new OrderTable()->addWhere(OrderTable::TIME, strtotime(date('Y-m-d 00:00:00')), '>')->count();
        $todayCount++;
        return "" . mt_rand(10, 99) . date('Ymd') . str_pad($todayCount, 6, '0', STR_PAD_LEFT) . mt_rand(10, 99);
    }


    /**
     * @throws Throwable
     */
    public static function formatOrder(OrderTable $order, bool $isComment): OrderItem
    {
        $orderItem = new OrderItem();
        $orderItem->setId($order->id);
        $orderItem->setSn($order->sn);
        $orderItem->setOriginalPrice($order->originalPrice);
        $orderItem->setPrice($order->price);
        $orderItem->setUserId($order->userId);
        $orderItem->setIsComment($isComment);
        $orderItem->setTime(date('Y-m-d H:i:s', $order->time));
        $orderItem->setStatus($order->status);
        $orderItem->setRefundTime(date('Y-m-d H:i:s', $order->refundTime));
        $orderItem->setRefundHandleTime(date('Y-m-d H:i:s', $order->refundHandleTime));
        $orderItem->setCompleteTime(date('Y-m-d H:i:s', $order->completeTime));

        return $orderItem;
    }

    /**
     * @throws Throwable
     */
    public static function formatShopOrder(
        ShopOrderTable $order,
        string         $businessName,
        string         $productName,
        string         $skuName,
        string         $picture
    ): ShopOrderItem
    {
        $orderItem = new ShopOrderItem();
        $orderItem->setId($order->id);
        $orderItem->setSn($order->sn);
        $orderItem->setPrice($order->price);
        $orderItem->setUserId($order->userId);
        $orderItem->setBusinessId($order->businessId);
        $orderItem->setBusinessName($businessName);
        $orderItem->setProductId($order->productId);
        $orderItem->setProductName($productName);
        $orderItem->setSkuId($order->skuId);
        $orderItem->setSkuName($skuName);
        $orderItem->setTime(date('Y-m-d H:i:s', $order->time));
        $orderItem->setOriginalPrice($order->time);
        $orderItem->setPicture($picture);
        return $orderItem;
    }

    /**
     * 退款前的判断
     * @throws Throwable
     */
    public static function orderRefundCheck(string $sn, float $refund_amount): OrderTable
    {
        $orderTable = new OrderTable()->where([
            [OrderTable::SN, '=', $sn,]
        ])->selectOne();
        if (empty($orderTable)) {
            throw new AppException('订单不存在');
        }

        if ($refund_amount > $orderTable->price - $orderTable->refundAmount) {
            throw new AppException('退款金额不能大于订单金额');
        }

        return $orderTable;
    }

    /**
     * 订单退款
     * @throws Throwable
     */
    public static function orderRefund(string $sn, float $refund_amount, string $refund_no): bool
    {
        $orderTable = self::orderRefundCheck($sn, $refund_amount);

        $ret = new OrderTable()->where([
            [OrderTable::ID, '=', $orderTable->id]
        ])->update([
            OrderTable::REFUND_COMPLETE_TIME => time(),
            OrderTable::REFUND_AMOUNT => $orderTable->refundAmount + $refund_amount,
            OrderTable::REFUND_NO => $refund_no,
            OrderTable::REFUND_STATUS => 2,
        ]);

        return (bool)$ret;
    }

    /**
     * @throws Throwable
     */
    public static function getOrder(OrderProto $request): OrderTable
    {
        $sn = $request->getSn();
        $userId = $request->getUserId();

        $where = [
            [OrderTable::SN, '=', $sn],
            [OrderTable::USER_ID, '=', $userId],
        ];

        $find = new OrderTable()->where($where)->selectOne();
        if (empty($find)) {
            throw new AppException('订单不存在');
        }
        return $find;
    }


    /**
     * 按照订单分组返回订单列表
     * @param OrderTable[] $orderTables
     * @param ShopOrderTable[] $shopOrderTables
     * @param integer[] $isCommentOrderIds
     * @return OrderItem[]
     * @throws Throwable
     */
    public static function listsBySn(array $orderTables, array $shopOrderTables, array $isCommentOrderIds): array
    {

        $shopOrders = [];
        foreach ($shopOrderTables as $shopOrder) {
            $sn = $shopOrder->sn;
            if (!isset($shopOrders[$sn])) {
                $shopOrders[$sn] = [];
            }
            $message = static::formatShopOrder(
                $shopOrder,
                $shopOrder->getByField(BusinessTable::BUSINESS_NAME, ''),
                $shopOrder->getByField(ShopProductsTable::NAME, ''),
                $shopOrder->getByField(ShopProductsSkuTable::NAME, ''),
                $shopOrder->getByField(ShopProductsSkuTable::PICTURE, ''),
            );
            $shopOrders[$sn][] = $message;
        }

        $lists = [];
        foreach ($orderTables as $order) {
            $sn = $order->sn;

            $orderItem = static::formatOrder($order, array_key_exists($order->id, $isCommentOrderIds));
            $orderItem->setLists($shopOrders[$sn]);
            $lists[] = $orderItem;
        }

        return $lists;
    }


    /**
     * 按照商家分组返回订单列表
     * @param OrderTable[] $orderTables
     * @param ShopOrderTable[] $shopOrderTables
     * @param integer[] $isCommentOrderIds
     * @return OrderByBusinessItem[]
     * @throws Throwable
     */
    public static function listsByBusiness(array $orderTables, array $shopOrderTables, array $isCommentOrderIds): array
    {


        $orders = [];
        foreach ($orderTables as $order) {
            $id = $order->id;
            $orderItem = static::formatOrder($order, array_key_exists($order->id, $isCommentOrderIds));
            $orders[$id] = $orderItem;
        }


        $businessOrders = [];
        foreach ($shopOrderTables as $shopOrder) {
            $businessId = $shopOrder->businessId;
            if (!isset($businessOrders[$businessId])) {
                $businessOrders[$businessId] = new OrderByBusinessItem();
            }

            $businessName = $shopOrder->getByField(BusinessTable::BUSINESS_NAME, '');

            $shopOrderItemMessage = static::formatShopOrder(
                $shopOrder,
                $businessName,
                $shopOrder->getByField(ShopProductsTable::NAME),
                $shopOrder->getByField(ShopProductsSkuTable::NAME),
                $shopOrder->getByField(ShopProductsSkuTable::PICTURE),
            );
            $shopOrderItemMessage->setOrder($orders[$shopOrder->orderId]);


            $orderByBusinessItem = $businessOrders[$businessId];
            $orderByBusinessItem->setBusinessId($businessId);
            $orderByBusinessItem->setBusinessName($businessName);
            $orderByBusinessItem->setOrderTotalPrice($orderByBusinessItem->getOrderTotalPrice() + $shopOrder->price);
            $orderByBusinessItem->setLists([$shopOrderItemMessage, ...$orderByBusinessItem->getLists()]);
        }


        return $businessOrders;
    }


    /**
     * 记录到我的常买，购买统计
     * @throws Throwable
     */
    public function addByCount($data): bool
    {
        $sn = $data['sn'];
        Db::transaction(function () use ($sn) {
            foreach (new ShopOrderTable()->where([
                [ShopOrderTable::SN, '=', $sn],
            ])->selectAll() as $shopOrderTable) {
                $find = new ShopProductByCountTable()->where([
                    [ShopProductByCountTable::USER_ID, '=', $shopOrderTable->userId],
                    [ShopProductByCountTable::BUSINESS_ID, '=', $shopOrderTable->businessId],
                    [ShopProductByCountTable::PRODUCT_ID, '=', $shopOrderTable->productId],
                    [ShopProductByCountTable::SKU_ID, '=', $shopOrderTable->skuId],
                ])->lock()->selectOne();
                if (empty($find)) {
                    new ShopProductByCountTable()->insert([
                        ShopProductByCountTable::USER_ID => $shopOrderTable->userId,
                        ShopProductByCountTable::BUSINESS_ID => $shopOrderTable->businessId,
                        ShopProductByCountTable::PRODUCT_ID => $shopOrderTable->productId,
                        ShopProductByCountTable::SKU_ID => $shopOrderTable->skuId,
                        ShopProductByCountTable::COUNT => $shopOrderTable->num,
                        ShopProductByCountTable::PRICE => $shopOrderTable->price,
                    ]);
                } else {
                    new ShopProductByCountTable()->where([
                        [ShopProductByCountTable::ID, '=', $find->id],
                    ])->update([
                        ShopProductByCountTable::COUNT => $find->count + $shopOrderTable->num,
                        ShopProductByCountTable::PRICE => $find->price + $shopOrderTable->price,
                    ]);
                }
            }
        });

        return true;
    }

}