<?php

namespace App\Utils;

use ReflectionClass;
use Throwable;

/**
 * 路径工具类
 * 用于处理各种路径相关的功能
 */
class PathUtils
{
    /**
     * 从目标记录中获取页面路径
     * 
     * @param object $targetRecord 目标记录对象
     * @param string $type 记录类型
     * @return string 页面路径
     */
    public static function getPathFromRecord($targetRecord, string $type = ''): string
    {
        if (isset($targetRecord->path) && $targetRecord->path) {
            return $targetRecord->path;
        }
        
        // 根据不同类型构建不同的路径
        if (!empty($type)) {
            // 将下划线分隔的表名转换为驼峰式
            $typeParts = explode('_', $type);
            $typeForPath = '';
            foreach ($typeParts as $part) {
                $typeForPath .= ucfirst($part);
            }
            
            // 构建基本路径
            $basePath = '/' . strtolower($typeForPath);
            
            // 如果有ID，添加到路径中
            if (isset($targetRecord->id) && $targetRecord->id) {
                return $basePath . '/detail/' . $targetRecord->id;
            }
            
            return $basePath;
        }
        
        return '';
    }
    
    /**
     * 获取类型的格式化名称
     * 
     * @param string $type 类型名称（下划线分隔）
     * @return string 格式化后的类型名称（驼峰式）
     */
    public static function formatTypeName(string $type): string
    {
        $typeParts = explode('_', $type);
        $typeNameArr = [];
        foreach ($typeParts as $part) {
            $typeNameArr[] = ucfirst($part);
        }
        return implode('', $typeNameArr);
    }
    
    /**
     * 从表格类中获取记录信息
     * 
     * @param string $type 表格类型
     * @param int $itemId 记录ID
     * @return object|null 记录对象，失败返回null
     */
    public static function getRecordFromTable(string $type, int $itemId)
    {
        try {
            // 将下划线分隔的表名转换为驼峰式类名
            $className = '';
            $parts = explode('_', $type);
            foreach ($parts as $part) {
                $className .= ucfirst($part);
            }
            $className .= 'Table';

            // 构建完整的类名
            $fullClassName = "Generate\\Tables\\Datas\\$className";

            // 使用反射检查类是否存在
            if (class_exists($fullClassName)) {
                // 获取主键常量
                $reflectionClass = new ReflectionClass($fullClassName);
                $priKeyConstant = $reflectionClass->getConstants()['PRI_KEY'];

                // 实例化表格并查询数据
                $targetTable = new $fullClassName();
                return $targetTable->where([
                    $priKeyConstant => $itemId
                ])->selectOne();
            }
        } catch (Throwable) {
            // 异常处理
        }
        
        return null;
    }
} 