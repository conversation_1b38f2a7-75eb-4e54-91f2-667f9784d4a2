const typeElem = document.getElementById('data-input-hide-vr_tag.type');
const contentElem = document.getElementById('form-row-vr_tag.content');
const toSceneElem = document.getElementById('form-row-vr_tag.to_scene_id');
const urlElem = document.getElementById('form-row-vr_tag.url');


function hideOther() {
    contentElem.style.display = 'none'
    toSceneElem.style.display = 'none'
    urlElem.style.display = 'none'
}

function changeType(type) {
    hideOther()
    console.log(type)
    switch (type) {
        case '1':
            toSceneElem.style.display = 'flex';
            break;
        case '2':
            contentElem.style.display = 'flex';
            break;
        case '3':
            urlElem.style.display = 'flex';
            break;
        case '4':
            urlElem.style.display = 'flex';
            break;
    }
}
if(typeElem){
    hideOther()
    changeType(typeElem.value)
    setInterval(function () {
        changeType(typeElem.value)
    }, 300)

}
