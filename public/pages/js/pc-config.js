const ctxType = document.getElementById('ctx_type')
if (ctxType) {
    const ctxTypeInput = ctxType.querySelector('input.visually-hidden')
    if (ctxTypeInput) {
        let currTypeValue = "";
        setInterval(() => {
            console.log(ctxTypeInput.value)
            if (ctxTypeInput.value !== currTypeValue) {
                currTypeValue = ctxTypeInput.value;

                document.getElementById('ctx_url').closest('div.row').style.display = currTypeValue === 'iframe' ? 'flex' : 'none'
                document.getElementById('ctx_text').closest('div.row').style.display = currTypeValue === 'text' ? 'flex' : 'none'
                document.getElementById('ctx_img').style.display = currTypeValue === 'img' ? 'flex' : 'none'


                document.getElementById('ctx_url').disabled = currTypeValue !== 'iframe'
                document.getElementById('ctx_text').disabled = currTypeValue !== 'text'
                document.getElementById('ctx_img').querySelector('input.visually-hidden').disabled = currTypeValue !== 'img'

            }
        }, 500);
    }


}

