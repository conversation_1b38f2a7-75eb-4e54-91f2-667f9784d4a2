<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=0">
  <title>Document</title>
  <script src="./ezuikit.js"></script>
  <style>
    html,body {
      padding: 0;
      margin: 0;
      text-align: center;
      background: #FFFFFF;
      overflow: hidden;
    }
  </style>
</head>

<body>
<div id="playWind"></div>
<script>
  var domain = "https://open.ys7.com";
  var EZOPENDemo;
  window.EZOPENDemo = EZOPENDemo;
  var width = document.documentElement.clientWidth;
  var height = document.documentElement.clientWidth * 9 / 16;
  const ezopenInit = (accessToken) => {
    EZOPENDemo = new EZUIKit.EZUIKitPlayer({
      id: 'playWind',
      width: width,
      height: height,
      template: "cc3de2d3bfe54b2aa553b422b4f652ab",
      url: "ezopen://<EMAIL>/BB8294161/1.live",
      accessToken: accessToken
    });
  }
  fetch('/tool/ys/get-access-token').then(res => res.json()).then(data => {
    if(data.errno===0){
      ezopenInit(data.data.token);
    }
  })
</script>
</body>

</html>
