<?php


function delFiles($dir): void
{


    $entries = scandir($dir);
    foreach ($entries as $entry) {
        if ($entry != "." && $entry != "..") {
            $path = $dir . DIRECTORY_SEPARATOR . $entry;
            if (is_dir($path)) {
                delFiles($path);
            } else {
                if (str_ends_with($path, ".Identifier")) {
                    echo "Deleting $path\n";
                    unlink($path);
                }
            }
        }
    }
}

delFiles("/var/www/html");