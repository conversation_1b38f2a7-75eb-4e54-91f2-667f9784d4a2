# TypeScript 代码同步系统

一个简单高效的 TypeScript 代码同步解决方案，基于 Swoole 路由模式实现。

## 🚀 快速开始

### 1. 启动 Swoole 服务器

```bash
# 启动 Swoole 服务器 (开发环境)
php bin/start.php
```

### 2. 使用 Node.js 客户端同步代码

```bash
# 一次性同步 (默认配置)
node sync-client.js

# 指定服务器地址和输出目录
node sync-client.js http://localhost:9501 ./src/generated

# 监听模式 (自动同步)
node sync-client.js --watch
```

## 📁 目录结构

```
项目根目录/
├── App/Controller/Tool/Sync.php           # 同步控制器
├── sync-client.js                         # Node.js 同步客户端
├── runtime/codes/ts/                      # TypeScript 源代码目录
└── README-sync.md                         # 使用说明
```

## 🔧 功能特性

### 服务端特性
- ✅ **开发环境限制**: 仅在开发环境下可用，生产环境自动禁用
- ✅ **路由集成**: 基于 Swoole 主服务器路由，无需额外端口
- ✅ **自动发现**: 自动查找 TypeScript 源目录
- ✅ **安全过滤**: 只允许特定文件类型 (.ts, .js, .json, .md, .proto)
- ✅ **Web 界面**: 内置测试界面

### Node.js 客户端特性
- ✅ **批量同步**: 一次性同步所有文件
- ✅ **增量更新**: 保持文件修改时间
- ✅ **监听模式**: 自动检测变化并同步
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **进度显示**: 实时显示同步进度

## 📡 API 接口

所有接口都基于主服务器路由：

### GET /tool/sync/status
获取服务器状态信息

**响应示例:**
```json
{
  "success": true,
  "data": {
    "status": "running",
    "source_dir": "/path/to/runtime/codes/ts",
    "timestamp": 1703123456,
    "php_version": "8.2.0",
    "swoole_version": "5.0.0",
    "allowed_extensions": [".ts", ".js", ".json", ".md", ".proto"]
  }
}
```

### GET /tool/sync/files
获取所有文件列表

**响应示例:**
```json
{
  "success": true,
  "data": {
    "files": [
      "models/User.ts",
      "services/ApiService.ts",
      "types/index.ts"
    ]
  }
}
```

### GET /tool/sync/file?path={path}
获取指定文件内容

**响应示例:**
```json
{
  "success": true,
  "data": {
    "path": "models/User.ts",
    "content": "export interface User { ... }",
    "size": 1024,
    "modified": 1703123456
  }
}
```

### GET /tool/sync/run
同步所有文件

**响应示例:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "files": [
      {
        "path": "models/User.ts",
        "content": "export interface User { ... }",
        "size": 1024,
        "modified": 1703123456
      }
    ],
    "count": 1,
    "timestamp": 1703123456
  }
}
```

### GET /tool/sync/index
获取同步服务首页 (Web 界面)

## 🛠️ 配置选项

### Node.js 客户端配置

```javascript
const TypeScriptSyncClient = require('./sync-client');

// 基本配置
const client = new TypeScriptSyncClient(
    'http://localhost:9501',     // Swoole 服务器地址
    './src/generated'            // 输出目录
);

// 一次性同步
await client.syncAll();

// 监听模式 (每5秒检查一次)
await client.watch(5000);
```

## 🔍 使用场景

### 1. 开发环境代码同步
在开发过程中，自动同步最新的类型定义：

```bash
# 启动 Swoole 服务器
php bin/start.php

# 前端项目中同步代码
node sync-client.js http://localhost:9501 ./src/types
```

### 2. CI/CD 流水线集成
在构建流程中自动同步最新的类型定义：

```bash
# 在构建脚本中添加
node sync-client.js http://build-server:9501 ./src/generated
npm run build
```

### 3. 多项目共享类型
多个前端项目共享同一套类型定义：

```bash
# 项目A
node sync-client.js http://shared-server:9501 ./src/shared-types

# 项目B  
node sync-client.js http://shared-server:9501 ./types/shared
```

## 🐛 故障排除

### 常见问题

**1. 找不到源目录**
```bash
# 检查目录是否存在
ls -la runtime/codes/ts/

# 系统会自动查找以下目录：
# - runtime/codes/ts
# - runtime/Codes/ts
# - runtime/codes/typescript
# - runtime/typescript
```

**2. 权限问题**
```bash
# 确保目录有读取权限
chmod -R 755 runtime/codes/ts/

# 确保输出目录有写入权限
chmod -R 755 ./src/generated
```

**3. 生产环境无法访问**
```bash
# 检查环境配置
# 确保 ConfigEnum::APP_PROD 在开发环境下为 false
```

**4. 连接失败**
```bash
# 检查 Swoole 服务器是否启动
curl http://localhost:9501/tool/sync/status

# 检查防火墙设置
```

## 📝 开发说明

### 扩展文件类型支持
在 `App/Controller/Tool/Sync.php` 中修改 `$allowedExtensions` 数组：

```php
private static array $allowedExtensions = ['.ts', '.js', '.json', '.md', '.proto', '.vue'];
```

### 自定义源目录查找逻辑
修改 `getSourceDirectory()` 方法添加更多默认路径：

```php
$defaultPaths = [
    ROOT_DIR . 'runtime/codes/ts',
    ROOT_DIR . 'src/generated',
    ROOT_DIR . 'custom/path'
];
```

### 添加认证支持
可以在控制器中添加简单的认证：

```php
private function checkAuth(): bool {
    $token = $this->request->header['authorization'] ?? '';
    return $token === 'Bearer your-secret-token';
}
```

## 🆚 优势对比

| 特性 | 路由模式 | 独立服务器模式 |
|------|----------|----------------|
| **启动方式** | 自动集成 | 需要单独启动 |
| **端口管理** | 使用主端口 | 需要额外端口 |
| **性能** | 高 (共享资源) | 中等 (独立进程) |
| **内存使用** | 共享主服务器 | 独立进程 |
| **生产环境** | 自动禁用 | 需手动管理 |
| **维护成本** | 低 | 中等 |

## 📄 许可证

MIT License - 可自由使用和修改。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！ 