#!/usr/bin/env node
/**
 * TypeScript 批量同步脚本
 * 
 * 使用方法：
 * node sync-batch.js
 * 
 * 功能：
 * - 同时启动多个同步任务
 * - 每个任务都运行在监听模式下
 * - 支持自定义配置
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class BatchSyncClient {
    constructor() {
        this.processes = [];
        this.isShuttingDown = false;
        
        // 默认同步配置
        this.syncConfigs = [
            {
                name: 'APIs同步',
                serverUrl: 'http://localhost:9501',
                outputDir: './network/apis/',
                sourceDir: 'apis',
                flatten: false,
                color: '\x1b[36m' // 青色
            },
            {
                name: 'Proto同步',
                serverUrl: 'http://localhost:9501',
                outputDir: './proto/protos/',
                sourceDir: 'proto',
                flatten: true,
                color: '\x1b[33m' // 黄色
            }
        ];
        
        this.setupSignalHandlers();
    }

    /**
     * 启动所有同步任务
     */
    async startAll() {
        console.log('\x1b[32m🚀 启动批量TypeScript代码同步...\x1b[0m');
        console.log('\x1b[90m─'.repeat(60) + '\x1b[0m');
        
        // 检查sync-client.js是否存在
        const clientPath = path.join(__dirname, 'sync-client.js');
        if (!fs.existsSync(clientPath)) {
            console.error('\x1b[31m❌ 找不到 sync-client.js 文件\x1b[0m');
            process.exit(1);
        }
        
        // 启动每个同步任务
        for (const config of this.syncConfigs) {
            await this.startSyncProcess(config);
            // 稍微延迟，避免同时启动造成混乱
            await this.sleep(1000);
        }
        
        console.log('\x1b[90m─'.repeat(60) + '\x1b[0m');
        console.log('\x1b[32m✅ 所有同步任务已启动\x1b[0m');
        console.log('\x1b[36m👀 监听模式已激活，按 Ctrl+C 停止所有任务\x1b[0m');
        console.log('\x1b[90m─'.repeat(60) + '\x1b[0m');
        
        // 保持进程运行
        await this.keepAlive();
    }

    /**
     * 启动单个同步进程
     */
    async startSyncProcess(config) {
        const args = [
            'sync-client.js',
            config.serverUrl,
            config.outputDir,
            `--source-dir=${config.sourceDir}`,
            '--watch'
        ];
        
        if (config.flatten) {
            args.push('--flatten');
        }
        
        console.log(`${config.color}🔄 启动 ${config.name}...\x1b[0m`);
        console.log(`${config.color}   命令: node ${args.join(' ')}\x1b[0m`);
        
        const child = spawn('node', args, {
            stdio: 'pipe',
            cwd: __dirname
        });
        
        // 设置进程标识
        child.configName = config.name;
        child.configColor = config.color;
        
        // 处理输出
        child.stdout.on('data', (data) => {
            const lines = data.toString().split('\n').filter(line => line.trim());
            lines.forEach(line => {
                console.log(`${config.color}[${config.name}]\x1b[0m ${line}`);
            });
        });
        
        child.stderr.on('data', (data) => {
            const lines = data.toString().split('\n').filter(line => line.trim());
            lines.forEach(line => {
                console.error(`${config.color}[${config.name}]\x1b[31m ERROR:\x1b[0m ${line}`);
            });
        });
        
        child.on('close', (code) => {
            if (!this.isShuttingDown) {
                console.log(`${config.color}[${config.name}]\x1b[31m 进程退出，代码: ${code}\x1b[0m`);
                // 如果不是正常关闭，尝试重启
                if (code !== 0) {
                    console.log(`${config.color}[${config.name}]\x1b[33m 5秒后重启...\x1b[0m`);
                    setTimeout(() => {
                        if (!this.isShuttingDown) {
                            this.startSyncProcess(config);
                        }
                    }, 5000);
                }
            }
        });
        
        child.on('error', (error) => {
            console.error(`${config.color}[${config.name}]\x1b[31m 启动失败: ${error.message}\x1b[0m`);
        });
        
        this.processes.push(child);
    }

    /**
     * 设置信号处理器
     */
    setupSignalHandlers() {
        const shutdown = (signal) => {
            if (this.isShuttingDown) return;
            
            console.log(`\n\x1b[33m📡 收到 ${signal} 信号，正在关闭所有同步任务...\x1b[0m`);
            this.isShuttingDown = true;
            
            this.processes.forEach((child, index) => {
                if (child && !child.killed) {
                    console.log(`\x1b[90m   关闭 ${child.configName}...\x1b[0m`);
                    child.kill('SIGTERM');
                }
            });
            
            // 强制退出
            setTimeout(() => {
                console.log('\x1b[31m⚠️  强制退出\x1b[0m');
                process.exit(0);
            }, 5000);
            
            // 等待所有进程关闭
            Promise.all(
                this.processes.map(child => new Promise(resolve => {
                    if (child.killed || child.exitCode !== null) {
                        resolve();
                    } else {
                        child.on('close', resolve);
                    }
                }))
            ).then(() => {
                console.log('\x1b[32m✅ 所有同步任务已停止\x1b[0m');
                process.exit(0);
            });
        };
        
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        
        // Windows支持
        if (process.platform === 'win32') {
            require('readline').createInterface({
                input: process.stdin,
                output: process.stdout
            }).on('SIGINT', () => shutdown('SIGINT'));
        }
    }

    /**
     * 保持进程运行
     */
    async keepAlive() {
        return new Promise(resolve => {
            // 这个Promise永远不会resolve，除非进程被终止
        });
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 显示状态信息
     */
    showStatus() {
        console.log('\n\x1b[36m📊 同步任务状态:\x1b[0m');
        this.processes.forEach((child, index) => {
            const status = child.killed ? '❌ 已停止' : '✅ 运行中';
            console.log(`   ${child.configColor}${child.configName}: ${status}\x1b[0m`);
        });
        console.log('');
    }
}

// 自定义配置支持
function loadCustomConfig() {
    const configPath = path.join(__dirname, 'sync-config.json');
    if (fs.existsSync(configPath)) {
        try {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            console.log('\x1b[36m📝 加载自定义配置文件\x1b[0m');
            return config;
        } catch (error) {
            console.warn('\x1b[33m⚠️  配置文件格式错误，使用默认配置\x1b[0m');
        }
    }
    return null;
}

// 创建示例配置文件
function createExampleConfig() {
    const configPath = path.join(__dirname, 'sync-config.example.json');
    const exampleConfig = {
        syncConfigs: [
            {
                name: 'APIs同步',
                serverUrl: 'http://localhost:9501',
                outputDir: './network/apis/',
                sourceDir: 'apis',
                flatten: false,
                color: '\x1b[36m'
            },
            {
                name: 'Proto同步',
                serverUrl: 'http://localhost:9501',
                outputDir: './proto/protos/',
                sourceDir: 'proto',
                flatten: true,
                color: '\x1b[33m'
            }
        ]
    };
    
    if (!fs.existsSync(configPath)) {
        fs.writeFileSync(configPath, JSON.stringify(exampleConfig, null, 2));
        console.log(`\x1b[36m📝 已创建示例配置文件: ${configPath}\x1b[0m`);
    }
}

// 主程序
if (require.main === module) {
    // 创建示例配置文件
    createExampleConfig();
    
    // 加载配置
    const customConfig = loadCustomConfig();
    
    const batchClient = new BatchSyncClient();
    
    // 如果有自定义配置，使用自定义配置
    if (customConfig && customConfig.syncConfigs) {
        batchClient.syncConfigs = customConfig.syncConfigs;
    }
    
    // 显示帮助信息
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
        console.log(`
\x1b[32mTypeScript 批量同步脚本\x1b[0m

\x1b[36m使用方法:\x1b[0m
  node sync-batch.js                启动所有同步任务
  node sync-batch.js --help         显示帮助信息

\x1b[36m配置文件:\x1b[0m
  sync-config.json                  自定义配置文件
  sync-config.example.json          示例配置文件

\x1b[36m当前配置:\x1b[0m`);
        
        batchClient.syncConfigs.forEach((config, index) => {
            console.log(`  ${index + 1}. ${config.name}`);
            console.log(`     服务器: ${config.serverUrl}`);
            console.log(`     输出目录: ${config.outputDir}`);
            console.log(`     源目录: ${config.sourceDir}`);
            console.log(`     扁平化: ${config.flatten ? '是' : '否'}`);
        });
        
        process.exit(0);
    }
    
    // 启动批量同步
    batchClient.startAll().catch(console.error);
}

module.exports = BatchSyncClient; 