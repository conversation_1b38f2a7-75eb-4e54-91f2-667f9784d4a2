{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "e138f0d8140aa4d7043e07f44689d1c4", "packages": [{"name": "adbario/php-dot-notation", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "shasum": ""}, "require": {"ext-json": "*", "php": "^5.5 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.7|^6.6|^7.5|^8.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "support": {"issues": "https://github.com/adbario/php-dot-notation/issues", "source": "https://github.com/adbario/php-dot-notation/tree/2.5.0"}, "time": "2022-10-14T20:31:46+00:00"}, {"name": "alibabacloud/credentials", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/aliyun/credentials-php.git", "reference": "410338b1831f7547a40071af3a9adea27b2fe6e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/credentials-php/zipball/410338b1831f7547a40071af3a9adea27b2fe6e5", "reference": "410338b1831f7547a40071af3a9adea27b2fe6e5", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.6"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^5.7|^6.6|^9.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Credentials\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Credentials for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "credentials", "library", "sdk", "tool"], "support": {"issues": "https://github.com/aliyun/credentials-php/issues", "source": "https://github.com/aliyun/credentials-php"}, "time": "2025-03-04T07:38:54+00:00"}, {"name": "alibabacloud/darabonba", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/darabonba.git", "reference": "36f0b4191e73f8069527af7af1436bab29188ebc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/darabonba/zipball/36f0b4191e73f8069527af7af1436bab29188ebc", "reference": "36f0b4191e73f8069527af7af1436bab29188ebc", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.4", "alibabacloud/tea": "^3.2", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "monolog/monolog": "^1.0|^2.1", "php": ">=5.5", "psr/http-message": "^0.11.0|^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3|^9.3", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Dara\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Darabonba for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "support": {"issues": "https://github.com/aliyun/tea-php/issues", "source": "https://github.com/aliyun/tea-php"}, "time": "2025-01-15T06:04:25+00:00"}, {"name": "alibabacloud/darabonba-openapi", "version": "0.2.13", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/darabonba-openapi.git", "reference": "0213396384e2c064eefd614f3dd53636a63f987f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/darabonba-openapi/zipball/0213396384e2c064eefd614f3dd53636a63f987f", "reference": "0213396384e2c064eefd614f3dd53636a63f987f", "shasum": ""}, "require": {"alibabacloud/credentials": "^1.1", "alibabacloud/gateway-spi": "^1", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.21", "alibabacloud/tea-xml": "^0.2", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi Client", "support": {"issues": "https://github.com/alibabacloud-sdk-php/darabonba-openapi/issues", "source": "https://github.com/alibabacloud-sdk-php/darabonba-openapi/tree/0.2.13"}, "time": "2024-07-15T13:11:36+00:00"}, {"name": "alibabacloud/dyplsapi-20170525", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/dyplsapi-20170525.git", "reference": "2ea6ba6aec692007c3d133001621be0796e84273"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/dyplsapi-20170525/zipball/2ea6ba6aec692007c3d133001621be0796e84273", "reference": "2ea6ba6aec692007c3d133001621be0796e84273", "shasum": ""}, "require": {"alibabacloud/darabonba": "^1.0.0", "alibabacloud/openapi-core": "^1.0.0", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Dyplsapi\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Dyplsapi (20170525) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/dyplsapi-20170525/tree/2.0.1"}, "time": "2025-02-14T05:57:22+00:00"}, {"name": "alibabacloud/dysmsapi-20170525", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/Dysmsapi-20170525.git", "reference": "5a301a076b29449a488607cbfbb7f12b8cc50c1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/Dysmsapi-20170525/zipball/5a301a076b29449a488607cbfbb7f12b8cc50c1f", "reference": "5a301a076b29449a488607cbfbb7f12b8cc50c1f", "shasum": ""}, "require": {"alibabacloud/darabonba-openapi": "^0.2.13", "alibabacloud/endpoint-util": "^0.1.0", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.21", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Dysmsapi\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Dysmsapi (20170525) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/Dysmsapi-20170525/tree/3.1.1"}, "time": "2025-01-03T17:14:56+00:00"}, {"name": "alibabacloud/dyvmsapi-20170525", "version": "3.2.2", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/Dyvmsapi-20170525.git", "reference": "20c50b61eb383d1aebbdcfe91fa3808fc81fad3a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/Dyvmsapi-20170525/zipball/20c50b61eb383d1aebbdcfe91fa3808fc81fad3a", "reference": "20c50b61eb383d1aebbdcfe91fa3808fc81fad3a", "shasum": ""}, "require": {"alibabacloud/darabonba-openapi": "^0.2.13", "alibabacloud/endpoint-util": "^0.1.0", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.21", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Dyvmsapi\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Dyvmsapi (20170525) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/Dyvmsapi-20170525/tree/3.2.2"}, "time": "2024-10-15T09:56:52+00:00"}, {"name": "alibabacloud/endpoint-util", "version": "0.1.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/endpoint-util.git", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/endpoint-util/zipball/f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "shasum": ""}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Endpoint\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Endpoint Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/endpoint-util/tree/0.1.1"}, "time": "2020-06-04T10:57:15+00:00"}, {"name": "alibabacloud/gateway-spi", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/alibabacloud-gateway-spi.git", "reference": "7440f77750c329d8ab252db1d1d967314ccd1fcb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/alibabacloud-gateway-spi/zipball/7440f77750c329d8ab252db1d1d967314ccd1fcb", "reference": "7440f77750c329d8ab252db1d1d967314ccd1fcb", "shasum": ""}, "require": {"alibabacloud/credentials": "^1.1", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\GatewaySpi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Gateway SPI Client", "support": {"source": "https://github.com/alibabacloud-sdk-php/alibabacloud-gateway-spi/tree/1.0.0"}, "time": "2022-07-14T05:31:35+00:00"}, {"name": "alibabacloud/openapi-core", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/openapi-core.git", "reference": "89055d4f3824f9c44d8821f41f788ea68f6ce9b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/openapi-core/zipball/89055d4f3824f9c44d8821f41f788ea68f6ce9b6", "reference": "89055d4f3824f9c44d8821f41f788ea68f6ce9b6", "shasum": ""}, "require": {"alibabacloud/credentials": "^1.2", "alibabacloud/darabonba": "^1", "alibabacloud/gateway-spi": "^1", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3|^9.3", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi Client Core", "support": {"issues": "https://github.com/alibabacloud-sdk-php/openapi-core/issues", "source": "https://github.com/alibabacloud-sdk-php/openapi-core/tree/v1.0.1"}, "time": "2025-03-04T05:59:56+00:00"}, {"name": "alibabacloud/openapi-util", "version": "0.2.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/openapi-util.git", "reference": "f31f7bcd835e08ca24b6b8ba33637eb4eceb093a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/openapi-util/zipball/f31f7bcd835e08ca24b6b8ba33637eb4eceb093a", "reference": "f31f7bcd835e08ca24b6b8ba33637eb4eceb093a", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-utils": "^0.2", "lizhichao/one-sm": "^1.5", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\OpenApiUtil\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi <PERSON>", "support": {"issues": "https://github.com/alibabacloud-sdk-php/openapi-util/issues", "source": "https://github.com/alibabacloud-sdk-php/openapi-util/tree/0.2.1"}, "time": "2023-01-10T09:10:10+00:00"}, {"name": "alibabacloud/tea", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/aliyun/tea-php.git", "reference": "1619cb96c158384f72b873e1f85de8b299c9c367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/tea-php/zipball/1619cb96c158384f72b873e1f85de8b299c9c367", "reference": "1619cb96c158384f72b873e1f85de8b299c9c367", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.4", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "support": {"issues": "https://github.com/aliyun/tea-php/issues", "source": "https://github.com/aliyun/tea-php"}, "time": "2023-05-16T06:43:41+00:00"}, {"name": "alibabacloud/tea-utils", "version": "0.2.21", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-utils.git", "reference": "5039e45714c6456186d267f5d81a4b260a652495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-utils/zipball/5039e45714c6456186d267f5d81a4b260a652495", "reference": "5039e45714c6456186d267f5d81a4b260a652495", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\Utils\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea Utils for PHP", "support": {"issues": "https://github.com/aliyun/tea-util/issues", "source": "https://github.com/aliyun/tea-util"}, "time": "2024-07-05T06:05:54+00:00"}, {"name": "alibabacloud/tea-xml", "version": "0.2.4", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-xml.git", "reference": "3e0c000bf536224eebbac913c371bef174c0a16a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-xml/zipball/3e0c000bf536224eebbac913c371bef174c0a16a", "reference": "3e0c000bf536224eebbac913c371bef174c0a16a", "shasum": ""}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/var-dumper": "*"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\XML\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea XML Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/tea-xml/tree/0.2.4"}, "time": "2022-08-02T04:12:58+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-24T11:22:20+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-10-17T10:06:22+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2024-07-18T11:15:46+00:00"}, {"name": "hyperf/context", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/context.git", "reference": "ac666862d644db7d813342c880826a1fda599bdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/context/zipball/ac666862d644db7d813342c880826a1fda599bdf", "reference": "ac666862d644db7d813342c880826a1fda599bdf", "shasum": ""}, "require": {"hyperf/engine": "^2.0", "php": ">=8.1"}, "suggest": {"swow/psr7-plus": "Required to use RequestContext and ResponseContext"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Context\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A coroutine/application context library.", "homepage": "https://hyperf.io", "keywords": ["Context", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/contract", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/contract.git", "reference": "6ef2c7f98917c52ccda3a37ae65b190848dde6c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/contract/zipball/6ef2c7f98917c52ccda3a37ae65b190848dde6c4", "reference": "6ef2c7f98917c52ccda3a37ae65b190848dde6c4", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Contract\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "The contracts of Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/engine", "version": "v2.13.1", "source": {"type": "git", "url": "https://github.com/hyperf/engine.git", "reference": "3002d34cfb6278c3a25f9d94c41c43bed03a88a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/engine/zipball/3002d34cfb6278c3a25f9d94c41c43bed03a88a6", "reference": "3002d34cfb6278c3a25f9d94c41c43bed03a88a6", "shasum": ""}, "require": {"hyperf/engine-contract": "~1.12.0", "php": ">=8.0"}, "conflict": {"ext-swoole": "<5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/guzzle": "^3.0", "hyperf/http-message": "^3.0", "mockery/mockery": "^1.5", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.4", "swoole/ide-helper": "5.*"}, "suggest": {"ext-sockets": "*", "ext-swoole": ">=5.0", "hyperf/http-message": "Required to use ResponseEmitter.", "psr/http-message": "Required to use WebSocket Frame."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Engine\\ConfigProvider"}, "branch-alias": {"dev-master": "2.13-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Engine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Coroutine engine provided by swoole.", "keywords": ["engine", "hyperf", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/engine/issues", "source": "https://github.com/hyperf/engine/tree/v2.13.1"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2025-02-08T03:29:51+00:00"}, {"name": "hyperf/engine-contract", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/hyperf/engine-contract.git", "reference": "08539eac8047e525384dd49f3da87f175d7e3c90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/engine-contract/zipball/08539eac8047e525384dd49f3da87f175d7e3c90", "reference": "08539eac8047e525384dd49f3da87f175d7e3c90", "shasum": ""}, "require": {"php": ">=8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": ">=7.0", "psr/http-message": "^1.0", "swoole/ide-helper": "^4.5"}, "suggest": {"psr/http-message": "Required to use WebSocket Frame."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Hyperf\\Engine\\Contract\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Contract for Coroutine Engine", "keywords": ["contract", "coroutine", "engine", "hyperf", "php"], "support": {"issues": "https://github.com/hyperf/engine-contract/issues", "source": "https://github.com/hyperf/engine-contract/tree/v1.12.0"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2025-02-08T02:36:22+00:00"}, {"name": "hyperf/pimple", "version": "v2.2.2", "source": {"type": "git", "url": "https://github.com/hyperf-cloud/pimple-integration.git", "reference": "7bd07745c256b83679471c06ec2a11e901d37277"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf-cloud/pimple-integration/zipball/7bd07745c256b83679471c06ec2a11e901d37277", "reference": "7bd07745c256b83679471c06ec2a11e901d37277", "shasum": ""}, "require": {"hyperf/context": "^3.0", "hyperf/contract": "^3.0", "php": ">=8.0", "pimple/pimple": "^3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/support": "^3.0", "mockery/mockery": "^1.3", "phpstan/phpstan": "^1.0", "phpunit/phpunit": ">=7.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Pimple\\ConfigProvider"}, "branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Hyperf\\Pimple\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "<PERSON><PERSON> Container", "keywords": ["container", "hyperf", "php", "psr11"], "support": {"issues": "https://github.com/hyperf-cloud/pimple-integration/issues", "source": "https://github.com/hyperf-cloud/pimple-integration/tree/v2.2.2"}, "time": "2023-06-10T04:41:29+00:00"}, {"name": "league/csv", "version": "9.22.0", "source": {"type": "git", "url": "https://github.com/thephpleague/csv.git", "reference": "afc109aa11f3086b8be8dfffa04ac31480b36b76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/afc109aa11f3086b8be8dfffa04ac31480b36b76", "reference": "afc109aa11f3086b8be8dfffa04ac31480b36b76", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1.2"}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.69.0", "phpbench/phpbench": "^1.4.0", "phpstan/phpstan": "^1.12.18", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.2", "phpstan/phpstan-strict-rules": "^1.6.2", "phpunit/phpunit": "^10.5.16 || ^11.5.7", "symfony/var-dumper": "^6.4.8 || ^7.2.3"}, "suggest": {"ext-dom": "Required to use the XMLConverter and the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-mbstring": "Needed to ease transcoding CSV using mb stream filters", "ext-mysqli": "Requiered to use the package with the MySQLi extension", "ext-pdo": "Required to use the package with the PDO extension", "ext-pgsql": "Requiered to use the package with the PgSQL extension", "ext-sqlite3": "Required to use the package with the SQLite3 extension"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "CSV data manipulation made easy in PHP", "homepage": "https://csv.thephpleague.com", "keywords": ["convert", "csv", "export", "filter", "import", "read", "transform", "write"], "support": {"docs": "https://csv.thephpleague.com", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2025-02-28T10:00:39+00:00"}, {"name": "lizhichao/one-sm", "version": "1.10", "source": {"type": "git", "url": "https://github.com/lizhichao/sm.git", "reference": "687a012a44a5bfd4d9143a0234e1060543be455a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lizhichao/sm/zipball/687a012a44a5bfd4d9143a0234e1060543be455a", "reference": "687a012a44a5bfd4d9143a0234e1060543be455a", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "autoload": {"psr-4": {"OneSm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "国密sm3", "keywords": ["php", "sm3"], "support": {"issues": "https://github.com/lizhichao/sm/issues", "source": "https://github.com/lizhichao/sm/tree/1.10"}, "funding": [{"url": "https://www.vicsdf.com/img/w.jpg", "type": "custom"}, {"url": "https://www.vicsdf.com/img/z.jpg", "type": "custom"}], "time": "2021-05-26T06:19:22+00:00"}, {"name": "monolog/monolog", "version": "2.10.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/5cf826f2991858b54d5c3809bee745560a1042a7", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.38 || ^9.6.19", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.10.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2024-11-12T12:43:37+00:00"}, {"name": "overtrue/pinyin", "version": "5.3.3", "source": {"type": "git", "url": "https://github.com/overtrue/pinyin.git", "reference": "bff15b27cf3e1cc416464b678576f4da9899692e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/pinyin/zipball/bff15b27cf3e1cc416464b678576f4da9899692e", "reference": "bff15b27cf3e1cc416464b678576f4da9899692e", "shasum": ""}, "require": {"php": ">=8.0.2"}, "require-dev": {"brainmaestro/composer-git-hooks": "^3.0", "friendsofphp/php-cs-fixer": "^3.2", "laravel/pint": "^1.10", "nunomaduro/termwind": "^1.0|^2.0", "phpunit/phpunit": "^10.0|^11.2"}, "bin": ["bin/pinyin"], "type": "library", "extra": {"hooks": {"pre-push": ["composer pint", "composer test"], "pre-commit": ["composer pint", "composer test"]}}, "autoload": {"psr-4": {"Overtrue\\Pinyin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>", "homepage": "http://github.com/overtrue"}], "description": "Chinese to pinyin translator.", "homepage": "https://github.com/overtrue/pinyin", "keywords": ["Chinese", "<PERSON><PERSON><PERSON>", "cn2pinyin"], "support": {"issues": "https://github.com/overtrue/pinyin/issues", "source": "https://github.com/overtrue/pinyin/tree/5.3.3"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "time": "2024-08-01T08:19:06+00:00"}, {"name": "pimple/pimple", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "a94b3a4db7fb774b3d78dad2315ddc07629e1bed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/a94b3a4db7fb774b3d78dad2315ddc07629e1bed", "reference": "a94b3a4db7fb774b3d78dad2315ddc07629e1bed", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1 || ^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.4@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev"}}, "autoload": {"psr-0": {"Pimple": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container", "homepage": "https://pimple.symfony.com", "keywords": ["container", "dependency injection"], "support": {"source": "https://github.com/silexphp/Pimple/tree/v3.5.0"}, "time": "2021-10-28T11:13:42+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "twig/twig", "version": "v3.20.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "3468920399451a384bef53cf7996965f7cd40183"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/3468920399451a384bef53cf7996965f7cd40183", "reference": "3468920399451a384bef53cf7996965f7cd40183", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.20.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-02-13T08:34:43+00:00"}, {"name": "yansongda/artful", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/yansongda/artful.git", "reference": "bad726c6287aca219750823bff46f70287ec3995"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yansongda/artful/zipball/bad726c6287aca219750823bff46f70287ec3995", "reference": "bad726c6287aca219750823bff46f70287ec3995", "shasum": ""}, "require": {"guzzlehttp/psr7": "^2.6", "php": ">=8.0", "psr/container": "^1.1 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-message": "^1.1 || ^2.0", "psr/log": "^1.1 || ^2.0 || ^3.0", "yansongda/supports": "~4.0.10"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.44", "guzzlehttp/guzzle": "^7.0", "hyperf/pimple": "^2.2", "mockery/mockery": "^1.4", "monolog/monolog": "^2.2", "phpstan/phpstan": "^1.0.0 || ^2.0.0", "phpunit/phpunit": "^9.0", "symfony/event-dispatcher": "^5.2.0", "symfony/http-foundation": "^5.2.0", "symfony/psr-http-message-bridge": "^2.1", "symfony/var-dumper": "^5.1"}, "suggest": {"hyperf/pimple": "其它/无框架下使用 SDK，请安装，任选其一", "illuminate/container": "其它/无框架下使用 SDK，请安装，任选其一"}, "type": "library", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Yansongda\\Artful\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Artful 是一个简单易用的 API 请求框架 PHP Api RequesT Framwork U Like。", "keywords": ["api", "artful", "framework", "request"], "support": {"homepage": "https://artful.yansongda.cn", "issues": "https://github.com/yansongda/artful/issues", "source": "https://github.com/yansongda/artful"}, "time": "2025-01-09T12:39:39+00:00"}, {"name": "yansongda/pay", "version": "v3.7.13", "source": {"type": "git", "url": "https://github.com/yansongda/pay.git", "reference": "6e1678f44acb91ba78900eb74e307de415a54e9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yansongda/pay/zipball/6e1678f44acb91ba78900eb74e307de415a54e9d", "reference": "6e1678f44acb91ba78900eb74e307de415a54e9d", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-json": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-simplexml": "*", "php": ">=8.0", "yansongda/artful": "~1.1.1", "yansongda/supports": "~4.0.10"}, "conflict": {"hyperf/framework": "<3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.44", "guzzlehttp/guzzle": "^7.0", "hyperf/pimple": "^2.2", "jetbrains/phpstorm-attributes": "^1.1", "mockery/mockery": "^1.4", "monolog/monolog": "^2.2", "phpstan/phpstan": "^1.0.0 || ^2.0.0", "phpunit/phpunit": "^9.0", "symfony/event-dispatcher": "^5.2.0", "symfony/http-foundation": "^5.2.0", "symfony/psr-http-message-bridge": "^2.1", "symfony/var-dumper": "^5.1"}, "type": "library", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Yansongda\\Pay\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "可能是我用过的最优雅的 Alipay 和 WeChat 的支付 SDK 扩展包了", "keywords": ["alipay", "pay", "wechat"], "support": {"homepage": "https://pay.yansongda.cn", "issues": "https://github.com/yansongda/pay/issues", "source": "https://github.com/yansongda/pay"}, "time": "2025-02-11T04:04:44+00:00"}, {"name": "yansongda/supports", "version": "v4.0.12", "source": {"type": "git", "url": "https://github.com/yansongda/supports.git", "reference": "308de376d20cb1cd4f959644793e0582ccd1ef6d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yansongda/supports/zipball/308de376d20cb1cd4f959644793e0582ccd1ef6d", "reference": "308de376d20cb1cd4f959644793e0582ccd1ef6d", "shasum": ""}, "require": {"ext-ctype": "*", "ext-mbstring": "*", "php": ">=8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.4", "phpstan/phpstan": "^1.1.0", "phpunit/phpunit": "^9.0"}, "suggest": {"monolog/monolog": "Use logger", "symfony/console": "Use stdout logger"}, "type": "library", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Yansongda\\Supports\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "common components", "keywords": ["array", "collection", "config", "support"], "support": {"issues": "https://github.com/yansongda/supports/issues", "source": "https://github.com/yansongda/supports"}, "time": "2025-01-08T08:55:20+00:00"}], "packages-dev": [{"name": "google/protobuf", "version": "v4.30.0", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "e1d66682f6836aa87820400f0aa07d9eb566feb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/e1d66682f6836aa87820400f0aa07d9eb566feb6", "reference": "e1d66682f6836aa87820400f0aa07d9eb566feb6", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": ">=5.0.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "support": {"source": "https://github.com/protocolbuffers/protobuf-php/tree/v4.30.0"}, "time": "2025-03-04T22:54:49+00:00"}, {"name": "swoole/ide-helper", "version": "6.0.0-rc1", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "8b35b19bc0c3783aadde07f901f61832d03a75cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swoole/ide-helper/zipball/8b35b19bc0c3783aadde07f901f61832d03a75cd", "reference": "8b35b19bc0c3783aadde07f901f61832d03a75cd", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "support": {"issues": "https://github.com/swoole/ide-helper/issues", "source": "https://github.com/swoole/ide-helper/tree/6.0.0-rc1"}, "time": "2024-12-02T06:59:00+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"swoole/ide-helper": 5}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=8.4", "ext-bcmath": "*", "ext-curl": "*", "ext-openssl": "*", "ext-redis": "*", "ext-pdo": "*", "ext-mysqli": "*", "ext-mbstring": "*", "ext-zlib": "*", "ext-xlswriter": "*", "ext-dom": "*", "ext-libxml": "*", "ext-posix": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}