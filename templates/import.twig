<div class="import-container">
    <form id="importForm" enctype="multipart/form-data" style="display: inline-block;">
        <input type="file"
               id="fileInput"
               name="file"
               accept=".xlsx,.xls,.csv"
               style="display: none;"
               {% for attrKey,attrValue in action.attributes %}data-{{ attrKey }}="{{ attrValue }}"{% endfor %}
        >
        <button type="button"
                id="importBtn"
                class="btn btn-primary btn-sm rounded-0 {% for class in action.classes %}{{ class }}{% endfor %}"
        >
            {% if action.icon %}
                <i class="{{ action.icon }}"></i>
            {% endif %}
            {{ action.label }}
        </button>
    </form>
</div>

<!-- Bootstrap 进度模态框 -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="progressModalLabel">
                    <i class="fas fa-upload me-2"></i>
                    <span id="modalTitle">文件导入进度</span>
                </h5>
            </div>
            <div class="modal-body">
                <!-- 上传进度区域 -->
                <div id="uploadSection">
                    <div class="text-center mb-3">
                        <i class="fas fa-cloud-upload-alt text-info" style="font-size: 2rem;"></i>
                        <h6 class="mt-2 text-secondary">正在上传文件...</h6>
                    </div>
                    <div class="progress mb-3" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-info"
                             role="progressbar"
                             style="width: 0%"
                             id="uploadProgressBar"
                             aria-valuenow="0"
                             aria-valuemin="0"
                             aria-valuemax="100">
                            <span id="uploadProgressText" class="fw-bold">0%</span>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted" id="uploadStatus">准备上传...</small>
                        <small class="text-muted" id="uploadSize"></small>
                    </div>
                </div>

                <!-- 处理进度区域 -->
                <div id="processSection" style="display: none;">
                    <div class="text-center mb-3">
                        <i class="fas fa-cogs text-success fa-spin" style="font-size: 2rem;"></i>
                        <h6 class="mt-2 text-secondary">正在处理数据...</h6>
                    </div>
                    <div class="progress mb-3" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                             role="progressbar"
                             style="width: 0%"
                             id="processProgressBar"
                             aria-valuenow="0"
                             aria-valuemin="0"
                             aria-valuemax="100">
                            <span id="processProgressText" class="fw-bold">0%</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <small class="text-muted" id="processStatus">正在处理数据，请稍候...</small>
                    </div>
                </div>

                <!-- 完成状态区域 -->
                <div id="completeSection" style="display: none;">
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success mb-3" style="font-size: 4rem;"></i>
                        <h4 class="text-success mb-3">导入完成！</h4>
                        <p class="text-muted mb-0" id="completeMessage">数据已成功导入到系统中</p>
                    </div>
                </div>

                <!-- 错误状态区域 -->
                <div id="errorSection" style="display: none;">
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle text-danger mb-3" style="font-size: 4rem;"></i>
                        <h4 class="text-danger mb-3">导入失败</h4>
                        <p class="text-muted mb-0" id="errorMessage">请检查文件格式后重试</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <!-- 进度中的按钮 -->
                <div id="progressButtons">
                    <button type="button" class="btn btn-secondary" id="cancelImportBtn">
                        <i class="fas fa-times me-1"></i>取消导入
                    </button>
                </div>
                <!-- 完成后的按钮 -->
                <div id="completeButtons" style="display: none;">
                    <button type="button" class="btn btn-success" id="refreshPageBtn">
                        <i class="fas fa-sync me-1"></i>刷新页面
                    </button>
                    <button type="button" class="btn btn-secondary" id="closeModalBtn1">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
                <!-- 错误后的按钮 -->
                <div id="errorButtons" style="display: none;">
                    <button type="button" class="btn btn-primary" id="retryImportBtn">
                        <i class="fas fa-redo me-1"></i>重新导入
                    </button>
                    <button type="button" class="btn btn-secondary" id="closeModalBtn2">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        class ImportManager {
            constructor() {
                this.elements = this.getElements();
                this.state = {
                    modal: null,
                    taskId: null,
                    progressInterval: null,
                    xhr: null,
                    originalBtnHtml: this.elements.importBtn ? this.elements.importBtn.innerHTML : ''
                };
                this.init();
            }

            getElements() {
                const elements = {
                    fileInput: document.getElementById('fileInput'),
                    importBtn: document.getElementById('importBtn'),
                    progressModal: document.getElementById('progressModal'),
                    modalTitle: document.getElementById('modalTitle'),
                    uploadSection: document.getElementById('uploadSection'),
                    uploadProgressBar: document.getElementById('uploadProgressBar'),
                    uploadProgressText: document.getElementById('uploadProgressText'),
                    uploadStatus: document.getElementById('uploadStatus'),
                    uploadSize: document.getElementById('uploadSize'),
                    processSection: document.getElementById('processSection'),
                    processProgressBar: document.getElementById('processProgressBar'),
                    processProgressText: document.getElementById('processProgressText'),
                    processStatus: document.getElementById('processStatus'),
                    completeSection: document.getElementById('completeSection'),
                    errorSection: document.getElementById('errorSection'),
                    errorMessage: document.getElementById('errorMessage'),
                    progressButtons: document.getElementById('progressButtons'),
                    completeButtons: document.getElementById('completeButtons'),
                    errorButtons: document.getElementById('errorButtons'),
                    cancelBtn: document.getElementById('cancelImportBtn'),
                    refreshBtn: document.getElementById('refreshPageBtn'),
                    retryBtn: document.getElementById('retryImportBtn'),
                    closeBtns: [
                        document.getElementById('closeModalBtn1'),
                        document.getElementById('closeModalBtn2')
                    ]
                };

                for(const key in elements){
                    if(elements[key] === null && key !== 'closeBtns' && !Array.isArray(elements[key])){
                        console.error(`Element with ID '${key}' not found.`);
                    }
                }
                return elements;
            }

            init() {
                if (!this.elements.progressModal || typeof bootstrap === 'undefined' || !bootstrap.Modal) {
                    console.error('Modal container, Bootstrap JS, or Bootstrap Modal component is not available.');
                    if (this.elements.importBtn) {
                        this.elements.importBtn.disabled = true;
                        this.elements.importBtn.textContent = '初始化错误';
                    }
                    return;
                }
                
                // 将模态框移动到 body 的末尾，以避免被 dropdown 等父容器的样式限制
                document.body.appendChild(this.elements.progressModal);

                try {
                    this.state.modal = new bootstrap.Modal(this.elements.progressModal);
                } catch(e) {
                    console.error('Failed to initialize Bootstrap modal.', e);
                    if (this.elements.importBtn) {
                        this.elements.importBtn.disabled = true;
                        this.elements.importBtn.textContent = '初始化错误';
                    }
                    return;
                }

                this.bindEvents();
            }

            bindEvents() {
                this.elements.importBtn.addEventListener('click', () => this.elements.fileInput.click());
                this.elements.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
                this.elements.progressModal.addEventListener('hidden.bs.modal', () => this.reset());

                this.elements.cancelBtn.addEventListener('click', () => this.cancelImport());
                this.elements.refreshBtn.addEventListener('click', () => this.refreshPage());
                this.elements.retryBtn.addEventListener('click', () => this.retryImport());
                this.elements.closeBtns.forEach(btn => {
                    if(btn) btn.addEventListener('click', () => this.closeModal());
                });
            }

            handleFileSelect(e) {
                const file = e.target.files[0];
                if (!file) return;

                const allowedTypes = [
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                    'application/vnd.ms-excel', // .xls
                    'text/csv' // .csv
                ];
                if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|csv)$/i)) {
                    this.showError('请选择正确的表格文件 (.xlsx, .xls, .csv)');
                    this.elements.fileInput.value = '';
                    return;
                }
                this.uploadFile(file);
            }

            uploadFile(file) {
                const formData = new FormData();
                formData.append('file', file);

                this.showModal('upload');
                this.elements.uploadSize.textContent = `文件大小: ${this.formatBytes(file.size)}`;
                this.elements.importBtn.disabled = true;
                this.elements.importBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';

                this.state.xhr = new XMLHttpRequest();
                this.state.xhr.upload.addEventListener('progress', e => {
                    if (e.lengthComputable) {
                        const percentComplete = Math.round((e.loaded / e.total) * 100);
                        this.updateUploadProgress(percentComplete, e.loaded, e.total);
                    }
                });

                this.state.xhr.addEventListener('load', () => {
                    if (this.state.xhr.status === 200) {
                        try {
                            const response = JSON.parse(this.state.xhr.responseText);
                            if (response.errno !== 0) {
                                this.showError('上传失败: ' + (response.msg || '未知错误'));
                                return;
                            }
                            this.state.taskId = response.data.taskId;
                            this.showModal('process');
                            this.elements.importBtn.innerHTML = '<i class="fas fa-cog fa-spin me-1"></i>处理中...';
                            this.startProgressPolling();
                        } catch (e) {
                            this.showError('响应解析失败: ' + e.message);
                        }
                    } else {
                        this.showError(`上传失败: HTTP ${this.state.xhr.status} ${this.state.xhr.statusText}`);
                    }
                });

                this.state.xhr.addEventListener('error', () => this.showError('网络错误，上传失败'));
                this.state.xhr.addEventListener('abort', () => {
                    console.log('Upload aborted by user.');
                    this.reset();
                });

                this.state.xhr.open('POST', 'import');
                this.state.xhr.send(formData);
            }
            
            startProgressPolling() {
                clearInterval(this.state.progressInterval);
                this.state.progressInterval = setInterval(() => {
                    if (!this.state.taskId) {
                        clearInterval(this.state.progressInterval);
                        return;
                    }
                    fetch(`get-import-progress?taskId=${this.state.taskId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.errno === 0 && data.data !== undefined) {
                                this.updateProcessProgress(data.data);
                                if (data.data >= 100) {
                                    clearInterval(this.state.progressInterval);
                                    this.showModal('complete');
                                }
                            } else {
                                console.error('获取进度失败:', data.message || '未知错误');
                                //可以选择在这里停止轮询以防无限出错
                                //clearInterval(this.state.progressInterval);
                                //this.showError('无法获取处理进度，请稍后重试。');
                            }
                        })
                        .catch(error => {
                            console.error('获取进度出错:', error);
                        });
                }, 1000);
            }

            updateUploadProgress(percent, loaded, total) {
                this.elements.uploadProgressBar.style.width = percent + '%';
                this.elements.uploadProgressBar.setAttribute('aria-valuenow', percent);
                this.elements.uploadProgressText.textContent = percent + '%';
                this.elements.uploadStatus.textContent = `正在上传... ${this.formatBytes(loaded)} / ${this.formatBytes(total)}`;
            }

            updateProcessProgress(percent) {
                const roundedPercent = Math.min(100, Math.round(percent));
                this.elements.processProgressBar.style.width = roundedPercent + '%';
                this.elements.processProgressBar.setAttribute('aria-valuenow', roundedPercent);
                this.elements.processProgressText.textContent = roundedPercent + '%';
                this.elements.processStatus.textContent = roundedPercent < 100 ? `正在处理数据... ${roundedPercent}%` : '数据处理完成';
            }

            showModal(type) {
                [this.elements.uploadSection, this.elements.processSection, this.elements.completeSection, this.elements.errorSection].forEach(el => el.style.display = 'none');
                [this.elements.progressButtons, this.elements.completeButtons, this.elements.errorButtons].forEach(el => el.style.display = 'none');

                switch (type) {
                    case 'upload':
                        this.elements.modalTitle.innerHTML = '<i class="fas fa-upload me-2"></i>正在上传文件';
                        this.elements.uploadSection.style.display = 'block';
                        this.elements.progressButtons.style.display = 'block';
                        break;
                    case 'process':
                        this.elements.modalTitle.innerHTML = '<i class="fas fa-cogs me-2"></i>正在处理数据';
                        this.elements.processSection.style.display = 'block';
                        this.elements.progressButtons.style.display = 'block';
                        break;
                    case 'complete':
                        this.elements.modalTitle.innerHTML = '<i class="fas fa-check-circle me-2"></i>导入完成';
                        this.elements.completeSection.style.display = 'block';
                        this.elements.completeButtons.style.display = 'block';
                        break;
                    case 'error':
                        this.elements.modalTitle.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>操作失败';
                        this.elements.errorSection.style.display = 'block';
                        this.elements.errorButtons.style.display = 'block';
                        break;
                }
                if (this.state.modal) this.state.modal.show();
            }

            closeModal() {
                if (this.state.modal) this.state.modal.hide();
            }
            
            showError(message) {
                this.elements.errorMessage.textContent = message;
                this.showModal('error');
            }

            reset() {
                this.elements.importBtn.disabled = false;
                this.elements.importBtn.innerHTML = this.state.originalBtnHtml;
                this.elements.fileInput.value = '';
                this.state.taskId = null;
                if (this.state.xhr) {
                    this.state.xhr.abort();
                    this.state.xhr = null;
                }
                clearInterval(this.state.progressInterval);
                this.state.progressInterval = null;
                
                this.updateUploadProgress(0, 0, 0);
                this.updateProcessProgress(0);
                this.elements.uploadStatus.textContent = '准备上传...';
            }
            
            cancelImport() {
                if (this.state.xhr) {
                    this.state.xhr.abort();
                }
                this.closeModal();
            }

            retryImport() {
                this.closeModal();
                setTimeout(() => this.elements.fileInput.click(), 300);
            }
        
            refreshPage() {
                location.reload();
            }

            formatBytes(bytes, decimals = 2) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const dm = decimals < 0 ? 0 : decimals;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
            }
        }
        
        new ImportManager();
    });
</script>