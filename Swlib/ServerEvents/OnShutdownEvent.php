<?php
declare(strict_types=1);

namespace Swlib\ServerEvents;

use <PERSON>wlib\Connect\MysqlHeart;
use <PERSON>wlib\Connect\PoolMysql;
use Swlib\Connect\PoolRedis;
use Swlib\Connect\RedisHeart;
use Swlib\DataManager\WorkerManager;
use Swlib\Event\Event;
use Swoole\Server;
use Throwable;

class OnShutdownEvent
{
    public function handle(Server $server): void
    {
        echo "Server shutdown, pid: " . $server->master_pid . PHP_EOL;

        try {
            // 触发自定义事件
            Event::emit('OnShutdownEvent', [
                'server' => $server,
            ]);
        } catch (Throwable) {
            // 忽略事件发送异常
        }

        // 停止心跳检测
        try {
            MysqlHeart::stop();
        } catch (Throwable) {
            // 忽略异常
        }

        try {
            RedisHeart::stop();
        } catch (Throwable) {
            // 忽略异常
        }

        // 关闭所有连接池
        try {
            PoolRedis::close();
        } catch (Throwable) {
            // 忽略异常
        }

        try {
            PoolMysql::close();
        } catch (Throwable) {
            // 忽略异常
        }

        try {
            WorkerManager::clear();
        } catch (Throwable) {
            // 忽略异常
        }

        try {
            // 移除监听自定义事件
            Event::offMaps();
        } catch (Throwable) {
            // 忽略异常
        }

        @unlink(RUNTIME_DIR . 'server_pid.txt');
    }
} 