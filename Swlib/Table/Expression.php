<?php
declare(strict_types=1);

namespace Swlib\Table;

/**
 * A wrapper for raw SQL expressions to prevent them from being parameterized in queries.
 * Use this for functions like NOW(), or for atomic updates like `count = count + 1`.
 */
readonly class Expression
{
    public function __construct(public string $value)
    {
    }

    public function __toString(): string
    {
        return $this->value;
    }
} 