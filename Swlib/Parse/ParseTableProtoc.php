<?php
declare(strict_types=1);

namespace Swlib\Parse;

use Exception;
use Swlib\Connect\PoolMysql;
use Swlib\Utils\File;
use Swlib\Utils\Func;

/**
 * Protobuf 文件生成器
 * 
 * 支持的注释配置：
 * - ID 字段注释中的 protobuf:ext:json:[] 用于生成额外字段
 * - 字段注释中的 g-str-field 表示生成额外的字符串字段
 * - 字段注释中的 protobuf:item:xxx 定义字段的 protobuf 类型
 * - 字段注释中的 protobuf:lists:xxx 表示在列表中生成特定类型的字段
 */
class ParseTableProtoc
{
    const string SAVE_DIR = ROOT_DIR . "protos/";
    const string SAVE_DIR_MAPS = ROOT_DIR . "protos/field_maps";
    
    private array $protobufMessage = [];
    private array $importFiles = [];
    private string $dbName;
    private string $tableName;
    private string $upperTableName;
    private array $fieldMaps = [];
    
    /**
     * @throws Exception
     */
    public function __construct(string $database, string $tableName, array $fields, string $tableComment = '')
    {
        $this->dbName = $database;
        $this->tableName = $tableName;
        $this->upperTableName = Func::underscoreToCamelCase($tableName);
        
        $this->initializeFieldMaps();
        $this->generateProtobufFile($fields, $tableComment);
        $this->saveProtobufFile();
        $this->saveFieldMaps();
    }

    /**
     * 编译 proto 文件
     */
    public static function compileProto(): void
    {
        self::cleanupOldFiles();
        $dirs = self::getCompilationDirectories();
        self::executeProtocCompilation($dirs);
    }

    /**
     * 创建目录
     */
    public static function createDir(): void
    {
        ParseTable::createDir(self::SAVE_DIR, false);
    }

    /**
     * 初始化字段映射
     */
    private function initializeFieldMaps(): void
    {
        $fieldMapsFileDir = self::SAVE_DIR_MAPS . "/" . $this->dbName;
        $fieldMapsFileName = $fieldMapsFileDir . "/$this->upperTableName.json";
        
        if (!is_dir($fieldMapsFileDir)) {
            mkdir($fieldMapsFileDir, 0777, true);
        }
        
        if (is_file($fieldMapsFileName)) {
            $this->fieldMaps = json_decode(file_get_contents($fieldMapsFileName), true) ?? [];
        }
    }

    /**
     * 生成 protobuf 文件内容
     */
    private function generateProtobufFile(array $fields, string $tableComment = ''): void
    {
        $this->createNamespace();
        $this->createItemMessage($fields, $tableComment);
        $this->createListsMessage($fields);
    }

    /**
     * 保存 protobuf 文件
     */
    private function saveProtobufFile(): void
    {
        $content = implode(PHP_EOL, $this->protobufMessage);
        $content = str_replace("// import files", implode(PHP_EOL, $this->importFiles), $content);
        
        $filePath = self::SAVE_DIR . $this->dbName . "/$this->upperTableName.proto";
        file_put_contents($filePath, $content . PHP_EOL);
    }

    /**
     * 保存字段映射文件
     */
    private function saveFieldMaps(): void
    {
        $fieldMapsFileDir = self::SAVE_DIR_MAPS . "/" . $this->dbName;
        $fieldMapsFileName = $fieldMapsFileDir . "/$this->upperTableName.json";
        file_put_contents($fieldMapsFileName, json_encode($this->fieldMaps));
    }

    /**
     * 创建命名空间和基础结构
     */
    private function createNamespace(): void
    {
        $str = 'syntax = "proto3";' . PHP_EOL . PHP_EOL;
        $str .= "// import files" . PHP_EOL . PHP_EOL;
        $str .= '// protoc  --php_out=../../   *.proto ' . PHP_EOL . PHP_EOL;
        $str .= "package Protobuf.$this->dbName.$this->upperTableName;" . PHP_EOL;
        $str .= "option php_metadata_namespace = \"GPBMetadata\\\\$this->dbName\";" . PHP_EOL;

        $this->protobufMessage[] = $str;
    }

    /**
     * 创建 Item 消息结构
     */
    private function createItemMessage(array $fields, string $tableComment = ''): void
    {
        // 添加默认查询字段
        $fields = $this->addDefaultQueryFields($fields);
        
        // 获取扩展字段
        $extFields = $this->getExtField($fields, 'item', $this->upperTableName);
        $fields = array_merge($fields, $extFields);

        // 添加表注释
        $messageComment = $this->formatComment($tableComment ?: "数据表 $this->tableName 的 protobuf 消息定义");
        $str = $messageComment . PHP_EOL;
        $str .= "message {$this->upperTableName}Proto {" . PHP_EOL;

        // 处理每个字段
        foreach ($fields as $item) {
            $field = $item['Field'];
            $dbType = $item['Type'];
            $comment = $item['Comment'] ?? '';

            $fieldName = Func::underscoreToCamelCase($field);
            $protobufFields = $this->generateItemFields($dbType, $fieldName, $comment);

            foreach ($protobufFields as $protobufField) {
                $index = $this->getFieldIndex($protobufField);
                
                // 添加字段注释
                if (!empty($comment)) {
                    $fieldComment = $this->formatComment($comment, 1);
                    $str .= $fieldComment . PHP_EOL;
                }
                
                $str .= "    $protobufField = $index;" . PHP_EOL;
            }
        }
        
        $str .= "}";
        $this->protobufMessage[] = $str;
    }

    /**
     * 创建 Lists 消息结构
     */
    private function createListsMessage(array $fields): void
    {
        $listFields = $this->getDefaultListFields();
        
        // 处理字段中的 protobuf:lists: 配置
        foreach ($fields as $item) {
            $field = $item['Field'];
            $comment = $item['Comment'] ?? '';

            $type = $this->getFieldType($comment, 'protobuf:lists:');
            if ($type !== false) {
                $listFields[] = ['Field' => $field, 'Type' => $type, 'Comment' => $comment];
            }
        }

        // 获取扩展字段
        $extFields = $this->getExtField($fields, 'lists', "{$this->upperTableName}ListsProto");
        $listFields = array_merge($listFields, $extFields);

        // 生成 Lists 消息
        $comment = $this->formatComment("数据表 $this->tableName 的列表 protobuf 消息定义");
        $str = $comment . PHP_EOL;
        $str .= "message {$this->upperTableName}ListsProto {" . PHP_EOL;

        $index = 1;
        foreach ($listFields as $fieldInfo) {
            $type = $fieldInfo['Type'];
            $field = $fieldInfo['Field'];
            $fieldComment = $fieldInfo['Comment'] ?? '';
            
            // 添加字段注释
            if (!empty($fieldComment)) {
                $comment = $this->formatComment($fieldComment, 1);
                $str .= $comment . PHP_EOL;
            }
            
            $str .= "    $type $field = $index;" . PHP_EOL;
            $index++;
        }

        $str .= "}";
        $this->protobufMessage[] = $str;
    }

    /**
     * 格式化注释
     */
    private function formatComment(string $comment, int $indentLevel = 0): string
    {
        if (empty($comment)) {
            return '';
        }

        $indent = str_repeat('    ', $indentLevel);
        $lines = explode("\n", $comment);
        $formattedLines = [];

        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line)) {
                $formattedLines[] = $indent . '// ' . $line;
            }
        }

        return implode(PHP_EOL, $formattedLines);
    }

    /**
     * 添加默认查询字段
     */
    private function addDefaultQueryFields(array $fields): array
    {
        $defaultFields = [
            ['Field' => 'query_page_no', 'Type' => 'int', 'Comment' => '查询页码'],
            ['Field' => 'query_page_size', 'Type' => 'int', 'Comment' => '每页数量'],
            ['Field' => 'query_sort_field', 'Type' => 'string', 'Comment' => '排序字段'],
            ['Field' => 'query_sort_type', 'Type' => 'string', 'Comment' => '排序类型'],
            ['Field' => 'query_count', 'Type' => 'int', 'Comment' => '查询总数'],
            ['Field' => 'ext_int', 'Type' => 'int', 'Comment' => '扩展整数字段'],
            ['Field' => 'ext_str', 'Type' => 'string', 'Comment' => '扩展字符串字段'],
        ];

        return array_merge($fields, $defaultFields);
    }

    /**
     * 获取默认列表字段
     */
    private function getDefaultListFields(): array
    {
        return [
            ['Field' => 'lists', 'Type' => "repeated {$this->upperTableName}Proto", 'Comment' => '数据列表'],
            ['Field' => 'total', 'Type' => 'int32', 'Comment' => '总记录数'],
            ['Field' => 'curr_page', 'Type' => 'int32', 'Comment' => '当前页码'],
            ['Field' => 'total_page', 'Type' => 'int32', 'Comment' => '总页数'],
        ];
    }

    /**
     * 获取字段索引
     */
    private function getFieldIndex(string $fieldDefinition): int
    {
        $index = $this->fieldMaps[$fieldDefinition] ?? (count($this->fieldMaps) + 1);
        $this->fieldMaps[$fieldDefinition] = $index;
        return $index;
    }

    /**
     * 生成单个字段的 protobuf 定义
     */
    private function generateItemFields(string $dbType, string $fieldName, string $comment): array
    {
        $field = lcfirst($fieldName);
        $customType = $this->getFieldType($comment, 'protobuf:item:');
        $fields = [];

        // 检查是否需要生成字符串字段
        if (stripos($comment, 'g-str-field') !== false) {
            $fields[] = "string {$field}Str";
        }

        // 根据数据库类型生成对应的 protobuf 字段
        $protobufType = $this->mapDbTypeToProtobuf($dbType, $field, $comment, $customType);
        
        if (is_array($protobufType)) {
            $fields = array_merge($fields, $protobufType);
        } else {
            $fields[] = $protobufType;
        }

        return $fields;
    }

    /**
     * 将数据库类型映射为 protobuf 类型
     */
    private function mapDbTypeToProtobuf(string $dbType, string $field, string $comment, string|false $customType): string|array
    {
        // 如果有自定义类型，优先使用
        $defaultType = $customType ?: $this->getDefaultProtobufType($dbType);

        return match (true) {
            str_starts_with($dbType, 'int') => $this->handleIntType($field, $comment, $defaultType),
            $this->isStringType($dbType) => "$defaultType $field",
            str_starts_with($dbType, 'bool') => ($customType ?: 'bool') . " $field",
            $this->isNumericType($dbType) => $this->handleNumericType($dbType, $field, $customType),
            str_starts_with($dbType, 'set') => "repeated string $field",
            str_starts_with($dbType, 'enum') => $this->handleEnumType($dbType, $field),
            str_starts_with($dbType, 'json') => $this->handleJsonType($field, $customType),
            str_starts_with($dbType, 'repeated') => "$dbType $field",
            default => ($customType ?: ($dbType ?: 'string')) . " $field",
        };
    }

    /**
     * 处理整数类型
     */
    private function handleIntType(string $field, string $comment, string $defaultType): array|string
    {
        $fields = [];
        
        // 时间字段自动添加字符串格式（除非已经指定了 g-str-field）
        if (stripos($field, 'time') !== false && stripos($comment, 'g-str-field') === false) {
            $fields[] = "string {$field}Str";
        }
        
        $fields[] = "$defaultType $field";
        return count($fields) === 1 ? $fields[0] : $fields;
    }

    /**
     * 处理数值类型
     */
    private function handleNumericType(string $dbType, string $field, string|false $customType): string
    {
        $typeMap = [
            'mediumint' => 'int32',
            'smallint' => 'int32', 
            'tinyint' => 'int32',
            'bigint' => 'int64',
            'decimal' => 'float',
            'float' => 'float',
            'double' => 'double',
        ];

        foreach ($typeMap as $dbPrefix => $protobufType) {
            if (str_starts_with($dbType, $dbPrefix)) {
                $type = $customType ?: $protobufType;
                return "$type $field";
            }
        }

        return ($customType ?: 'string') . " $field";
    }

    /**
     * 处理枚举类型
     */
    private function handleEnumType(string $dbType, string $field): string
    {
        $fieldName = ucfirst($field);
        $this->createEnumProtoc($this->upperTableName, $fieldName, $dbType);
        return "$this->upperTableName{$fieldName}Enum $field";
    }

    /**
     * 处理 JSON 类型
     */
    private function handleJsonType(string $field, string|false $customType): array
    {
        $type = $customType ?: 'int32';
        $fields = ["repeated $type $field"];
        
        if ($type === "int32") {
            $fields[] = "repeated string {$field}Str";
        }
        
        return $fields;
    }

    /**
     * 检查是否为字符串类型
     */
    private function isStringType(string $dbType): bool
    {
        $stringTypes = [
            'varchar', 'char', 'text', 'datetime', 'longtext', 
            'binary', 'date', 'timestamp', 'time', 'blob', 
            'longblob', 'varbinary'
        ];

        return array_any($stringTypes, fn($type) => str_starts_with($dbType, $type));

    }

    /**
     * 检查是否为数值类型
     */
    private function isNumericType(string $dbType): bool
    {
        $numericTypes = [
            'mediumint', 'smallint', 'tinyint', 'bigint', 
            'decimal', 'float', 'double'
        ];

        return array_any($numericTypes, fn($type) => str_starts_with($dbType, $type));

    }

    /**
     * 获取默认的 protobuf 类型
     */
    private function getDefaultProtobufType(string $dbType): string
    {
        return match (true) {
            str_starts_with($dbType, 'int') => 'int32',
            str_starts_with($dbType, 'bigint') => 'int64',
            str_starts_with($dbType, 'bool') => 'bool',
            str_starts_with($dbType, 'float'), str_starts_with($dbType, 'decimal') => 'float',
            str_starts_with($dbType, 'double') => 'double',
            default => 'string',
        };
    }

    /**
     * 获取扩展字段配置
     */
    private function getExtField(array $fields, string $pos, string $tableName): array
    {
        $extFields = [];
        
        foreach ($fields as $field) {
            if ($field['Field'] !== 'id') {
                continue;
            }

            $comment = $field['Comment'] ?? '';
            if (empty($comment) || !str_contains($comment, 'protobuf:ext:json:')) {
                continue;
            }

            $extFields = $this->parseExtensionConfig($comment, $pos, $tableName);
            break;
        }

        return $extFields;
    }

    /**
     * 解析扩展配置
     */
    private function parseExtensionConfig(string $comment, string $pos, string $tableName): array
    {
        $json = str_replace('protobuf:ext:json:', '', $comment);
        $json = trim($json);
        
        $extConfig = json_decode($json, true);
        if (empty($extConfig)) {
            return [];
        }

        $extFields = [];
        foreach ($extConfig as $item) {
            $parts = explode(':', $item);
            if (count($parts) < 3) {
                continue;
            }

            $configPos = trim($parts[0]);
            $fieldName = trim($parts[1]);
            $type = trim($parts[2]);

            if ($configPos !== $pos) {
                continue;
            }

            $type = str_replace('$self', $tableName, $type);
            $this->importFile($type, $tableName);
            
            $extFields[] = [
                'Field' => $fieldName,
                'Type' => $type,
                'Comment' => "扩展字段: $fieldName",
            ];
        }

        return $extFields;
    }

    /**
     * 处理文件导入
     */
    private function importFile(string $type, string $tableName): void
    {
        $tempTableName = str_replace('ListsProto', '', $tableName);
        $typeArr = explode('.', $type);
        
        // 检查是否需要导入
        if (count($typeArr) < 3 || 
            !in_array($typeArr[0], ['Protobuf', 'repeated Protobuf']) || 
            $typeArr[1] !== $this->dbName ||
            $typeArr[2] === $tempTableName) {
            return;
        }

        $fileName = "$typeArr[2].proto";
        $importStatement = "import \"$fileName\";";
        
        if (!in_array($importStatement, $this->importFiles)) {
            $this->importFiles[] = $importStatement;
        }
    }

    /**
     * 创建枚举类型
     */
    private function createEnumProtoc(string $tableName, string $fieldName, string $enumDefinition): void
    {
        $enumValues = $this->parseEnumValues($enumDefinition);
        $enumName = "$tableName$fieldName";
        $upperFieldName = strtoupper($fieldName);

        $comment = $this->formatComment("枚举类型: $fieldName");
        $enumStr = $comment . PHP_EOL;
        $enumStr .= "enum {$enumName}Enum {" . PHP_EOL;
        $enumStr .= "    // 未定义值" . PHP_EOL;
        $enumStr .= "    UNDEFINED_$upperFieldName = 0;" . PHP_EOL;

        foreach ($enumValues as $index => $value) {
            $key = strtoupper($value);
            $enumValue = $index + 1;
            $enumStr .= "    // 枚举值: $value" . PHP_EOL;
            $enumStr .= "    $key = $enumValue;" . PHP_EOL;
        }

        $enumStr .= "}" . PHP_EOL;
        $this->protobufMessage[] = $enumStr;
    }

    /**
     * 解析枚举值
     */
    private function parseEnumValues(string $enumDefinition): array
    {
        $enumStr = substr($enumDefinition, 5, -1); // 移除 'enum(' 和 ')'
        $enumStr = str_replace(["'", '"'], "", $enumStr);
        return array_map('trim', explode(",", $enumStr));
    }

    /**
     * 从注释中提取字段类型
     */
    private function getFieldType(string $comment, string $prefix): string|false
    {
        $index = stripos($comment, $prefix);
        if ($index === false) {
            return false;
        }

        if (preg_match("/" . preg_quote($prefix) . "([a-zA-Z0-9.\\s]+)/", $comment, $matches)) {
            return trim($matches[1]);
        }

        return false;
    }

    /**
     * 清理旧文件
     */
    private static function cleanupOldFiles(): void
    {
        File::delDirectory(RUNTIME_DIR . "Protobuf/Protobuf/");
        File::delDirectory(RUNTIME_DIR . "Protobuf/GPBMetadata/");
    }

    /**
     * 获取编译目录列表
     */
    private static function getCompilationDirectories(): array
    {
        $dirs = [""];
        PoolMysql::eachDbName(function ($dbName) use (&$dirs) {
            $dirs[] = Func::underscoreToCamelCase($dbName);
        });
        return $dirs;
    }

    /**
     * 执行 protoc 编译
     */
    private static function executeProtocCompilation(array $dirs): void
    {
        $outDir = RUNTIME_DIR . "Protobuf/";
        if (!is_dir($outDir)) {
            mkdir($outDir, 0777, true);
        }

        foreach ($dirs as $dir) {
            $inDir = rtrim(self::SAVE_DIR . "/$dir", '/');
            if (!is_dir($inDir)) {
                continue;
            }

            $command = "protoc -I $inDir $inDir/*.proto --php_out=$outDir";
            echo "执行编译命令: $command" . PHP_EOL;
            exec($command);
        }
    }
}