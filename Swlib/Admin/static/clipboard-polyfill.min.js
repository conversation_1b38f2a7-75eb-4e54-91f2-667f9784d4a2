!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).clipboard={})}(this,function(e){"use strict";function o(e,a,u,c){return new(u=u||Promise)(function(n,t){function r(e){try{o(c.next(e))}catch(e){t(e)}}function i(e){try{o(c.throw(e))}catch(e){t(e)}}function o(e){var t;e.done?n(e.value):((t=e.value)instanceof u?t:new u(function(e){e(t)})).then(r,i)}o((c=c.apply(e,a||[])).next())})}function s(n,r){var i,o,a,u={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},e={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(t){return function(e){return function(t){if(i)throw new TypeError("Generator is already executing.");for(;u;)try{if(i=1,o&&(a=2&t[0]?o.return:t[0]?o.throw||((a=o.return)&&a.call(o),0):o.next)&&!(a=a.call(o,t[1])).done)return a;switch(o=0,a&&(t=[2&t[0],a.value]),t[0]){case 0:case 1:a=t;break;case 4:return u.label++,{value:t[1],done:!1};case 5:u.label++,o=t[1],t=[0];continue;case 7:t=u.ops.pop(),u.trys.pop();continue;default:if(!((a=0<(a=u.trys).length&&a[a.length-1])||6!==t[0]&&2!==t[0])){u=0;continue}if(3===t[0]&&(!a||t[1]>a[0]&&t[1]<a[3])){u.label=t[1];break}if(6===t[0]&&u.label<a[1]){u.label=a[1],a=t;break}if(a&&u.label<a[2]){u.label=a[2],u.ops.push(t);break}a[2]&&u.ops.pop(),u.trys.pop();continue}t=r.call(n,u)}catch(e){t=[6,e],o=0}finally{i=a=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}([t,e])}}}var t=function(e){};function a(e){t(e)}(function(){(console.warn||console.log).apply(console,arguments)}).bind("[clipboard-polyfill]");var n,r,i,u,c="undefined"==typeof navigator?void 0:navigator,l=null==c?void 0:c.clipboard,d=null===(n=null==l?void 0:l.read)||void 0===n?void 0:n.bind(l),f=null===(r=null==l?void 0:l.readText)||void 0===r?void 0:r.bind(l),p=null===(i=null==l?void 0:l.write)||void 0===i?void 0:i.bind(l),v=null===(u=null==l?void 0:l.writeText)||void 0===u?void 0:u.bind(l),h="undefined"==typeof window?void 0:window,y=null==h?void 0:h.ClipboardItem,b=h;function w(){return"undefined"==typeof ClipboardEvent&&void 0!==b.clipboardData&&void 0!==b.clipboardData.setData}var m=function(){this.success=!1};function g(e){var t=new m,n=function(e,t,n){for(var r in a("listener called"),e.success=!0,t){var i=t[r],o=n.clipboardData;o.setData(r,i),"text/plain"===r&&o.getData(r)!==i&&(a("setting text/plain failed"),e.success=!1)}n.preventDefault()}.bind(this,t,e);document.addEventListener("copy",n);try{document.execCommand("copy")}finally{document.removeEventListener("copy",n)}return t.success}function x(e,t){E(e);var n=g(t);return T(),n}function E(e){var t,n=document.getSelection();n&&((t=document.createRange()).selectNodeContents(e),n.removeAllRanges(),n.addRange(t))}function T(){var e=document.getSelection();e&&e.removeAllRanges()}function C(r){return o(this,void 0,void 0,function(){var n;return s(this,function(e){if(n="text/plain"in r,w()){if(!n)throw new Error("No `text/plain` value was specified.");if(t=r["text/plain"],b.clipboardData.setData("Text",t))return[2,!0];throw new Error("Copying failed, possibly because the user rejected it.")}var t;return g(r)?(a("regular execCopy worked"),[2,!0]):-1<navigator.userAgent.indexOf("Edge")?(a('UA "Edge" => assuming success'),[2,!0]):x(document.body,r)?(a("copyUsingTempSelection worked"),[2,!0]):function(e){var t=document.createElement("div");t.setAttribute("style","-webkit-user-select: text !important"),t.textContent="temporary element",document.body.appendChild(t);var n=x(t,e);return document.body.removeChild(t),n}(r)?(a("copyUsingTempElem worked"),[2,!0]):function(e){a("copyTextUsingDOM");var t=document.createElement("div");t.setAttribute("style","-webkit-user-select: text !important");var n=t;t.attachShadow&&(a("Using shadow DOM."),n=t.attachShadow({mode:"open"}));var r=document.createElement("span");r.innerText=e,n.appendChild(r),document.body.appendChild(t),E(r);var i=document.execCommand("copy");return T(),document.body.removeChild(t),i}(r["text/plain"])?(a("copyTextUsingDOM worked"),[2,!0]):[2,!1]})})}function D(){return o(this,void 0,void 0,function(){return s(this,function(e){if(f)return a("Using `navigator.clipboard.readText()`."),[2,f()];if(w())return a("Reading text using IE strategy."),[2,function(){return o(this,void 0,void 0,function(){var t;return s(this,function(e){if(""===(t=b.clipboardData.getData("Text")))throw new Error("Empty clipboard or could not read plain text from clipboard");return[2,t]})})}()];throw new Error("Read is not supported in your browser.")})})}function S(e,t){for(var n=0,r=e;n<r.length;n++)if(-1!==r[n].types.indexOf(t))return!0;return!1}var k=(U.prototype.getType=function(t){return o(this,void 0,void 0,function(){return s(this,function(e){return[2,this._items[t]]})})},U);function U(e,t){var n;for(var r in void 0===t&&(t={}),this.types=Object.keys(e),this._items={},e){var i=e[r];this._items[r]="string"==typeof i?O(r,i):i}this.presentationStyle=null!==(n=null==t?void 0:t.presentationStyle)&&void 0!==n?n:"unspecified"}function O(e,t){return new Blob([t],{type:e})}function A(c){return o(this,void 0,void 0,function(){var t,n,r,i,o,a,u;return s(this,function(e){switch(e.label){case 0:t={},n=0,r=c.types,e.label=1;case 1:return n<r.length?(i=r[n],o=t,a=i,[4,c.getType(i)]):[3,4];case 2:o[a]=e.sent(),e.label=3;case 3:return n++,[3,1];case 4:return u={},c.presentationStyle&&(u.presentationStyle=c.presentationStyle),[2,new y(t,u)]}})})}function R(e){var t={};return t["text/plain"]=O(e,"text/plain"),new k(t)}function _(t,n){return o(this,void 0,void 0,function(){return s(this,function(e){switch(e.label){case 0:return[4,t.getType(n)];case 1:return[4,function(i){return o(this,void 0,void 0,function(){return s(this,function(e){return[2,new Promise(function(t,n){var r=new FileReader;r.addEventListener("load",function(){var e=r.result;"string"==typeof e?t(e):n("could not convert blob to string")}),r.readAsText(i)})]})})}(e.sent())];case 2:return[2,e.sent()]}})})}e.ClipboardItem=k,e.read=function(){return o(this,void 0,void 0,function(){var t;return s(this,function(e){switch(e.label){case 0:return d?(a("Using `navigator.clipboard.read()`."),[2,d()]):(t=R,[4,D()]);case 1:return[2,[t.apply(void 0,[e.sent()])]]}})})},e.readText=D,e.setDebugLog=function(e){t=e},e.suppressWarnings=function(){0},e.write=function(i){return o(this,void 0,void 0,function(){var t,n,r;return s(this,function(e){switch(e.label){case 0:return p&&y?(a("Using `navigator.clipboard.write()`."),[4,Promise.all(i.map(A))]):[3,5];case 1:t=e.sent(),e.label=2;case 2:return e.trys.push([2,4,,5]),[4,p(t)];case 3:return[2,e.sent()];case 4:if(n=e.sent(),!S(i,"text/plain")&&!S(i,"text/html"))throw n;return[3,5];case 5:return S(i,"text/plain")||a("clipboard.write() was called without a `text/plain` data type. On some platforms, this may result in an empty clipboard. Call suppressWarnings() to suppress this warning."),r=C,[4,function(u){return o(this,void 0,void 0,function(){var t,n,r,i,o,a;return s(this,function(e){switch(e.label){case 0:t={},n=0,r=u.types,e.label=1;case 1:return n<r.length?(i=r[n],o=t,[4,_(u,a=i)]):[3,4];case 2:o[a]=e.sent(),e.label=3;case 3:return n++,[3,1];case 4:return[2,t]}})})}(i[0])];case 6:if(!r.apply(void 0,[e.sent()]))throw new Error("write() failed");return[2]}})})},e.writeText=function(n){return o(this,void 0,void 0,function(){return s(this,function(e){if(v)return a("Using `navigator.clipboard.writeText()`."),[2,v(n)];if(!C(((t={})["text/plain"]=n,t)))throw new Error("writeText() failed");var t;return[2]})})},Object.defineProperty(e,"__esModule",{value:!0})});