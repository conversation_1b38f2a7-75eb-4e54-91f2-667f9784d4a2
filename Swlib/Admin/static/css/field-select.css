.select-box {
    position: relative;
}

.select-box .form-control {
    cursor: pointer;
}

/* 下拉图标样式 */
.select-box .select-dropdown-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: 0.8rem;
    color: #6c757d;
    pointer-events: none;
    z-index: 3;
}

.select-box-filter .select-dropdown-icon {
    font-size: 0.75rem;
    top: calc(50% - 1px);
}

.select-box .icon {
    position: absolute;
    right: 42px;
    top: 8px;
    cursor: pointer;
    transition: all .5s;
    font-weight: 900;
}
.select-box.select-box-filter .icon {
    position: absolute;
    right: 14px;
    top: 4px;
    background: #ffffff;
    width: 24px;
    text-align: center;
}

.select-box .icon.icon-close {
    right: 60px;
    top: 5px;
    font-size: 20px;
    display: none;
}

.select-box .icon.icon-close:hover {
    transform: rotate(90deg);
}

.select-box .icon.icon-down {
    user-select: none;
}

.select-box .icon.icon-down:hover {
    transform: scale(1.2);
}

/* 选中值容器样式 */
.select-box .selected-value-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #ced4da;
    border-radius: 0;
    padding: 0.25rem 0.5rem;
    background-color: #fff;
}

.select-box-filter .selected-value-container {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.select-box .selected-value-container .selected-text {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.select-box .selected-value-container .btn-clear {
    background: transparent;
    border: none;
    margin-left: 8px;
    padding: 0;
    font-size: 1.2rem;
    line-height: 0.8;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s;
}

.select-box .selected-value-container .btn-clear:hover {
    color: #dc3545;
    transform: scale(1.2);
}

/* 下拉菜单样式优化 */
.select-box .dropdown-menu {
    width: 100%;
    max-height: 280px;
    overflow-y: auto;
    padding: 0;
}

/* 下拉菜单头部样式 */
.select-box .dropdown-menu .dropdown-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.select-box .dropdown-menu .dropdown-menu-title {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.select-box .dropdown-menu .btn-close-dropdown {
    background: transparent;
    border: none;
    padding: 0;
    font-size: 1.2rem;
    line-height: 0.8;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s;
}

.select-box .dropdown-menu .btn-close-dropdown:hover {
    color: #dc3545;
    transform: scale(1.2);
}

.select-box .dropdown-menu .dropdown-no-results {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 0.5rem 0;
}