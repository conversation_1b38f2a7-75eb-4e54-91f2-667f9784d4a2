.layout {
    display: flex;
    flex-direction: row;
}
.disabled{
    cursor: no-drop;
    user-select: none;
}
.layout-left {
    width: 220px;
    min-width: 220px;
}

.layout-right {
    flex: 1;
}

.admin-title {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    height: 50px;
    border-bottom: 1px solid #F2F2F2;
    margin-bottom: 0;
}

.accordion {
    --bs-accordion-btn-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16"> <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4"/> </svg>');
    --bs-accordion-btn-active-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x" viewBox="0 0 16 16"><path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"/> </svg>');
}

.accordion-button::after {
    opacity: .15;
}

.accordion .accordion-button {
    box-shadow: unset;
}

.accordion-body {
    padding-left: 36px;
    overflow: hidden;
}

.nav-pills {
    --bs-nav-pills-link-active-bg: #dbdde1;
}

.nav-pills .nav-link {
    color: #676f7e;
    transition: all 0.5s;
    padding-left: 10px;
    position: relative;
    z-index: 2;
}

.nav-pills .nav-link:after {
    content: '';
    width: 25px;
    height: 50px;
    border-left: 1px solid #dbdde1;
    border-bottom: 1px solid #dbdde1;
    display: block;
    position: absolute;
    left: -20px;
    top: -30px;
    z-index: 1;
}

.nav-pills .nav-link:hover {
    color: #000000;
}


.lists-pagination .page-cus .page-link {
    user-select: none;
    background: unset;
    border-width: 0;
}

.lists-pagination .select-page-size {
    padding-right: 20px;
    padding-left: 10px;
}

.filter-down-icon {
    right: 20px;
    top: 4px;
    cursor: pointer;
}

#loading {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    z-index: 3;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, .3);
    color: #ffffff;
}

@keyframes loading {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

#loading .icon {
    font-size: 32px;
    color: #ffffff;
    animation: loading 1s linear infinite;
}

table tr td .badge {
    font-size: 10px;
    color: #e9e9e9;
}

.th-action {
    min-width: 105px;
}

.td-int2time {
    display: inline-block;
    min-width: 158px;
}

@keyframes admin-toast {
    0% {
        top: 25%;
        opacity: 1;
    }
    75% {
        top: 20%;
        opacity: .8;
    }
    to {
        top: 5%;
        opacity: 0;
    }
}
.admin-toast {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    top: 25%;
    z-index: 2;
    background: rgba(0, 0, 0, .8);
    color: #ffffff;
    padding: 10px 20px;
    border-radius: 10px;
    animation: admin-toast 1.5s;
}

.table-th {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
}

.order-sort-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-left: 3px;
    margin-top: -5px;
}

.order-sort-box a {
    color: #333333;
    text-decoration: unset;
    font-size: 14px;
    height: 14px;
}

.order-sort-box a.curr {
    color: #0d6efd;
}

.order-sort-box a:hover {
    color: #0d6efd;
}

.order-sort-box a.sort-down {
    margin-top: -2px;
}

.order-sort-box a.sort-up {
    margin-bottom: -2px;
}

/* 当视口宽度大于等于 768px 时，通常认为是 PC 设备 */
@media screen and (min-width: 768px) {
    .list-table {
        max-width: calc(100vw - 286px);min-height: 70vh;
    }
}

