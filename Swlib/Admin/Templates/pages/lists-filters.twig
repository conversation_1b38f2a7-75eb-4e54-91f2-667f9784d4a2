{% for filter in filterFields %}
    {% include filter.templateFilter %}
{% endfor %}

<div class="col-auto mb-2">
    <div class="btn-group shadow">
        <button type="submit" class="btn btn-primary btn-sm rounded-0">
            <i class="bi bi-search"></i>
            {{ lang("搜索") }}
        </button>
        <a href="{{ listRefreshUrl }}" class="btn btn-sm rounded-0 btn-primary">
            <i class="bi bi-x"></i>
        </a>
    </div>

</div>
