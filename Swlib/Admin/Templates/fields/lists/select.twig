{% if value is empty %}
    <span class="badge user-select-none rounded-0">未赋值</span>
{% else %}
    {% if field.relationUrl %}
        <a class="d-inline-block text-truncate {% for class in field.classes %}{{ class }}{% endfor %}"
           style="max-width: {{ field.listMaxWidth }}px;"
           data-bs-toggle="tooltip" {% if field.showText %}data-bs-title='{{ field.showText }}'{% endif %}
           href="{{ field.relationUrl }}?{{ field.idFieldOriginalName }}={{ value }}"
           data-value="{{ value }}"
           {% for attrKey,attrValue in field.attributes %}data-{{ attrKey }}="{{ attrValue }}"{% endfor %}
        >
            {{ field.showText }}
        </a>
    {% else %}
        <span class="d-inline-block text-truncate {% for class in field.classes %}{{ class }}{% endfor %}"
              style="max-width: {{ field.listMaxWidth }}px;"
              data-value="{{ value }}"
              {% for attrKey,attrValue in field.attributes %}data-{{ attrKey }}="{{ attrValue }}"{% endfor %}
              data-bs-toggle="tooltip" {% if field.showText %}data-bs-title='{{ field.showText }}'{% endif %}
              {% if field.showText %}onclick="copyText('{{ field.showText }}')"{% endif %}
        >
            {{ field.showText }}
        </span>
    {% endif %}
{% endif %}
