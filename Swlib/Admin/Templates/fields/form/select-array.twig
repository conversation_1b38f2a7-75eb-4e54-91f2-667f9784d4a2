<div class="row mb-3">
    <label for="{{ field.elemId }}"
           class="col-sm-2 col-form-label text-end {% for class in field.classes %}{{ class }}{% endfor %}"
           {% for attrKey,attrValue in field.attributes %}data-{{ attrKey }}="{{ attrValue }}"{% endfor %}>
        {{ field.label }}</label>
    <div class="col-sm-10 select-lists" id="{{ field.elemId }}">
        {#  复用 select.twig 的完整结构来创建数组选择 #}

        {% for item in field.showTexts %}
            <div class="select-list-row d-flex align-items-start mb-2">
                <div class="select-box flex-fill pe-2">
                    {# 输入模式的元素 - 复用select.twig结构 #}
                    <input type="text"
                           class="form-control rounded-0 dropdown-toggle select-input"
                           autocomplete="off"
                           data-bs-toggle="dropdown"
                           data-field="{{ field.field }}"
                           data-url="{{ url('get-select-list') }}"
                            {% if field.options is not empty %}
                                readonly
                            {% else %}
                                onfocus="handleInputOnFocus(this)"
                                onkeyup="handleInputOnKeyup(this)"
                            {% endif %}
                           {% if field.required %}required{% endif %}
                           {% if field.disabled %}disabled{% endif %}
                           {% for attrKey,attrValue in field.attributes %}{{ attrKey }}="{{ attrValue }}"{% endfor %}
                           placeholder="{{ lang('请输入') }}{{ field.label }}"
                           aria-label="{{ field.label }}"
                           value="{{ item.text }}"
                           {% if item.id is not empty %}style="display: none;"{% endif %}>
                           
                    {# 输入框右侧下拉图标 #}
                    <i class="bi bi-chevron-down select-dropdown-icon" data-bs-toggle="dropdown" {% if item.id is not empty %}style="display: none;"{% endif %}></i>
                           
                    {# 选中值容器 - 当有值被选中时显示 #}
                    <div class="selected-value-container form-control" {% if item.id is empty %}style="display: none;"{% else %}style="display: flex;"{% endif %}>
                        <span class="selected-text">{{ item.text }}</span>
                        <button type="button" class="btn-clear" onclick="clearSelectValue(this)">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>

                    {# 隐藏的实际表单值 - 数组形式 #}
                    <input type="text" class="form-control visually-hidden" {% if field.required %}required{% endif %}
                           name="{{ field.field }}[]" value="{{ item.id }}"
                           aria-label="{{ field.label }}">

                    <div class="invalid-feedback">
                        请选择{{ field.label }}
                    </div>

                    {# 下拉菜单 #}
                    <ul class="dropdown-menu">
                        {# 下拉菜单头部 - 关闭按钮 #}
                        <li class="dropdown-menu-header">
                            <span class="dropdown-menu-title">{{ lang('选择') }} {{ field.label }}</span>
                            <button type="button" class="btn-close-dropdown" onclick="closeDropdown(this)">
                                <i class="bi bi-x"></i>
                            </button>
                        </li>
                        
                        {% if field.options is not empty %}
                            {% for option in field.options %}
                                <li onclick="selectDropdownMenuItem(this,'{{ option.id }}','{{ option.text }}')">
                                    <a class="dropdown-item" href="javascript:">
                                        {{ option.text }}
                                    </a>
                                </li>
                            {% endfor %}
                        {% endif %}
                    </ul>
                </div>
                
                {# 第一行显示添加按钮，其他行显示删除按钮 #}
                {% if loop.first %}
                    <button type="button" class="btn btn-primary rounded-0"
                            data-placeholder="{{ lang('请输入') }}{{ field.label }}"
                            data-label="{{ field.label }}"
                            data-required="{{ field.required }}"
                            data-disabled="{{ field.disabled }}"
                            data-field="{{ field.field }}"
                            data-url="{{ url('get-select-list') }}"
                            onclick="addSelectArrayItem(this)">
                        <i class="bi bi-plus"></i>
                    </button>
                {% else %}
                    <button type="button" class="btn btn-danger rounded-0" onclick="delSelectArrayItem(this)">
                        <i class="bi bi-dash"></i>
                    </button>
                {% endif %}
            </div>
        {% else %}
            {# 如果没有初始数据，创建一个空行 #}
            <div class="select-list-row d-flex align-items-start mb-2">
                <div class="select-box flex-fill pe-2">
                    {# 输入模式的元素 #}
                    <input type="text"
                           class="form-control rounded-0 dropdown-toggle select-input"
                           autocomplete="off"
                           data-bs-toggle="dropdown"
                           data-field="{{ field.field }}"
                           data-url="{{ url('get-select-list') }}"
                            {% if field.options is not empty %}
                                readonly
                            {% else %}
                                onfocus="handleInputOnFocus(this)"
                                onkeyup="handleInputOnKeyup(this)"
                            {% endif %}
                           {% if field.required %}required{% endif %}
                           {% if field.disabled %}disabled{% endif %}
                           {% for attrKey,attrValue in field.attributes %}{{ attrKey }}="{{ attrValue }}"{% endfor %}
                           placeholder="{{ lang('请输入') }}{{ field.label }}"
                           aria-label="{{ field.label }}"
                           value="">
                           
                    {# 输入框右侧下拉图标 #}
                    <i class="bi bi-chevron-down select-dropdown-icon" data-bs-toggle="dropdown"></i>
                           
                    {# 选中值容器 - 初始隐藏 #}
                    <div class="selected-value-container form-control" style="display: none;">
                        <span class="selected-text"></span>
                        <button type="button" class="btn-clear" onclick="clearSelectValue(this)">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>

                    {# 隐藏的实际表单值 - 数组形式 #}
                    <input type="text" class="form-control visually-hidden" {% if field.required %}required{% endif %}
                           name="{{ field.field }}[]" value=""
                           aria-label="{{ field.label }}">

                    <div class="invalid-feedback">
                        请选择{{ field.label }}
                    </div>

                    {# 下拉菜单 #}
                    <ul class="dropdown-menu">
                        {# 下拉菜单头部 - 关闭按钮 #}
                        <li class="dropdown-menu-header">
                            <span class="dropdown-menu-title">{{ lang('选择') }} {{ field.label }}</span>
                            <button type="button" class="btn-close-dropdown" onclick="closeDropdown(this)">
                                <i class="bi bi-x"></i>
                            </button>
                        </li>
                        
                        {% if field.options is not empty %}
                            {% for option in field.options %}
                                <li onclick="selectDropdownMenuItem(this,'{{ option.id }}','{{ option.text }}')">
                                    <a class="dropdown-item" href="javascript:">
                                        {{ option.text }}
                                    </a>
                                </li>
                            {% endfor %}
                        {% endif %}
                    </ul>
                </div>
                <button type="button" class="btn btn-primary rounded-0"
                        data-placeholder="{{ lang('请输入') }}{{ field.label }}"
                        data-label="{{ field.label }}"
                        data-required="{{ field.required }}"
                        data-disabled="{{ field.disabled }}"
                        data-field="{{ field.field }}"
                        data-url="{{ url('get-select-list') }}"
                        onclick="addSelectArrayItem(this)">
                    <i class="bi bi-plus"></i>
                </button>
            </div>
        {% endfor %}

    </div>
</div>
