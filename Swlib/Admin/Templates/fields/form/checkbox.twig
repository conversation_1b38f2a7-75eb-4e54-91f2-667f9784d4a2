<div class="row mb-3">
    <label
            class="col-sm-2 col-form-label text-end {% for class in field.classes %}{{ class }}{% endfor %}"
    >
        {{ field.label }}</label>
    <div class="col-sm-10 pt-2">

        {% for index,opt in field.options %}
            <div class="form-check form-check-inline">
                <input class="form-check-input"
                       name="{{ field.field }}[]"
                       {% if opt.checked %}checked{% endif %}
                       type="checkbox"
                       id="checkbox{{ form.elemId }}{{ index }}"
                {% for attrKey,attrValue in field.attributes %}{{ attrKey }}="{{ attrValue }}"{% endfor %}
                value="{{ opt.id }}"
                {% if opt.disabled %}disabled{% endif %}>
                <label class="form-check-label" for="checkbox{{ form.elemId }}{{ index }}">
                    {{ opt.text }}
                </label>
            </div>
        {% endfor %}

    </div>

</div>
