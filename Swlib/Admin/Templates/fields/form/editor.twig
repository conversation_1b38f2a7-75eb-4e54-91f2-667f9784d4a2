<div class="row mb-3">
    <label for="{{ field.elemId }}"
           class="col-sm-2 col-form-label text-end {% for class in field.classes %}{{ class }}{% endfor %}"
           {% for attrKey,attrValue in field.attributes %}data-{{ attrKey }}="{{ attrValue }}"{% endfor %}
    >
        {{ field.label }}
    </label>
    <div class="col-sm-10">

        <div class="editor—wrapper" data-id="{{ field.elemId }}" data-url="{{ field.url }}">
            <div class="toolbar-container"><!-- 工具栏 --></div>
            <div class="editor-container"><!-- 编辑器 --></div>
        </div>

        <textarea type="text" class="form-control visually-hidden"
                  id="{{ field.elemId }}"
                  name="{{ field.field }}"
                  {% if field.required %}required{% endif %} placeholder="{{ lang('请输入') }}{{ field.label }}"
        >{{ field.value }}</textarea>
        <div class="invalid-feedback">
            {{ lang('请输入') }}{{ field.label }}
        </div>
    </div>

</div>