<?php


use App\Model\CrawlerProduct;

use App\Model\PayModel;
use App\Service\AliPhonePrivacyService;
use App\Service\WxService;
use Generate\Tables\Datas\ClinicTransfersTable;
use Generate\Tables\Datas\HongBaoTable;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyProductsTable;
use Generate\Tables\Datas\OrderLogisticsTable;
use Generate\Tables\Datas\OrderRefundTable;
use Generate\Tables\Datas\PrivacyPhoneTable;
use Generate\Tables\Datas\ShopOrderTable;
use Generate\Tables\Datas\ZpJobsTable;
use GuzzleHttp\Client;
use Swlib\Connect\PoolMysql;
use Protobuf\Datas\ZpCompanies\ZpCompaniesAuthTypeEnum;
use Protobuf\Datas\ZpCompanies\ZpCompaniesStatusEnum;
use Protobuf\Datas\ZpJobsCollect\ZpJobsCollectTypeEnum;
use Protobuf\Datas\ZpLeave\ZpLeaveTargetTypeEnum;
use Swlib\Enum\CtxEnum;
use Swlib\Table\Db;
use Swlib\Table\Expression;
use Swoole\Coroutine;
use Yansongda\Pay\Pay;
use function Swoole\Coroutine\run;

require_once './vendor/autoload.php';

const ROOT_DIR = __DIR__ . DIRECTORY_SEPARATOR;
const RUNTIME_DIR = __DIR__ . DIRECTORY_SEPARATOR . 'runtime' . DIRECTORY_SEPARATOR;

run(function () {


    $refundPrice = new OrderRefundTable()->where([
        [OrderRefundTable::BUSINESS_ID, '=', 123],
    ])->setDebugSql()->sum(OrderRefundTable::REFUND_NUM, OrderRefundTable::REFUND_PRICE, '*');
    var_dump($refundPrice);
});

