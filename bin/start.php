<?php
declare(strict_types=1);
require_once "./vendor/autoload.php";

use Generate\ConfigEnum;
use Swlib\App;
use Swlib\Process\Process;
use Swlib\ServerEvents\OnCloseEvent;
use Swlib\ServerEvents\OnFinishEvent;
use Swlib\ServerEvents\OnMessageEvent;
use Swlib\ServerEvents\OnOpenEvent;
use Swlib\ServerEvents\OnPipeMessageEvent;
use Swlib\ServerEvents\OnReceiveEvent;
use Swlib\ServerEvents\OnRequestEvent;
use Swlib\ServerEvents\OnStartEvent;
use Swlib\ServerEvents\OnTaskEvent;
use Swlib\ServerEvents\OnWorkerStartEvent;
use Swlib\ServerEvents\OnWorkerStopEvent;
use Swoole\WebSocket\Server;

define('ROOT_DIR', dirname(__DIR__) . DIRECTORY_SEPARATOR);
const APP_DIR = ROOT_DIR . 'App' . DIRECTORY_SEPARATOR;
const PUBLIC_DIR = ROOT_DIR . 'public' . DIRECTORY_SEPARATOR;
const RUNTIME_DIR = ROOT_DIR . 'runtime' . DIRECTORY_SEPARATOR;


$app = new App();

try {
    $app->parse();

    $server = new Server(
        "0.0.0.0",
        ConfigEnum::PORT,
        SWOOLE_PROCESS
    );


    $config = [
        'hook_flags' => SWOOLE_HOOK_ALL,
        'daemonize' => ConfigEnum::APP_PROD, // 设为true则以守护进程方式运行
        'worker_num' => ConfigEnum::WORKER_NUM,
        'task_worker_num' => ConfigEnum::TASK_WORKER_NUM,
        'task_enable_coroutine' => true,
        'task_max_request' => 1024,
        'upload_max_filesize' => 100 * 1024 * 1024,
        'heartbeat_idle_time' => 600, // 表示一个连接如果600秒内未向服务器发送任何数据，此连接将被强制关闭
        'heartbeat_check_interval' => 60,  // 表示每60秒遍历一次
        'enable_coroutine' => true,
        'max_request' => 1024,
        'dispatch_mode' => 4,// 根据IP 分配 worker进程
        'max_wait_time' => 10,
        'reload_async' => true,// 平滑重启
        'log_file' => RUNTIME_DIR . '/log/server_error.log'
    ];

    if (ConfigEnum::APP_PROD === false) {
        // 此功能较为简易，请勿在公网环境直接使用
        $config['document_root'] = PUBLIC_DIR;// v4.4.0以下版本, 此处必须为绝对路径
        // 开启静态文件请求处理功能，需配合 document_root 使用 默认 false
        $config['enable_static_handler'] = true;
    }


    $config = $app->generateDevSSL($config);
    $server->set($config);

    // 绑定服务器事件，如果需要扩展，可以在此进行绑定
    $server->on('receive', [new OnReceiveEvent(), 'handle']);
    $server->on('start', [new OnStartEvent(), 'handle']);
    $server->on('workerStart', [new OnWorkerStartEvent(), 'handle']);
    $server->on('workerStop', [new OnWorkerStopEvent(), 'handle']);
    $server->on('pipeMessage', [new OnPipeMessageEvent(), 'handle']);
    $server->on('open', [new OnOpenEvent(), 'handle']);
    $server->on('message', [new OnMessageEvent(), 'handle']);
    $server->on('close', [new OnCloseEvent(), 'handle']);
    $server->on('request', [new OnRequestEvent($server), 'handle']);
    $server->on('task', [new OnTaskEvent(), 'handle']);
    $server->on('finish', [new OnFinishEvent(), 'handle']);


    // 添加自定义进程
    Process::run($server);

    $port = ConfigEnum::PORT;

    echo "Swoole http server is started at http://127.0.0.1:$port" . PHP_EOL;

    $server->start();
} catch (Exception $e) {
    var_dump($e->getMessage());
    var_dump($e->getTraceAsString());
}