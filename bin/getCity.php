<?php
declare(strict_types=1);


use Generate\Tables\CitysTable;
use Overtrue\Pinyin\Pinyin;
use function Swoole\Coroutine\run;

require_once dirname(__DIR__) . "/Swlib/App.php";


/**
 *
 * composer require overtrue/pinyin
 *
 * CREATE TABLE `citys` (
 * `id` int unsigned NOT NULL AUTO_INCREMENT,
 * `code` int DEFAULT NULL COMMENT '城市编码',
 * `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '城市名称',
 * `letter` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拼音首字母',
 * PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB AUTO_INCREMENT=3210 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='城市列表';
 *
 *
 */


run(function () {
    $html = file_get_contents("https://www.mca.gov.cn/mzsj/xzqh/2023/202301xzqh.html");


    // 创建 DOMDocument 实例
    $dom = new DOMDocument();

    // 抑制警告和错误
    libxml_use_internal_errors(true);
    $dom->loadHTML($html);
    libxml_clear_errors();

    // 创建 DOMXPath 实例
    $xpath = new DOMXPath($dom);

    // 使用 XPath 查询提取表格中的所有行
    $rows = $xpath->query('//table//tr');


    // 初始化拼音对象
    $pinyin = new Pinyin();

    foreach ($rows as $row) {
        $columns = $xpath->query('td', $row);

        if ($columns->length >= 2) {
            $firstColumn = $columns->item(1)?->nodeValue;
            $secondColumn = $columns->item(2)?->nodeValue;
            if (empty($firstColumn) || !is_numeric($firstColumn) || empty($secondColumn)) continue;
            // 去除左右两边所有不可见字符
            $firstColumn = preg_replace('/^\p{Z}+|\p{Z}+$/u', '', $firstColumn);
            $secondColumn = preg_replace('/^\p{Z}+|\p{Z}+$/u', '', $secondColumn);
            $secondColumn = rtrim($secondColumn, '*');

            echo "First Column: " . $firstColumn . ", Second Column: " . $secondColumn . PHP_EOL;
            $letter = $pinyin->abbr(mb_substr($secondColumn, 0, 1));
            $letter = strtoupper((string)$letter);

            $id = new CitysTable()->where([
                [CitysTable::CODE, '=', $firstColumn]
            ])->selectField(CitysTable::ID);
            if (empty($id)) {
                new CitysTable()->insert([
                    CitysTable::CODE => $firstColumn,
                    CitysTable::NAME => $secondColumn,
                    CitysTable::LETTER => $letter,
                ]);
            } else {
                new CitysTable()->where([
                    [CitysTable::ID, '=', $id]
                ])->update([
                    CitysTable::NAME => $secondColumn,
                    CitysTable::LETTER => $letter,
                ]);
            }

        }
    }


});
