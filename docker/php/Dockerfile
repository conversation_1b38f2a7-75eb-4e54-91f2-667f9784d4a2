FROM php:8.4.5-cli-bookworm
COPY --from=composer /usr/bin/composer /usr/bin/composer

# 换源,使用了代理的情况下,不需要
#RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources
#RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources


# 支持的 docker 镜像有：

#基于 Debian 的 docker 镜像：从 jessie (Debian 8) 开始（最低 PHP 版本：5.5）
#基于 Alpine 的 docker 镜像：自 Alpine 3.9 起（最低 PHP 版本：7.1）
# swoole 的 io_uring 需要这个库 liburing-dev
# 安装必要的软件包和 PHP 扩展
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    liburing-dev git psmisc protobuf-compiler && \
    curl -L https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions -o /usr/local/bin/install-php-extensions && \
    chmod +x /usr/local/bin/install-php-extensions && \
    install-php-extensions @fix_letsencrypt json pcntl openssl pdo pdo_mysql mysqli redis protobuf swoole-^6@stable bcmath sockets xlswriter zip && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*


# 设置工作目录
WORKDIR /var/www/html

EXPOSE 9501