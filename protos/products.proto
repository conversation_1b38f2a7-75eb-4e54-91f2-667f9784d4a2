syntax = "proto3";

//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Product;
option php_metadata_namespace = "GPBMetadata\\Common";
// 商品列表请求类
message ShopProductRequest{
  int32 page = 1;
  int32 size = 2;
  string sort = 3;
  string sortField = 4;
  string keyword = 5;
  int32 categoryId = 6;
  int32 brandId = 7;
  bool recommendedForYou = 8 ; // 查询为你推荐
  bool userId = 9 ;
}

message ShopSourcePrice{
  int32 sourceId = 1;
  string sourceName = 2;
  double price = 3; // 价格
  double marketPrice = 4; // 市场价格
  string icon = 5; // 市场价格
  string url = 6; // url
  string picture = 7 ; // 图片
}

message ShopProductImageItem{
  string url = 1;
}


message ShopProductSku{
  // 一般通过 code 和 productId 可以定位到来源网站的产品
  string code = 1; // 来源网站的 code
  string productId = 2; // 来源网站的 productId
  string url = 3; // 来源网站的 url ,
  string name = 4; // 名称
  double price = 5; // 价格
  double marketPrice = 6; // 市场价格
  repeated ShopSourcePrice sourcePrices = 7; // 来源价格
  string unit = 8; // 单位
  string picture = 9 ; // 图片
  int32 id = 10 ;
  int32 carId = 11 ;
}

message UpdateCarSku{
  int32 skuId = 1;
  int32 userId = 2;
  int32 productId = 3;
  int32 carId = 4;
}

message ShopProductItem{
  string picture = 1 ; // 图片
  string name = 2 ; //名称
  string code = 3 ; // 产品编码
  int32 saleNum = 4; // 销量
  double price = 5; // 价格
  double marketPrice = 6; // 市场价格
  string brand = 7; // 品牌
  string validUntil = 8; // 有效期
  string approvalNum = 9;// 批准文号,注册证号
  repeated ShopProductSku skus = 10; //sku
  repeated ShopSourcePrice sourcePrices = 11; // 来源价格
  int32 businessId = 12;
  string businessName = 13;
  repeated ShopProductImageItem images = 14;
  string unit = 15;
  int32 id = 16;
  // 商品数量，一般用于购物车，添加了多少个商品
  int32 num = 17;
}

message ShopProductLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated ShopProductItem lists = 2;
}

// 搜索历史
message HistorySearchItem{
  string keyword = 1 ;
  int32 num = 2 ;
  int32 id = 3 ;
}
// 搜索历史列表
message HistorySearchLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated HistorySearchItem lists = 2;
}

// 我的常买
message ShopProductByCountItem{
  int32 count = 1 ; // 购买次数
  double price = 2; // 订单金额
  double originalPrice = 3 ;// 原始价格
  string businessName = 4; // 商家名称
  string productName = 5; // 产品名称
  int32 skuId = 6; // 规格名称
  double skuPrice = 7; // 规格名称
  string skuName = 8; // 规格名称
  string picture = 9; // 商品图片
}
// 我的常买
message ShopProductByCountLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated ShopProductByCountItem lists = 2;
}
