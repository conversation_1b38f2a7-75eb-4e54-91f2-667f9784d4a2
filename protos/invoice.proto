syntax = "proto3";


//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Invoice;
option php_metadata_namespace = "GPBMetadata\\Common";
/**
 * 发票信息
 */


// 发票类型
message InvoiceType{
  int32 id = 1;
  string name = 2;
}

message InvoiceTypes{
  int32 total = 1;
  int32 totalPage = 3;
  repeated InvoiceType lists = 2;
}

message InvoiceItem{
  int32 id = 1;
  string email = 2; //接收邮箱
  InvoiceType type = 3; // 发票类型，1-增值税发票，2-增值税专用发票，3-增值税普通发票，4-增值税电子普通发票，5-增值税电子专用发票，6-增值税普通发票（卷票），7-增值税专用发票（卷票），8-增值税电子普通发票（通行费），9-增值税电子专用发票（通行费）
  string header = 4; //发票抬头
  string num = 5;//纳税人识别号
  string time = 6; // 开票时间
  string notes = 7; // 备注
  string userId = 8; // 用户ID
  double price = 9; // 开票金额
  string pic = 10; // 发票图片地址
}

message InvoiceLists{
  repeated InvoiceItem lists = 1;
}

message InvoiceRequest{
  double price = 1;
  int32 businessId = 2;
  string email = 3; //接收邮箱
  // 发票类型，1-增值税发票，2-增值税专用发票，3-增值税普通发票，4-增值税电子普通发票，5-增值税电子专用发票，6-增值税普通发票（卷票），7-增值税专用发票（卷票），8-增值税电子普通发票（通行费），9-增值税电子专用发票（通行费）
  int32 typeId = 4;
  string header = 5; //发票抬头
  string num = 6;//纳税人识别号
  string notes = 7; // 备注
  int32 userId = 8; // 用户ID
}



