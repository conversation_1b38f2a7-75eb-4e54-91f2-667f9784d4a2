syntax = "proto3";


//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Order;
option php_metadata_namespace = "GPBMetadata\\Common";

message OrderRequest {
  int32 page = 1;
  int32 size = 2;
  string keyword = 3;// 搜索关键字，商品，或者商家
  int32 userId = 4;
  int32 addrId = 5;
  OrderStatusEnum status = 6; // 订单状态
  string sn = 7; // 订单号
  repeated int32 ids = 8; // 订单号
  int32 skuId = 9; // 订单号
  bool hasInvoice = 10; // 是否已经开了发票
  bool byBusiness = 11; // 是否按照商家分组
}

message PayResponse{
  string orderInfo = 1;
}

enum OrderStatusEnum{
  UNKNOWN = 0;
  WAIT_PAY = 1; //待付款
  WAIT_DELIVERY = 2; //待发货
  WAIT_RECEIVE = 3;//待收货
  REFUND = 4;//退换货
  CANCEL = 5;//已取消
  TIMEOUT = 6;//已超时
  REFUND_FINISH = 7;//退货完成
  FINISH = 8;//订单完成
}

message OrderStatusLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated OrderStatusEnum lists = 2;
}


message ShopOrderItem{
  int32 id = 1;
  string sn = 2; // 订单号
  double price = 3; // 订单金额
  int32 userId = 4; // 用户ID
  int32 businessId = 5; //商家ID
  int32 productId = 6; //产品ID
  int32 skuId = 13; //SKU ID
  string time = 7;// 下单时间
  double originalPrice = 8 ;// 原始价格
  string businessName = 9; // 商家名称
  string productName = 10; // 产品名称
  string skuName = 11; // 规格名称
  string picture = 12; // 商品图片
  OrderItem order = 14;// 订单数据
}

message OrderItem{
  int32 id = 1;
  string sn = 2; // 订单号
  double originalPrice = 3 ;// 原始价格
  double price = 4; // 订单金额
  int32 invoiceId = 5; // 发票ID 是否已经开票了，开票了就有这个ID
  int32 userId = 6; // 用户ID
  string time = 7;// 下单时间
  OrderStatusEnum status = 8; // 订单状态
  string refundTime = 9; // 退款时间
  string refundHandleTime = 10; // 退款处理时间
  string completeTime = 11; // 订单完成时间，确认收货时间
  repeated ShopOrderItem lists = 12;
  bool isComment = 13;// 是否评论过了
  string payNo = 15;// 支付流水
  string payTime = 16; // 支付时间
  string payType = 17; // 支付方式
}

message OrderByBusinessItem{
  string businessName = 1; // 商家名称
  string businessId = 2; // 商家名称
  double orderTotalPrice = 3; // 订单总金额
  repeated ShopOrderItem lists = 4; // 订单数据
}


message OrderLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated OrderItem lists = 3; // 根据订单号分组
  repeated OrderByBusinessItem businessLists = 4; // 根据商家分组
}



