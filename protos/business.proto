syntax = "proto3";
import "products.proto";

//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Business;
option php_metadata_namespace = "GPBMetadata\\Common";
message BusinessCategory{
  int32 id = 1;
  string name = 2;
  int32 level = 3;
  int32 parentId = 4;
}

message BusinessCategoryLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated BusinessCategory lists = 2;
}

message BusinessItem{
  int32 id = 1;
  string businessName = 2;
  string username = 3;
  string phone = 4;
  string addr = 5;
  string info = 6;
  string license = 7;
  string otherLicense = 8;
  bool isAuth = 9;
  string createTime = 10;
  string licenseTime = 11;
  BusinessCategory category = 12;
  repeated Protobuf.Product.ShopProductItem productLists = 13;
  string logo = 14;
}

message BusinessLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated BusinessItem lists = 2;
}

message BusinessListRequest {
  int32 page = 1;
  int32 size = 2;
  string keyword = 3;
}
