syntax = "proto3";


//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Coupon;
option php_metadata_namespace = "GPBMetadata\\Common";


message CouponItem{
  int32 id = 1;
  string title = 2; // 标题
  string subTitle = 3; // 副标题
  double amount = 4; // 优惠金额
  double conditionAmount = 5; // 使用门槛
  int32 startTime = 6; // 开使用时间
  int32 endTime = 7; // 过期时间
  string notes = 8; // 备注
  int32 userId = 9;
  bool hasUse = 10; // 是否使用
}

message CouponLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated CouponItem lists = 2;
}
