{"int32 id": 1, "string name": 2, "string code": 3, "int32 brandId": 4, "int32 businessId": 5, "int32 categoryId": 6, "float price": 7, "float marketPrice": 8, "string picture": 9, "string unit": 10, "string subName": 11, "string content": 12, "int32 saleNum": 13, "string medicalInsuranceCode": 14, "string lastUpdateTimeStr": 15, "int32 lastUpdateTime": 16, "string productionEnterprises": 17, "string registrationCertificatePic": 18, "string dateOfManufacture": 19, "string validUntil": 20, "string approvalNumber": 21, "int32 isDisabled": 22, "int32 queryPageNo": 23, "int32 queryPageSize": 24, "string querySortField": 25, "string querySortType": 26, "int32 queryCount": 27, "int32 extInt": 28, "string extStr": 29}