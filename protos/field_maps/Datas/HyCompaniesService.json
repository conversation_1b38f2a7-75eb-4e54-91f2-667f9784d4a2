{"int32 id": 1, "int32 hyCompanies": 2, "HyCompaniesServiceTypeEnum type": 3, "string contactPerson": 4, "string contactPhone": 5, "int32 serviceArea": 6, "string description": 7, "string images": 8, "int32 isPub": 9, "int32 views": 10, "int32 favoriteCount": 11, "string createdAt": 12, "string updatedAt": 13, "int32 queryPageNo": 14, "int32 queryPageSize": 15, "string querySortField": 16, "string querySortType": 17, "int32 queryCount": 18, "int32 extInt": 19, "string extStr": 20, "int32 companiesId": 21, "Protobuf.Datas.HyCompanies.HyCompaniesProto companies": 22, "int32 userId": 23, "string serviceArea": 24, "string expiresAt": 25, "int32 fundingNeed": 26, "string companiesType": 27, "string productName": 28, "string serviceAddress": 29, "string startAt": 30, "string productCategory": 31, "string productType": 32, "int32 productSize": 33, "int32 productNum": 34, "int32 isTop": 35, "int32 isBanner": 36, "string bannerImg": 37, "int32 displaySort": 38, "float price": 39, "string servicePerson": 40, "string serviceDesc": 41, "string serviceImages": 42, "string productSku": 43, "string productNo": 44, "string yiBao": 45, "string serviceAreaIds": 46, "float priceMax": 47, "string imagesDesc": 48, "string excludeTypes": 49, "int32 companiesType": 50, "string unit": 51, "int32 isSale": 52, "Protobuf.Datas.HyProductsSku.HyProductsSkuProto skus": 53, "repeated Protobuf.Datas.HyProductsSku.HyProductsSkuProto skus": 54, "int32 commentNum": 55}