{"int32 id": 1, "string name": 2, "string code": 3, "string smmm": 4, "string slc": 5, "string syy": 6, "string ssb": 7, "string seky": 8, "string sjws": 9, "string syyb": 10, "int32 brandId": 11, "float price": 12, "float marketPrice": 13, "string picture": 14, "string unit": 15, "string subName": 16, "string content": 17, "int32 businessId": 18, "int32 showShop": 19, "int32 deliveryTime": 20, "int32 saleNum": 21, "int32 hotSort": 22, "string medicalInsuranceCode": 23, "string lastUpdateTimeStr": 24, "int32 lastUpdateTime": 25, "string productionEnterprises": 26, "string registrationCertificatePic": 27, "string dateOfManufacture": 28, "string validUntil": 29, "string approvalNumber": 30, "int32 categoryId": 31, "int32 queryPageNo": 32, "int32 queryPageSize": 33, "string querySortField": 34, "string querySortType": 35, "string brandName": 36, "string categoryName": 37, "int32 queryCount": 38, "int32 extInt": 39, "string extStr": 40}