{"int32 id": 1, "int32 businessId": 2, "string sn": 3, "int32 orderId": 4, "int32 productId": 5, "int32 skuId": 6, "int32 userId": 7, "string createdTime": 8, "string updatedTime": 9, "int32 shorOrderId": 10, "int32 refundNum": 11, "int32 refundOnly": 12, "string refundTimeStr": 13, "int32 refundTime": 14, "string refundMsg": 15, "string handleRefundTimeStr": 16, "int32 handleRefundTime": 17, "int32 handleRefundStatus": 18, "string handleRefundMsg": 19, "float handleRefundPrice": 20, "int32 queryPageNo": 21, "int32 queryPageSize": 22, "string querySortField": 23, "string querySortType": 24, "int32 queryCount": 25, "int32 extInt": 26, "string extStr": 27, "repeated Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto companiesService": 28, "string handleRefundTime": 29, "repeated Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto companiesServices": 30, "Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto companiesService": 31, "Protobuf.Datas.HyCompaniesm.HyCompaniesProto companies": 32, "Protobuf.Datas.HyProductsSku.HyProductsSkuProto sku": 33, "Protobuf.Datas.HyCompanies.HyCompaniesProto companies": 34, "float refundPrice": 35}