{"int32 id": 1, "int32 userId": 2, "int32 currentLevelId": 3, "float depositPaid": 4, "float depositUsed": 5, "float availableDeposit": 6, "int32 authStatus": 7, "string authStartTime": 8, "string authEndTime": 9, "string createdAt": 10, "string updatedAt": 11, "int32 queryPageNo": 12, "int32 queryPageSize": 13, "string querySortField": 14, "string querySortType": 15, "int32 queryCount": 16, "int32 extInt": 17, "string extStr": 18, "Protobuf.Datas.AuthLevels.AuthLevelsProto authLevel": 19}