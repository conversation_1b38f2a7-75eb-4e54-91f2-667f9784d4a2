{"int32 id": 1, "int32 businessId": 2, "string sn": 3, "int32 orderId": 4, "int32 productId": 5, "int32 skuId": 6, "int32 userId": 7, "string news": 8, "string logisticsNumber": 9, "string createdTime": 10, "string updatedTime": 11, "int32 queryPageNo": 12, "int32 queryPageSize": 13, "string querySortField": 14, "string querySortType": 15, "int32 queryCount": 16, "int32 extInt": 17, "string extStr": 18, "int32 number": 19, "int32 shorOrderId": 20, "int32 num": 21, "repeated Protobuf.Datas.ShopOrder.ShopOrderProto shops": 22, "Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto companiesService": 23, "repeated Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto companiesService": 24, "int32 refundNum": 25, "int32 isRefundOnly": 26, "int32 refundOnly": 27, "string refundTimeStr": 28, "int32 refundTime": 29, "string refundMsg": 30, "string handleRefundTimeStr": 31, "int32 handleRefundTime": 32, "int32 handleRefundStatus": 33, "string handleRefundMsg": 34, "float handleRefundPrice": 35, "int32 confirmReceipt": 36, "string confirmReceiptTime": 37, "string lastTimeStr": 38, "int32 lastTime": 39, "string lastDetail": 40, "string status": 41}