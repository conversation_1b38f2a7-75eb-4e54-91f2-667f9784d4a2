{"int32 id": 1, "string sn": 2, "float originalPrice": 3, "float price": 4, "int32 userId": 5, "string timeStr": 6, "int32 time": 7, "int32 orderStatus": 8, "string refundTimeStr": 9, "int32 refundTime": 10, "float refundAmount": 11, "string refundHandleTimeStr": 12, "int32 refundHandleTime": 13, "string refundCompleteTimeStr": 14, "int32 refundCompleteTime": 15, "string refundNo": 16, "int32 refundStatus": 17, "string refundMsg": 18, "string completeTimeStr": 19, "int32 completeTime": 20, "string mark": 21, "int32 payType": 22, "string payTimeStr": 23, "int32 payTime": 24, "string payNo": 25, "int32 addrId": 26, "int32 queryPageNo": 27, "int32 queryPageSize": 28, "string querySortField": 29, "string querySortType": 30, "int32 queryCount": 31, "int32 extInt": 32, "string extStr": 33, "string notes": 34, "int32 status": 35, "OrderStatusEnum status": 36, "repeated Protobuf.Datas.ShopOrder.ShopOrderProto orders": 37, "repeated Protobuf.Datas.ShopOrder.ShopOrderProto shops": 38, "int32 businessId": 39, "Protobuf.Datas.Address.AddressProto address": 40}