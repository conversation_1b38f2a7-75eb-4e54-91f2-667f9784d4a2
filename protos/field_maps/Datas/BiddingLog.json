{"int32 id": 1, "int32 biddingShopId": 2, "string desc": 3, "float price": 4, "string timeStr": 5, "int32 time": 6, "int32 userId": 7, "string contactName": 8, "string contactPhone": 9, "int32 queryPageNo": 10, "int32 queryPageSize": 11, "string querySortField": 12, "string querySortType": 13, "string companiesName": 14, "string companiesBusinessLicense": 15, "string companiesPermit": 16, "int32 queryCount": 17, "int32 extInt": 18, "string extStr": 19, "string images": 20, "Protobuf.Datas.BiddingShop.BiddingShopProto shop": 21, "Protobuf.Datas.BiddingShop.HyCompaniesProto companies": 22, "Protobuf.Datas.HyCompanies.HyCompaniesProto companies": 23, "Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto shop": 24}