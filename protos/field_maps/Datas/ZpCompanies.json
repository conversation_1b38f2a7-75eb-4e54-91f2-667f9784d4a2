{"int32 id": 1, "string name": 2, "string province": 3, "string city": 4, "string location": 5, "string description": 6, "string url": 7, "string logo": 8, "repeated string sloganWelfare": 9, "string timeStr": 10, "int32 time": 11, "repeated string environmentPics": 12, "string permit": 13, "string businessLicense": 14, "ZpCompaniesStatusEnum status": 15, "string msg": 16, "int32 userId": 17, "string contactsName": 18, "string contactsPhone": 19, "string expireTimeStr": 20, "int32 expireTime": 21, "ZpCompaniesAuthTypeEnum authType": 22, "int32 queryPageNo": 23, "int32 queryPageSize": 24, "bool isFocus": 25, "int32 focusCount": 26, "string querySortField": 27, "string querySortType": 28, "int32 queryCount": 29, "int32 extInt": 30, "string extStr": 31}