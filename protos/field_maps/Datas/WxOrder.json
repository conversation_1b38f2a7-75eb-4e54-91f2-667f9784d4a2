{"int32 id": 1, "int32 engineerId": 2, "int32 serviceId": 3, "string timeStr": 4, "int32 time": 5, "WxOrderTypeEnum type": 6, "int32 queryPageNo": 7, "int32 queryPageSize": 8, "string querySortField": 9, "string querySortType": 10, "int32 queryCount": 11, "int32 extInt": 12, "string extStr": 13, "Protobuf.Datas.WxEngineer.WxEngineerProto engineer": 14, "Protobuf.Datas.WxService.WxServiceProto service": 15, "Protobuf.Datas.WxOrder.WxOrderTypeEnum type": 16}