{"int32 id": 1, "int32 num": 2, "int32 userId": 3, "int32 productId": 4, "int32 skuId": 5, "int32 businessId": 6, "int32 queryPageNo": 7, "int32 queryPageSize": 8, "string querySortField": 9, "string querySortType": 10, "int32 queryCount": 11, "int32 extInt": 12, "string extStr": 13, "Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto companiesService": 14, "Protobuf.Datas.HyCompanies.HyCompaniesProto companies": 15, "Protobuf.Datas.HyProductsSku.HyProductsSkuProto sku": 16}