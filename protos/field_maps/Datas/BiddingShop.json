{"int32 id": 1, "string name": 2, "float budgetMin": 3, "float budgetMax": 4, "string ask": 5, "string contactName": 6, "string contactPhone": 7, "string timeStr": 8, "int32 time": 9, "int32 biddingCount": 10, "float biddingMin": 11, "float biddingMax": 12, "float biddingAvg": 13, "string biddingName": 14, "BiddingShopStatusEnum status": 15, "int32 userId": 16, "string addrStr": 17, "int32 addr": 18, "int32 queryPageNo": 19, "int32 queryPageSize": 20, "string querySortField": 21, "string querySortType": 22, "int32 queryCount": 23, "int32 extInt": 24, "string extStr": 25}