{"int32 id": 1, "int32 productId": 2, "string name": 3, "string description": 4, "int32 queryPageNo": 5, "int32 queryPageSize": 6, "string querySortField": 7, "string querySortType": 8, "int32 queryCount": 9, "int32 extInt": 10, "string extStr": 11, "string image": 12, "float price": 13, "int32 minBuy": 14, "int32 inventory": 15, "int32 buyNum": 16, "float buyPrice": 17, "repeated Protobuf.Datas.OrderLogistics.OrderLogisticsProto logistics": 18, "string code": 19, "int32 isDicker": 20, "float cost": 21, "int32 view": 22, "int32 click": 23, "int32 sale": 24, "float grossProfit": 25, "float grossProfitMargin": 26, "float conversionRate": 27, "string productName": 28, "float salesVolume": 29}