{"int32 id": 1, "string name": 2, "string logo": 3, "string phone": 4, "string mainProducts": 5, "string introduction": 6, "string address": 7, "string bannerImage": 8, "string timeStr": 9, "int32 time": 10, "int32 userId": 11, "HyCompaniesStatusEnum status": 12, "int32 queryPageNo": 13, "int32 queryPageSize": 14, "string querySortField": 15, "string querySortType": 16, "string qualifications": 17, "int32 queryCount": 18, "int32 extInt": 19, "string extStr": 20, "string businessLicense": 21, "string permit": 22, "string medicinePermit": 23, "string contactPerson": 24, "int32 isDrug": 25, "repeated Protobuf.Datas.DrugDisplays.DrugDisplaysProto drugs": 26, "int32 isCustom": 27, "repeated Protobuf.Datas.CustomProducts.CustomProductsProto customProducts": 28, "int32 type": 29, "int32 authType": 30, "int32 saleNum": 31, "int32 isSpecialInvitation": 32, "Protobuf.Datas.AuthUserInfo.AuthUserInfoProto auth": 33, "float availableBalance": 34, "float lockedBalance": 35, "string bankName": 36, "string bankAccount": 37, "string bankOpening": 38, "string alipayAccount": 39, "string wechatAccount": 40}