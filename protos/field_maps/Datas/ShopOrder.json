{"int32 id": 1, "int32 userId": 2, "string sn": 3, "float price": 4, "int32 num": 5, "int32 businessId": 6, "int32 productId": 7, "int32 skuId": 8, "string timeStr": 9, "int32 time": 10, "int32 orderId": 11, "int32 invoiceId": 12, "int32 queryPageNo": 13, "int32 queryPageSize": 14, "string querySortField": 15, "string querySortType": 16, "int32 queryCount": 17, "int32 extInt": 18, "string extStr": 19, "string productTable": 20, "ompaniesService.Datas.HyCompaniesService.HyCompaniesServiceProto companiesService": 21, "Protobuf.Datas.HyCompaniesService.HyCompaniesServiceProto companiesService": 22, "Protobuf.Datas.HyProductsSku.HyProductsSkuProto sku": 23, "string productName": 24, "string skuName": 25, "repeated Protobuf.Datas.HyProductsSku.HyProductsSkuProto skus": 26, "Protobuf.Datas.HyCompanies.HyCompaniesProto companies": 27, "Protobuf.Datas.OrderLogistics.OrderLogisticsProto logistics": 28, "repeated Protobuf.Datas.OrderLogistics.OrderLogisticsProto logistics": 29, "int32 quantityShipped": 30, "int32 returnedQuantity": 31, "int32 completedQuantity": 32}