{"int32 id": 1, "string code": 2, "string name": 3, "float marketPrice": 4, "float price": 5, "string approvalNumber": 6, "string productId": 7, "string medicalInsuranceCode": 8, "string content": 9, "string picture": 10, "int32 sourceId": 11, "string unit": 12, "string lastUpdateTimeStr": 13, "int32 lastUpdateTime": 14, "int32 brandId": 15, "string productionEnterprises": 16, "string registrationCertificatePic": 17, "string dateOfManufacture": 18, "string validUntil": 19, "string url": 20, "int32 queryPageNo": 21, "int32 queryPageSize": 22, "string querySortField": 23, "string querySortType": 24, "int32 queryCount": 25, "int32 extInt": 26, "string extStr": 27}