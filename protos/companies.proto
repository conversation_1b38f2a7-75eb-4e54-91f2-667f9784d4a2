syntax = "proto3";
import "Datas/User.proto";

//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Companies;
option php_metadata_namespace = "GPBMetadata\\Common";
// 企业信息
message CompaniesItem{
  int32 id = 1;
  string name = 2;
  string province = 3;
  string city = 4;
  string location = 5;
  string description = 6;
  string url = 7;
  string logo = 8;
  repeated string sloganWelfare = 9;
  string createdAt = 10;
  int32 jobsCount = 11;
  repeated string pics = 12;
  int32 focusCount = 13;
  bool isFocus = 14;
}
// 企业列表
message CompaniesLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated CompaniesItem lists = 2;
}

// 关注信息
message CompaniesFocusItem{
  int32 id = 1;
  int32 userId = 2;
  int32 companyId = 3;
  Protobuf.Datas.User.UserProto user = 4;
}

// 关注列表
message CompaniesFocusLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated CompaniesFocusItem lists = 2;
}

// 企业办公环境
message CompaniesOfficeEnvItem{
  int32 id = 1;
  int32 companyId = 2;
  string url = 3;
}

// 企业办公环境列表
message CompaniesOfficeEnvLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated CompaniesOfficeEnvItem lists = 2;
}


// 福利
message WelfareItem{
  int32 id = 1;
  string name = 2;
}

// 福利树
message WelfareTree{
  WelfareItem me = 1;
  WelfareItem parent = 2;
  repeated WelfareItem children = 3;
}
// 福利树列表
message WelfareTreeLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated WelfareTree lists = 3;
}


// 企业福利
message CompaniesWelfareItem{
  int32 id = 1;
  int32 companyId = 2;
  repeated WelfareTreeLists lists = 3;
}

// 企业福利列表
message CompaniesWelfareLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated CompaniesWelfareItem lists = 2;
}