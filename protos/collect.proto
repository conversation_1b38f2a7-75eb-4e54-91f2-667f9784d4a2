syntax = "proto3";


//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Collect;
option php_metadata_namespace = "GPBMetadata\\Common";


message CollectItem{
  int32 id = 1;
  string time = 2;
  int32 userId = 3;
  int32 productId = 4;
  string productName = 5;
  string productPicture = 6;
  string productPrice = 7;
  int32 skuId = 8;
  string skuName = 9;
  int32 businessId = 10;
  string businessName = 11;
  string num = 12; // 产品收藏人数
}

message CollectLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated CollectItem lists = 2;
}
