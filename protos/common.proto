syntax = "proto3";


//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Common;
option php_metadata_namespace = "GPBMetadata\\Common";

message Request {
  int32 page = 1;
  int32 size = 2;
  string sort = 3;
  string sortField = 4;
  int32 userId = 5;
  int32 id = 6;
  int32 type = 7;
  string datetime = 8;
  int32 num = 9;
  repeated int32 ids = 10;
}


message Id{
  int32 id = 1;
}

message Response {
  int32 errno = 1; // 是否有错误 1 有错误 给用户提示 msg ; 0 表示没有错误 用户读取 data;
  bytes data = 2; // 没有错误就有数据
  string msg = 3; // 有错误就有错误信息
  int32 path = 4; // websocket 的情况下,前台是用那个路由处理本次返回
}

message Router {
  int32 errno = 1; // 是否有错误 1 有错误 给用户提示 msg ; 0 表示没有错误 用户读取 data;
  bytes data = 2; // 没有错误就有数据
  string msg = 3; // 有错误就有错误信息
  string path = 4; // 是用那个路由处理本次返回
}

message Success {
  bool success = 1;
  string status=2;
}

// 国家信息
message CountryItem{
  int32 id = 1;
  string countryCode = 2;
  string name = 3;
  string phonePrefix = 4;
  string iconPath = 5;
}
// 国家信息列表
message CountryLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated CountryItem lists = 3;
}

message Article{
  int32 id = 1;
  string title = 2;
  string content = 3;
}

message Language{
  string key = 1;
  string value = 2;
  string replace = 3;
}

message LanguageLists{
  repeated Language lists = 1;
}