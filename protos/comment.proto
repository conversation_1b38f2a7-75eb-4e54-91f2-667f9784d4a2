syntax = "proto3";


//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

// 评论
package Protobuf.Comment;
option php_metadata_namespace = "GPBMetadata\\Common";
message CommentTag{
  int32 id = 1;
  string name = 2;
}

message CommentTagLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated CommentTag lists = 2;
}

message CommentImage{
  string url = 1;
}

message CommentItem{
  string comment = 1; // 评论的具体内容
  repeated CommentImage images = 2; // 评论的图片列表
  bool anonymous = 3 ; // 是否匿名
  int32 descNum = 4; // 描述符合评分1-5分
  int32 compositeNum = 5; // 综合评分1-5分
  int32 logisticsNum = 6 ; // 物流评分1-5分
  int32 customerServiceNum = 7;// 客服评分1-5分
  CommentTag tag = 8;// 评论类型 1：整体评价 2：性价比 3：产品描述
  int32 productId = 9 ;// 产品ID
  string productName = 10 ;// 产品ID
  int32 skuId = 11 ;// 规格ID
  string skuName = 12 ;// 规格ID
  int32 orderId = 13 ;// 订单ID
  int32 userId = 14 ;// 用户ID
  int32 id = 15 ;// 评论ID
  string time = 16 ;// 评论ID
}

message CommentLists{
  int32 total = 1 ;
  int32 totalPage = 3;
  repeated CommentItem lists = 2;
}
