syntax = "proto3";


//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Footprint;
option php_metadata_namespace = "GPBMetadata\\Common";



// 我的足迹
message MyFootprintItem{
  int32 userId = 1;
  int32 productId = 2;
  string picture = 12; // 商品图片
}

// 足迹列表,根据每个日期统计
message MyFootprintItemsByDay{
  int32 total = 1;
  int32 totalPage = 4;
  repeated MyFootprintItem lists = 2;
  string theDay = 3; // 日期
}

// 足迹列表
message MyFootprintLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated MyFootprintItemsByDay lists = 2;
}