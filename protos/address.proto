syntax = "proto3";

//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Address;

option php_metadata_namespace = "GPBMetadata\\Common";

message AddressItem{
  string username = 1;
  string phone = 2;
  string addr = 3;
  bool isDefault = 4;
  int32 userId = 5;
  // 验证码
  string code = 6;
  int32 id = 7;
}


message AddressLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated AddressItem lists = 2;
}
