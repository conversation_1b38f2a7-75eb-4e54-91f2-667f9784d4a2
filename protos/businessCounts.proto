syntax = "proto3";

package Protobuf.BusinessCounts;
option php_metadata_namespace = "GPBMetadata\\Common";


// 商家统计数据响应
message BusinessCountsProto {
  int32 shelf_count = 1;                    // 上架产品数量
  int32 sold_product_count = 2;             // 销售产品数量
  double total_sales = 3;                   // 销售额
  double gross_profit = 4;                  // 销售毛利
  double profit_margin = 5;                 // 毛利率(%)
  int32 customer_count = 6;                 // 客户数
  int32 order_count = 7;                    // 订单数量
  double average_order_value = 8;           // 客单价
  double withdrawable_amount = 9;           // 可提现金额
  double locked_amount = 10;                // 冻结金额
  double refund_amount = 11;                // 已退款金额
}

