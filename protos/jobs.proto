syntax = "proto3";
import "Datas/User.proto";
import "companies.proto";

//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Jobs;
option php_metadata_namespace = "GPBMetadata\\Common";

enum JobType{
  FullTime = 0;// 全职
  PartTime = 1;// 兼职
}

// 用户附件简历
message ResumeItem{
  int32 id = 1;
  int32 userId = 2;
  string url = 3;
  string name = 5;
  int32 size = 6;
  Protobuf.Datas.User.UserProto user = 4;
}

// 用户附件简历列表
message ResumeLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated ResumeItem lists = 3;
}

// 职位类型
message JobsTypeItem{
  int32 id = 1;
  string name = 2;
}
// 职位类型列表
message JobsTypeLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated JobsTypeItem lists = 3;
}

// 职位级别
message JobsLevelItem{
  int32 id = 1;
  string name = 2;
}

// 职位级别列表
message JobsLevelLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated JobsLevelItem lists = 3;
}

message JobsListsRequest {
  int32 page = 1;
  int32 size = 2;
  string keyword = 3;
  string province = 4;
  string city = 5;
  int32 companiesId = 6;
  string sortField = 7;
  string sortType = 8;
  int32 userId = 9;
  int32 typeId = 10;
}

// 职位信息
message JobsItem{
  int32 id = 1;
  int32 companyId = 2;
  Protobuf.Companies.CompaniesItem company = 3;
  string title = 4;
  string description = 5;
  string ask = 6;
  string salaryRange = 7;
  int32 typeId = 8;
  JobsTypeItem type = 9;
  int32 levelId = 10;
  JobsLevelItem level = 11;
  string createdAt = 12;
  bool isCollected = 13; //是否收藏
  repeated TagItem tags = 14;
  int32 showNum = 15;
  bool hasApply = 17; // 是否已经申请过该职位
}

// 职位列表
message JobsLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated JobsItem lists = 3;
}

// 职位申请信息
message JobsApplyItem{
  int32 id = 1;
  int32 jobId = 2;
  JobsItem jobs = 3;
  int32 userId = 4;
  Protobuf.Datas.User.UserProto  user = 5;
  string applicationTime = 6;
  string status = 7; //面试结果
  string interviewTime = 8; // 面试时间
  string feedback = 9; // 面试反馈
  int32 interviewUserId = 10; // 面试官
  Protobuf.Datas.User.UserProto   interviewUser = 11; // 面试官
}

// 职位申请列表
message JobsApplyLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated JobsApplyItem lists = 3;
}


// 用户收藏职位项
message JobsCollectItem{
  int32 id = 1;
  int32 userId = 2;
  Protobuf.Datas.User.UserProto user = 3;
  int32 jobsId = 4;
  JobsItem jobs = 5;
  string time = 6;
}

// 用户收藏职位列表
message JobsCollectLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated JobsCollectItem lists = 3;
}

message TagItem{
  int32 id = 1;
  string name = 2;
}

message JobsTagLists{
  int32 total = 1;
  int32 totalPage = 2;
  repeated TagItem lists = 3;
}

// 按照职位类型统计职位数量
message CountByTypeItemMessage{
  int32 count = 1;
  JobsTypeItem type = 2;
  JobsItem jobs = 3;
}
// 按照职位类型统计职位数量列表
message CountByTypeListsMessage{
  int32 total = 1;
  int32 totalPage = 2;
  repeated CountByTypeItemMessage lists = 3;
}

// 用户查看职位详情
message JobsUserSeeItemMessage{
  int32 id = 1;
  int32 userId = 2;
  int32 jobId = 3;
  int32 num = 4;
  string firstTime = 5;
  string lastTime = 6;
  JobsItem jobs = 7;
}

// 用户查看职位列表
message JobsUserSeeListsMessage{
  int32 total = 1;
  int32 totalPage = 2;
  repeated JobsUserSeeItemMessage lists = 3;
}

// 用户统计数量
message UserCounts{
  int32 apply = 1;  // 申请数量
  int32 interview = 2; // 面试数量
  int32 see = 3; // 查看职位数量
}

// 用户的求职意向
message Intention{
  int32 id = 1; // 唯一标识
  int32 userId = 2;// 用户ID
  JobType  type = 3;// 职位类型，兼职或者全职
  string city = 4; // 期望城市
  repeated int32 typeIds = 5;// 职位名称ID列表
  string salaryRange = 6 ; // 薪资范围
}