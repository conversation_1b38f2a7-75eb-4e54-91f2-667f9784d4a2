syntax = "proto3";
import "Datas/User.proto";
//  cd .\src\protos\
//  D:\download\protoc-26.1-win64\bin\protoc  --php_out=../   *.proto

package Protobuf.Speciality;
option php_metadata_namespace = "GPBMetadata\\Common";
// 特长分类
message  SpecialityType{
  int32 id = 1;
  string name = 2;
}
// 特长分类列表
message SpecialityTypeLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated SpecialityType lists = 2;
}

// 特长信息项
message SpecialityItem{
  int32 id = 1;
  string name = 2;
  SpecialityType type = 3;
}

// 特长信息列表
message SpecialityLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated SpecialityItem lists = 2;
}

// 用户特长项
message UserSpecialityItem{
  int32 id = 1;
  int32 userId = 2;
  Protobuf.Datas.User.UserProto user = 3;
  int32 specialityId = 4;
  SpecialityItem speciality = 5;
}

// 用户特长列表
message UserSpecialityLists{
  int32 total = 1;
  int32 totalPage = 3;
  repeated UserSpecialityItem lists = 2;
}