---
description: 
globs: 
alwaysApply: false
---
# 牙谷电商 API 项目概览

## 项目简介
这是一个基于 Swoole 的高性能 PHP 电商 API 项目，为"牙谷"和"口小白" APP 提供后端服务。

## 核心技术栈
- **框架**: Swoole WebSocket Server
- **PHP版本**: >= 8.4
- **主要依赖**: 
  - Swoole: 高性能异步网络框架
  - Twig: 模板引擎
  - Guzzle: HTTP 客户端
  - 支付宝支付: yansongda/pay
  - 阿里云服务: 短信、语音等

## 项目结构
- **主入口**: [bin/start.php](mdc:bin/start.php) - Swoole 服务器启动文件
- **应用目录**: [App/](mdc:App) - 主要业务逻辑
- **配置文件**: [composer.json](mdc:composer.json) - 项目依赖和自动加载配置
- **公共资源**: [public/](mdc:public) - 静态文件目录
- **运行时**: [runtime/](mdc:runtime) - 日志和缓存文件
- **前端**: [uni-app/](mdc:uni-app) - 跨平台前端应用

## 关键特性
- WebSocket 实时通信
- 高并发处理能力
- 支持多种支付方式
- 集成阿里云服务
- Docker 容器化部署

## 开发环境
- 开发启动: [bin/dev.php](mdc:bin/dev.php)
- 重载服务: [bin/reload.php](mdc:bin/reload.php)
- Docker 配置: [docker-compose.yml](mdc:docker-compose.yml)
