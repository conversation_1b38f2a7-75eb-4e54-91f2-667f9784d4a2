---
description: 
globs: 
alwaysApply: false
---
# Swoole 服务器开发指南

## 服务器启动和配置

### 主启动文件
- **入口文件**: [bin/start.php](mdc:bin/start.php)
- **开发模式**: [bin/dev.php](mdc:bin/dev.php)
- **重载脚本**: [bin/reload.php](mdc:bin/reload.php)

### 服务器事件处理
Swoole 服务器事件位于 `Swlib/ServerEvents/` 目录下：

#### WebSocket 事件
- **连接打开**: `OnOpenEvent` - 处理 WebSocket 连接建立
- **消息接收**: `OnMessageEvent` - 处理 WebSocket 消息
- **连接关闭**: `OnCloseEvent` - 处理连接断开

#### HTTP 事件
- **HTTP 请求**: `OnRequestEvent` - 处理 HTTP 请求

#### 服务器生命周期事件
- **服务器启动**: `OnStartEvent` - 服务器启动时执行
- **Worker 启动**: `OnWorkerStartEvent` - Worker 进程启动时执行
- **Worker 停止**: `OnWorkerStopEvent` - Worker 进程停止时执行

#### 任务处理事件
- **任务执行**: `OnTaskEvent` - 异步任务处理
- **任务完成**: `OnFinishEvent` - 任务完成回调

## 服务器配置

### 核心配置项
```php
$config = [
    'worker_num' => ConfigEnum::WORKER_NUM,        // Worker 进程数
    'task_worker_num' => ConfigEnum::TASK_WORKER_NUM, // Task 进程数
    'task_enable_coroutine' => true,               // 启用任务协程
    'enable_coroutine' => true,                    // 启用协程
    'max_request' => 1024,                         // 最大请求数
    'heartbeat_idle_time' => 600,                  // 心跳检测时间
    'reload_async' => true,                        // 异步重载
];
```

### 开发环境配置
- **静态文件服务**: `enable_static_handler = true`
- **文档根目录**: [public/](mdc:public) 目录
- **SSL 配置**: 通过 `generateDevSSL()` 方法自动配置

## 自定义进程
- **进程管理**: `App\Process\Process` 类负责添加自定义进程
- **用途**: 定时任务、队列消费、数据同步等

## 开发调试
1. 使用 `bin/dev.php` 启动开发模式
2. 通过 `bin/reload.php` 热重载代码
3. 日志文件位于 `runtime/log/server_error.log`

## 部署注意事项
1. 生产环境设置 `daemonize = true`
2. 合理配置 Worker 进程数
3. 监控内存使用情况
4. 定期重启服务释放内存
